const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError } = require('apollo-server-lambda');
const knex = require('knex');
const { validateFormula } = require('../common/formulaValidation');

// resolver definition
const resolvers = {
    Query: {
        // function to validate salary formula
        validateSalaryFormula: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Input validation
                if (!args.formula || args.formula.trim().length === 0) {
                    return {
                        success: false,
                        isValid: false,
                        errorCode: 'IVE0721',
                        message: commonLib.func.getError('', 'IVE0721').message,
                        errors: ['IVE0721']
                    };
                }

                // Validate formula using the same validation logic as backend
                const formulaValidationResult = await validateFormula(organizationDbConnection, args.formula);

                if (formulaValidationResult.isValid) {
                    return {
                        success: true,
                        isValid: true,
                        errorCode: null,
                        message: 'Formula is valid',
                        errors: []
                    };
                } else {
                    const firstError = formulaValidationResult.errors[0];
                    return {
                        success: true,
                        isValid: false,
                        errorCode: firstError,
                        message: commonLib.func.getError('', firstError).message,
                        errors: formulaValidationResult.errors
                    };
                }

            } catch (error) {
                console.error('Error in validateSalaryFormula:', error);
                throw new ApolloError(
                    commonLib.func.getError('', 'PST0001').message,
                    'PST0001'
                );
            } finally {
                // Close database connection if it was opened
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

exports.resolvers = resolvers;
