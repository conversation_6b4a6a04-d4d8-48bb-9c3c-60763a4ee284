// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError } = require('apollo-server-lambda');
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const moment = require('moment-timezone');

// resolver definition
const resolvers = {
    Query: {
        // function to list adhoc allowance types
        listAdhocAllowanceTypes: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside listAdhocAllowanceTypes function');
                const loggedInEmpId = context.logInEmpId;
                const { formId, status, isCandidateForm } = args;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // check access right based on employeeid
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    formId
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100';
                }

                // get adhoc allowance types data
                const adhocAllowanceTypesData = await getAdhocAllowanceTypes(organizationDbConnection, status, isCandidateForm);

                // return response
                return {
                    errorCode: '',
                    message: 'Adhoc allowance types listed successfully.',
                    success: true,
                    adhocAllowanceTypes: JSON.stringify(adhocAllowanceTypesData)
                };
            }
            catch (mainCatchError) {
                console.log('Error in listAdhocAllowanceTypes function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0043');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        },

        // function to get single adhoc allowance type
        getAdhocAllowanceType: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside getAdhocAllowanceType function');
                const loggedInEmpId = context.logInEmpId;
                const { adhocAllowanceTypeId, formId } = args;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // check access right based on employeeid
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    formId
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100';
                }

                // get single adhoc allowance type data
                const adhocAllowanceTypeData = await getSingleAdhocAllowanceType(organizationDbConnection, adhocAllowanceTypeId);

                if (!adhocAllowanceTypeData) {
                    throw 'IVE0757';
                }

                // return response
                return {
                    errorCode: '',
                    message: 'Adhoc allowance type retrieved successfully.',
                    success: true,
                    adhocAllowanceType: JSON.stringify(adhocAllowanceTypeData)
                };
            }
            catch (mainCatchError) {
                console.log('Error in getAdhocAllowanceType function main catch block', mainCatchError);
                
                if (mainCatchError === 'IVE0757') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new ApolloError(errResult.message1, mainCatchError);
                }
                
                const errResult = commonLib.func.getError(mainCatchError, 'PST0043');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

// function to get adhoc allowance types
async function getAdhocAllowanceTypes(organizationDbConnection, status, isCandidateForm) {
    try {
        return await organizationDbConnection.transaction(async (trx) => {
            // Build query for adhoc allowance types
            let adhocAllowanceTypesQuery = organizationDbConnection(ehrTables.onetimeEarningTypes + ' as AAT')
                .select(
                    'AAT.One_Time_Earning_Type_Id',
                    'AAT.Title',
                    'AAT.Name_In_Payslip',
                    'AAT.Calculation_Type',
                    'AAT.Amount',
                    'AAT.Custom_Formula',
                    'AAT.Override_Custom_Formula',
                    'AAT.Commitment_Period',
                    'AAT.Commitment_Period_Months',
                    'AAT.Clawback_Type',
                    'AAT.Clawback_Formula',
                    'AAT.Clawback_Amount',
                    'AAT.Default_Payout_Month_From',
                    'AAT.Default_Payout_Duration_Months',
                    'AAT.Auto_Approval',
                    'AAT.Tax_Inclusion',
                    'AAT.Status',
                    'AAT.Description',
                    'AAT.Added_On',
                    'AAT.Updated_On',
                    'AAT.Added_By',
                    'AAT.Updated_By',
                    // Employee name concatenations
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI_Added.Emp_First_Name, EPI_Added.Emp_Middle_Name, EPI_Added.Emp_Last_Name) as Added_By_Name"),
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI_Updated.Emp_First_Name, EPI_Updated.Emp_Middle_Name, EPI_Updated.Emp_Last_Name) as Updated_By_Name")
                )
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI_Added', 'EPI_Added.Employee_Id', 'AAT.Added_By')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI_Updated', 'EPI_Updated.Employee_Id', 'AAT.Updated_By')
                .orderBy('AAT.Title', 'asc')
                .transacting(trx);

            // Apply status filter if provided
            if (status && (status.toLowerCase() === 'active' || status.toLowerCase() === 'inactive')) {
               adhocAllowanceTypesQuery = adhocAllowanceTypesQuery.whereRaw('LOWER(AAT.Status) = ?', [status.toLowerCase()]);
            }

            // Apply candidate form filter if provided
            if (isCandidateForm === true) {
                adhocAllowanceTypesQuery = adhocAllowanceTypesQuery.whereRaw('LOWER(AAT.Default_Payout_Month_From) = ?', ['date of joining']);
            }

            const adhocAllowanceTypes = await adhocAllowanceTypesQuery;

            // Get all adhoc allowance type IDs
            const adhocAllowanceTypeIds = adhocAllowanceTypes.map(aat => aat.One_Time_Earning_Type_Id);

            // Get benefit associations from table (following pattern from listAllowanceType.js)
            let benefitAssociations = {};
            if (adhocAllowanceTypeIds.length > 0) {
                // Special Form IDs that need Form_Name from ehr_forms table (same as getBenefitFormsByFormId)
                const specialFormIds = [280, 281, 110, 90, 126, 52, 58];

                const benefits = await organizationDbConnection(ehrTables.adhocAllowanceBenefitAssociation + ' as AABA')
                    .select('AABA.Adhoc_Allowance_Id', 'BF.Form_Id', 'BF.Display_Form_Name', 'EF.Form_Name as EHR_Form_Name')
                    .innerJoin(ehrTables.benefitForms + ' as BF', 'BF.Form_Id', 'AABA.Form_Id')
                    .leftJoin(ehrTables.ehrForms + ' as EF', 'EF.Form_Id', 'BF.Form_Id')
                    .whereIn('AABA.Adhoc_Allowance_Id', adhocAllowanceTypeIds)
                    .transacting(trx);

                benefits.forEach(benefit => {
                    if (!benefitAssociations[benefit.Adhoc_Allowance_Id]) {
                        benefitAssociations[benefit.Adhoc_Allowance_Id] = {
                            names: [],
                            ids: []
                        };
                    }
                    // Use EHR_Form_Name for special form IDs, otherwise use Display_Form_Name
                    const formName = specialFormIds.includes(benefit.Form_Id) && benefit.EHR_Form_Name
                        ? benefit.EHR_Form_Name
                        : benefit.Display_Form_Name;

                    benefitAssociations[benefit.Adhoc_Allowance_Id].names.push(formName);
                    benefitAssociations[benefit.Adhoc_Allowance_Id].ids.push(benefit.Form_Id);
                });
            }

            // Add benefit associations to each adhoc allowance type
            adhocAllowanceTypes.forEach(aat => {
                const benefits = benefitAssociations[aat.One_Time_Earning_Type_Id];
                aat.Benefits_Name = benefits ? benefits.names : [];
                aat.Benefits_Association = benefits ? benefits.ids : [];
            });

            return adhocAllowanceTypes;
        });
    } catch (error) {
        console.log('Error in getAdhocAllowanceTypes function:', error);
        throw error;
    }
}

// function to get single adhoc allowance type
async function getSingleAdhocAllowanceType(organizationDbConnection, adhocAllowanceTypeId) {
    try {
        const adhocAllowanceType = await organizationDbConnection(ehrTables.onetimeEarningTypes + ' as AAT')
            .select(
                'AAT.*',
                organizationDbConnection.raw("CONCAT_WS(' ', EPI_Added.Emp_First_Name, EPI_Added.Emp_Middle_Name, EPI_Added.Emp_Last_Name) as Added_By_Name"),
                organizationDbConnection.raw("CONCAT_WS(' ', EPI_Updated.Emp_First_Name, EPI_Updated.Emp_Middle_Name, EPI_Updated.Emp_Last_Name) as Updated_By_Name")
            )
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI_Added', 'EPI_Added.Employee_Id', 'AAT.Added_By')
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI_Updated', 'EPI_Updated.Employee_Id', 'AAT.Updated_By')
            .where('AAT.One_Time_Earning_Type_Id', adhocAllowanceTypeId)
            .first();

        if (!adhocAllowanceType) {
            return null;
        }

        // Get benefit associations from table
        const specialFormIds = [280, 281, 110, 90, 126, 52, 58];

        const benefits = await organizationDbConnection(ehrTables.adhocAllowanceBenefitAssociation + ' as AABA')
            .select('AABA.Form_Id', 'BF.Display_Form_Name', 'EF.Form_Name as EHR_Form_Name')
            .innerJoin(ehrTables.benefitForms + ' as BF', 'BF.Form_Id', 'AABA.Form_Id')
            .leftJoin(ehrTables.ehrForms + ' as EF', 'EF.Form_Id', 'BF.Form_Id')
            .where('AABA.Adhoc_Allowance_Id', adhocAllowanceTypeId);

        adhocAllowanceType.Benefits_Name = [];
        adhocAllowanceType.Benefits_Association = [];

        benefits.forEach(benefit => {
            // Use EHR_Form_Name for special form IDs, otherwise use Display_Form_Name
            const formName = specialFormIds.includes(benefit.Form_Id) && benefit.EHR_Form_Name
                ? benefit.EHR_Form_Name
                : benefit.Display_Form_Name;

            adhocAllowanceType.Benefits_Name.push(formName);
            adhocAllowanceType.Benefits_Association.push(benefit.Form_Id);
        });

        return adhocAllowanceType;
    } catch (error) {
        console.log('Error in getSingleAdhocAllowanceType function:', error);
        throw error;
    }
}

module.exports.resolvers = resolvers;

