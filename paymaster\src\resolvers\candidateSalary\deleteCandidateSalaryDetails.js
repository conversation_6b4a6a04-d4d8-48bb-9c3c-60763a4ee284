// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const { validateCandidateStatus, revertCandidateStatusOnDelete } = require('../../common/commonfunctions');

const deleteCandidateSalaryDetails = async (parent, args, context, info) => {
  console.log('Inside deleteCandidateSalaryDetails function');

  let organizationDbConnection;

  try {
    const loginEmployeeId = context.logInEmpId;

    // Get database connection
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Check access rights using formId from input
    const formIdToCheck = args.formId;
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      '',
      'UI',
      false,
      formIdToCheck
    );

    // Check delete access
    if (Object.keys(checkRights).length === 0 || checkRights.Role_Delete !== 1) {
      throw '_DB0103'; // No delete access
    }

    // Validate candidate status for all candidate IDs (DELETE operation)
    //there is no bulk edit
    if (args.candidateIds && args.candidateIds.length > 0) {
      for (const candidateId of args.candidateIds) {
        await validateCandidateStatus(organizationDbConnection, candidateId, 'delete');
      }
    }


    return await organizationDbConnection.transaction(async (trx) => {
      // Delete using whereIn for bulk operation
      const deletedRecords = await deleteByCandidateIds(organizationDbConnection, args.candidateIds, trx);

      if (deletedRecords === 0) {
        return {
          errorCode: "PFF0027",
          message: "No candidate salary records found to delete"
        };
      }

      // Revert candidate status from "Hired with compensation" to "Hired" if applicable
      await revertCandidateStatusOnDelete(organizationDbConnection, args.candidateIds, trx);

      return {
        errorCode: "",
        message: `Candidate salary details deleted successfully. ${deletedRecords} record(s) removed.`
      };
    });

  } catch (error) {
    console.error('Error in deleteCandidateSalaryDetails:', error);
    const errResult = commonLib.func.getError(error, 'PFF0027');
    throw new ApolloError(errResult.message, errResult.code);
  } finally {
    if (organizationDbConnection) {
      organizationDbConnection.destroy();
    }
  }
};



/**
 * Delete candidate salary records by candidate IDs using whereIn
 * Handles both single and bulk delete operations efficiently
 */
async function deleteByCandidateIds(organizationDbConnection, candidateIds, trx) {
  // First check if any records exist
  try {
  const existingRecords = await organizationDbConnection(ehrTables.candidateSalaryDetails)
    .select('Candidate_Id')
    .whereIn('Candidate_Id', candidateIds)
    .transacting(trx);

  if (existingRecords.length === 0) {
    return 0; // No records to delete
  }

  const existingCandidateIds = existingRecords.map(record => record.Candidate_Id);

  // Delete allowance components using whereIn
  await organizationDbConnection(ehrTables.candidateSalaryAllowance)
    .whereIn('Candidate_Id', existingCandidateIds)
    .del()
    .transacting(trx);

  // Delete retiral components using whereIn
  await organizationDbConnection(ehrTables.candidateSalaryRetirals)
    .whereIn('Candidate_Id', existingCandidateIds)
    .del()
    .transacting(trx);

  // Delete main salary records using whereIn
  const deletedCount = await organizationDbConnection(ehrTables.candidateSalaryDetails)
    .whereIn('Candidate_Id', existingCandidateIds)
    .del()
    .transacting(trx);

  return deletedCount;
  } catch (error) {
    console.error('Error in deleteByCandidateIds:', error);
    throw error;
  }
}

// Export resolvers
const resolvers = {
  Mutation: {
    deleteCandidateSalaryDetails
  }
};

module.exports = { resolvers };
