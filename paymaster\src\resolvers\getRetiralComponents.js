// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// Organization database connection
const knex = require('knex');
// require common constant files
const constants = require('../common/appconstants');
// require table alias
const {ehrTables} = require('../common/tablealias');

// variable declarations
let errResult = {};
let appmanagerDbConnection = '';

// resolver definition
const resolvers = {
    Query: {
        // function to get retiral components
        getRetiralComponents: async (parent, args, context, info) => {
            try{
                console.log('Inside getRetiralComponents function');

                // variable declarations
                let formIdArray=constants.defaultValues.retiralFormIds;

                // make appmanager database connection
                appmanagerDbConnection = knex(context.connection.AppManagerDb);
                
                return(
                    appmanagerDbConnection
                    .transaction(function(trx){
                        return (
                            // Get retirals formId,form name,custom form name from forms table
                            appmanagerDbConnection
                            .select('F.Form_Id as formId','F.Form_Name as formName','CF.New_Form_Name as customFormName')
                            .from(ehrTables.forms + ' as F')
                            .leftJoin(ehrTables.customizationForms +' as CF',"CF.Form_Id", "F.Form_Id")
                            .whereIn('F.Form_Id', formIdArray)
                            .transacting(trx)
                            .then(async (formsData) => {
                                // check whether data exist or not
                                if(formsData.length>0)
                                {
                                    // if custom formname exist then return it as form name
                                    for(let j in formsData){
                                        if(formsData[j].customFormName)
                                        {
                                            formsData[j]['formName']=formsData[j].customFormName
                                        }
                                    }
                                    return { errorCode: '', message: 'Retirals retrieved successfully.' , retirals:formsData};
                                }
                                else{
                                    return { errorCode: '', message: 'No retirals found.' , retirals:[]};
                                }
                            })
                        )
                    })
                    .then(function (result) {
                        return result;
                    })
                    //catch db-connectivity errors
                    .catch(function (catchError) {
                        console.log('Error in getRetiralComponents function .catch() block', catchError);
                        errResult = commonLib.func.getError(catchError, 'PST0005');
                        throw new ApolloError(errResult.message,errResult.code)
                    })
                    /**close db connection */
                    .finally(() => {
                        appmanagerDbConnection.destroy();
                    })
                );
            }
            catch (mainCatchError){
                console.log('Error in getRetiralComponents function main catch block',mainCatchError);
                // destroy database connection
                (appmanagerDbConnection)?appmanagerDbConnection.destroy():'';
                errResult = commonLib.func.getError(mainCatchError, 'PST0006');
                throw new ApolloError(errResult.message,errResult.code)
            }
        }
    }
};

exports.resolvers = resolvers;
