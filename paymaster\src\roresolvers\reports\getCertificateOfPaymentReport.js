//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const { ehrTables } = require('../../common/tablealias');


//BIR-2316 Certificate of Compensation Payment/Tax Withheld
module.exports.getCertificateOfPaymentReport = async (parent, args, context, info) => {

    let organizationDbConnection;
    try{
        console.log("Inside getCertificateOfPaymentReport() function ", args)
        organizationDbConnection= knex(context.connection.OrganizationDb);

        const[employeeDetails, orgDetails, taxConfiguration, companyLocation, taxDetails]  =  await Promise.all([
                organizationDbConnection('emp_personal_info as EMP')
                .select('EJ.Employee_Id','EMP.Emp_First_Name', 'EMP.Emp_Middle_Name', 'EMP.Emp_Last_Name', 'EMP.DOB',
                    'CD.pApartment_Name', 'CD.pStreet_Name', 'CD.pCity', 'CD.pState', 'CD.pCountry', 'CD.pPincode',
                    'CD.cApartment_Name', 'CD.cStreet_Name', 'CD.cCity', 'CD.cState', 'CD.cCountry', 'CD.cPincode', 'CD.Mobile_No', 'CD.Mobile_No_Country_Code',)
                .leftJoin('emp_job as EJ', 'EJ.Employee_Id', 'EMP.Employee_Id')
                .leftJoin('contact_details as CD', 'CD.Employee_Id', 'EMP.Employee_Id')
                .where('EMP.Employee_Id', args.employeeId),

                organizationDbConnection(ehrTables.orgDetails).select('Org_Name').limit(1),

                organizationDbConnection(ehrTables.taxConfiguration).select('TAN').limit(1),

                organizationDbConnection(ehrTables.location+" as L")
                    .select('L.Phone','L.Street1', 'L.Street2', 'city.City_Name', 'state.State_Name', 'country.Country_Name', 'L.Pincode')
                    .leftJoin(ehrTables.country, 'L.Country_Code','country.Country_Code')
                    .leftJoin(ehrTables.state, 'L.State_Id','state.State_Id')
                    .leftJoin(ehrTables.city, 'L.City_Id', 'city.City_Id')
                    .where('L.Location_Type','MainBranch'),

                employeeTaxDetails(args, context.Org_Code)
            ]);


        let empTaxDetails = taxDetails?.empTaxDetails?.Tax_Calculation_Details;

        let grossEarnings = empTaxDetails && empTaxDetails.Gross_Earnings ?  empTaxDetails.Gross_Earnings : [];

        const result = {
            assessmentYear: args.assessmentYear, //1
            taxPayerIdentificationNo: employeeDetails[0]?.Employee_Id, //3
            firstName: employeeDetails[0]?.Emp_First_Name, //4
            lastName: employeeDetails[0]?.Emp_Last_Name, //4
            middleName: employeeDetails[0]?.Emp_Middle_Name, //4
            dateofBirth: employeeDetails[0]?.DOB, //7
            mobileNo: employeeDetails[0]?.Mobile_No, //8
            mobileCountryCode: employeeDetails[0]?.Mobile_No_Country_Code,
            //6 Employee Permanent Address
            permenantAddress: {
                apartmentName: employeeDetails[0]?.pApartment_Name,
                streetName: employeeDetails[0]?.pStreet_Name,
                city: employeeDetails[0]?.pCity,
                state: employeeDetails[0]?.pState,
                country: employeeDetails[0]?.pCountry,
                pincode: employeeDetails[0]?.pPincode //6A
            },
            //6B Employee Current Address
            currentAddress: {
                apartmentName: employeeDetails[0]?.cApartment_Name,
                streetName: employeeDetails[0]?.cStreet_Name,
                city: employeeDetails[0]?.cCity,
                state: employeeDetails[0]?.cState,
                country: employeeDetails[0]?.cCountry,
                pincode: employeeDetails[0]?.cPincode //6C
            },
            employerIdentificationNo: taxConfiguration[0]?.TAN, //15
            employerName: orgDetails[0]?.Org_Name, //16
            //17 and 17A
            maillingAddress: {
                street1:  companyLocation[0]?.Street1,
                street2: companyLocation[0]?.Street2,
                cityName: companyLocation[0]?.City_Name,
                stateName: companyLocation[0]?.State_Name,
                countryName: companyLocation[0]?.Country_Name,
                pincode: companyLocation[0]?.Pincode,
                phone: companyLocation[0]?.Phone
            },
            qualifiedDependent: 'No', //9A
            totalExemptions: 0, //26
            premiumPaidHealth: 0,  //27
            taxDue: 0, //29
            bPreviousEmployer: 0, // 
            salariesAndCompensation: 0, //40
            holidayPayMWE:  grossEarnings && grossEarnings.length  ? grossEarnings.filter(s=> s.Earnings_Name === "Holiday Pay (MWE)")[0]?.Total : 0, //33
            overtimePayMWE:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Overtime Pay (MWE)")[0]?.Total : 0, //34
            hazardPayMWE:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Hazard Pay (MWE)")[0]?.Total : 0, //36
            monthPay:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "13th Month Pay")[0]?.Total : 0, //37
            deMinimisBenefits:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "De Minimis Benefits")[0]?.Total : 0, //38
            basicSalary:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Basic Salary")[0]?.Total : 0, //42
            representation:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Representation")[0]?.Total : 0, //43
            transportaion:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Transportaion")[0]?.Total : 0, //44
            costLivingAllowance:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Cost of Living Allowance")[0]?.Total : 0, //45
            fixedHousingAllowance:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Fixed Housing Allowance")[0]?.Total : 0, //46
            commission:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Commission")[0]?.Total : 0, //48
            profitSharing:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Profit Sharing")[0]?.Total : 0, //49
            feesIncludingDirector:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Fees Including Director's Fees")[0]?.Total : 0, //50
            taxableMonthPay:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Taxable 13th Month Pay")[0]?.Total : 0, //51
            hazardPay:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Hazard Pay")[0]?.Total : 0, //52
            overtimePay:  grossEarnings && grossEarnings.length ? grossEarnings.filter(s=> s.Earnings_Name === "Overtime Pay")[0]?.Total : 0, //53
            presentEmployerGrossIncome: empTaxDetails?.Gross_Earnings_Tot_Income, //21
            totalNonTaxable: empTaxDetails?.Gross_Earnings_Tot_Income, //22
            presentEmployerTaxableIncome : empTaxDetails?.Gross_Total_Income, //23
            previousEmployerTaxableIncome: empTaxDetails?.Tds_History_Tot_Income, //24
            otherBenefitsGrossTaxableIncome: empTaxDetails?.Annual_Taxable_Income, //25
        }    

        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return result;
    } catch(err){
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.error('Error in getCertificateOfPaymentReport function main catch block.', err);
        let errResult = commonLib.func.getError(err, 'PFF0016');
        throw new ApolloError(errResult.message, errResult.code)
    }
}

async function employeeTaxDetails(args, orgCode) {
   
    try {
        console.log("Inside employeeTaxDetails() function")
        
        const axios = require('axios');

        let url = process.env.employeeTaxDetailUrl;

        url = url.replace("{orgCode}", orgCode);

        let data = JSON.stringify({
            "employeeId": args.employeeId,
            "assessmentYear": args.assessmentYear
        });
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: url,
            headers: {
                'Content-Type': 'application/json',
                'Cookie': 'PHPSESSID=ba6pc7th42gbkvp0vn992ug0uc'
            },
            data : data
        };
        return axios.request(config)
        .then((response) => {
            console.log(JSON.stringify(response.data));
            return response.data;
        }).catch((error) => {
            console.error("Error: while retrieve employeeTaxDetails api  block", error);
            return  { success: false, empTaxDetails: {}}
        });
    } catch(error){
        console.error("Error: while retrieve employeeTaxDetails main catch block ", error);
        return  { success: false, empTaxDetails: {}}
    }
}