const moment = require('moment');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

/**
 * Get last salary date for a given month in Non-Calendar Month paycycle
 * @param {number} paycycleEndDay - Paycycle end day
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {string} - Last salary date in YYYY-MM-DD format
 */
function getLastSalaryDateForMonth(paycycleEndDay, month, year) {
    try {
        // Form the date using the paycycle end day, month and year
        const lastSalaryDate = moment(`${paycycleEndDay}-${month}-${year}`, 'DD-MM-YYYY');

        // Form the end date of a month using the given month and year
        const monthEndDate = moment(`01-${month}-${year}`, 'DD-MM-YYYY').endOf('month');

        // If last salary date falls on next month
        if (lastSalaryDate.isAfter(monthEndDate)) {
            // last day of the given month
            return monthEndDate.format('YYYY-MM-DD');
        }

        return lastSalaryDate.format('YYYY-MM-DD');
    } catch (error) {
        console.error('Error in getLastSalaryDateForMonth:', error);
        throw error;
    }
}
async function getSalaryDateRange(payslipMonth, payslipYr, punchInDate, formId = 0, orgCode, organizationDbConnection, organizationDetails) {
    try {
        // Convert punchInDate to timestamp if it's a string
        let punchInTimestamp;
        if (typeof punchInDate === 'string') {
            punchInTimestamp = moment(punchInDate).unix();
        } else {
            punchInTimestamp = punchInDate;
        }

        // Get organization details
        const paymentDay = organizationDetails.Payment_Day;
        const cutoffDay = organizationDetails.Cutoff_Day;
        const considerCutoffDaysForAttendanceAndTimeoff = organizationDetails.Consider_Cutoff_Days_For_Attendance_And_Timeoff?.toLowerCase() || 'no';
        const paycycle = organizationDetails.Paycycle;
        

        if (paycycle && paycycle.toLowerCase() === "non-calendar month") {
            const paycycleEndDay = organizationDetails.Paycycle_End_Day;

            // Call the function to get the last salary date
            let lastSalaryDate = getLastSalaryDateForMonth(paycycleEndDay, payslipMonth, payslipYr);
            
            // Check if punch in date is greater than last salary date
            if (punchInTimestamp > moment(lastSalaryDate).unix()) {
                if (payslipMonth == 12) {
                    payslipMonth = 1;
                    payslipYr++;
                } else {
                    payslipMonth++;
                }

                lastSalaryDate = getLastSalaryDateForMonth(paycycleEndDay, payslipMonth, payslipYr);
            }

            // Calculate previous month
            let previousPayslipMonth, previousPayslipYear;
            if (payslipMonth == 1) { // If given month is jan
                previousPayslipMonth = 12;
                previousPayslipYear = payslipYr - 1;
            } else {
                previousPayslipMonth = payslipMonth - 1;
                previousPayslipYear = payslipYr;
            }

            // Call the function to get the previous month last salary date
            const prevMonthLastSalaryDate = getLastSalaryDateForMonth(paycycleEndDay, previousPayslipMonth, previousPayslipYear);

            const salaryDate = moment(prevMonthLastSalaryDate).add(1, 'day').format('YYYY-MM-DD');

            let paymentDate, nextPaymentDate;

            if (paymentDay <= 10) {
                // Payment Day Value is less than or equal to 10
                paymentDate = moment(`${paymentDay}-${payslipMonth}-${payslipYr}`, 'DD-MM-YYYY').format('YYYY-MM-DD');
                nextPaymentDate = moment(paymentDate).add(1, 'month').format('YYYY-MM-DD');
            } else {
                const lastSalaryDatePaymentDay = moment(lastSalaryDate).date();
                
                // If payment day is greater than or equal to last month salary date
                if (paymentDay >= lastSalaryDatePaymentDay) {
                    nextPaymentDate = moment(`${lastSalaryDatePaymentDay}-${payslipMonth}-${payslipYr}`, 'DD-MM-YYYY').format('YYYY-MM-DD');
                    
                    // Check if the payslip month is greater than 1
                    let paymentMonth;
                    if (payslipMonth > 1) {
                        paymentMonth = payslipMonth - 1;
                    } else {
                        paymentMonth = 12;
                        payslipYr = payslipYr - 1;
                    }
                    
                    const paymentDate1 = moment(`01-${paymentMonth}-${payslipYr}`, 'DD-MM-YYYY');
                    paymentDate = paymentDate1.endOf('month').format('YYYY-MM-DD');
                } else {
                    // Payment Day Value is Greater than 10
                    nextPaymentDate = moment(`${paymentDay}-${payslipMonth}-${payslipYr}`, 'DD-MM-YYYY').format('YYYY-MM-DD');
                    paymentDate = moment(nextPaymentDate).subtract(1, 'month').format('YYYY-MM-DD');
                }
            }

            // Temporary fix - return salary start and end date as prev cut off date and current cutoff date
            const prevcutoffDate = salaryDate;
            const cutoffDate = lastSalaryDate;

            return {
                Salary_Date: salaryDate,
                Cutoff_Date: cutoffDate,
                Prev_CutoffDate: prevcutoffDate,
                Last_SalaryDate: lastSalaryDate,
                Consider_Cutoff_Days_For_Attendance_And_Timeoff: considerCutoffDaysForAttendanceAndTimeoff
            };
        } else {
            // Calendar Month - use existing getSalaryDay function
            const paycyleDate = await commonLib.func.getSalaryDay(
                orgCode,
                organizationDbConnection,
                payslipMonth.toString(),
                payslipYr,
                formId,
                organizationDetails
            );
            
            // Handle specific form IDs for attendance and time off
            const attendanceAndTimeOffFormIds = [29, 31, 128, 163, 190, 203, 256, 8];
            if (attendanceAndTimeOffFormIds.includes(formId) && considerCutoffDaysForAttendanceAndTimeoff === 'yes') {
                if (punchInTimestamp > moment(paycyleDate.Last_SalaryDate).unix()) {
                    if (payslipMonth == 12) {
                        payslipMonth = 1;
                        payslipYr++;
                    } else {
                        payslipMonth++;
                    }
                    
                    const updatedPaycyleDate = await commonLib.func.getSalaryDay(
                        orgCode,
                        organizationDbConnection,
                        payslipMonth.toString(),
                        payslipYr,
                        formId,
                        organizationDetails
                    );
                    return updatedPaycyleDate;
                }
            }
            
            return paycyleDate;
        }
    } catch (error) {
        console.error('Error in getSalaryDateRange:', error);
        throw error;
    }
}

module.exports = {
    getSalaryDateRange,
    getLastSalaryDateForMonth
};
