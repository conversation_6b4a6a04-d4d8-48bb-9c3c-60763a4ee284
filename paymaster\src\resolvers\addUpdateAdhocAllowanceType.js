// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const { validateCommonRuleInput } = require('../common/commonfunctions');
const { validateFormula } = require('../common/formulaValidation');

// resolver definition
const resolvers = {
    Mutation: {
        // function to delete adhoc allowance type
        deleteAdhocAllowanceType: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Validate required inputs
                if (!args.adhocAllowanceTypeId) {
                    throw 'IVE0756';
                }

                if (!args.formId) {
                    throw 'IVE0658';
                }

                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );
                if (Object.keys(checkRights).length === 0 || checkRights.Role_Delete !== 1) {
                    throw '_DB0103';
                }

                // Perform deletion with all business logic checks
                const result = await deleteAdhocAllowanceTypeWithChecks(
                    organizationDbConnection,
                    args.adhocAllowanceTypeId
                );

                return result;
            }
            catch (mainCatchError) {
                console.log('Error in deleteAdhocAllowanceType function main catch block', mainCatchError);

                // Handle structured validation errors with code and validationError payload
                if ((mainCatchError && mainCatchError.code === 'IVE0000') || mainCatchError === 'IVE0000') {
                    // Extract validationError from structured error
                    const errorPayload = mainCatchError.validationError || null;

                    if (errorPayload && Object.keys(errorPayload).length > 0) {
                        // Get the first validation error message to use as main error message
                        const firstErrorCode = Object.keys(errorPayload)[0];
                        const firstErrorMessage = errorPayload[firstErrorCode];
                        throw new UserInputError(firstErrorMessage, { validationError: errorPayload });
                    } else {
                        const errResult = commonLib.func.getError('', 'IVE0000');
                        throw new UserInputError(errResult.message, { validationError: errorPayload });
                    }
                } else if (mainCatchError === 'IVE0756' || mainCatchError === 'IVE0757' ||
                    mainCatchError === 'IVE0758' || mainCatchError === 'IVE0759') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new UserInputError(errResult.message1, { errorCode: mainCatchError });
                } else if (mainCatchError === '_DB0100' || mainCatchError === '_DB0103') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new ApolloError(errResult.message, errResult.code);
                } else {
                    const errResult = commonLib.func.getError(mainCatchError, 'PST0044');
                    throw new ApolloError(errResult.message, errResult.code);
                }
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        },

        // function to add/update adhoc allowance type
        addUpdateAdhocAllowanceType: async (parent, args, context, info) => {
            let organizationDbConnection;
            let validationError = {};
            try {
                const loggedInEmpId = context.logInEmpId;
                const isEditMode = !!args.adhocAllowanceTypeId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Validate formId
                if (!args.formId) {
                    throw 'IVE0658';
                }

                // check access right based on employeeid
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );
                if (Object.keys(checkRights).length === 0) {
                    throw '_DB0104';
                }

                if (isEditMode && checkRights.Role_Update !== 1) {
                    throw '_DB0102';
                } else if (!isEditMode && checkRights.Role_Add !== 1) {
                    throw '_DB0101';
                }

                // Input validation using validateCommonRuleInput
                const fieldValidations = {};

                // Map fields to validation rules
                if (args.adhocAllowanceTypeTitle) fieldValidations.adhocAllowanceTypeTitle = 'IVE0740';
                if (args.adhocAllowanceTypeDescription) fieldValidations.adhocAllowanceTypeDescription = 'IVE0760';

                validationError = await validateCommonRuleInput(args, fieldValidations);

                // Formula validation for Custom Formula type
                if (args.calculationType && args.calculationType.toLowerCase() === 'custom formula') {
                    if (!args.customFormula || args.customFormula.trim().length === 0) {
                        validationError['IVE0743'] = commonLib.func.getError('', 'IVE0743').message1;
                    } else {
                        // Validate formula syntax and semantics
                        const formulaValidationResult = await validateFormula(organizationDbConnection, args.customFormula);
                        if (!formulaValidationResult.isValid) {
                            const firstError = formulaValidationResult.errors[0];
                            validationError[firstError] = commonLib.func.getError('', firstError).message;
                        }
                    }
                }

                // Validate clawback formula if provided
                if (args.clawbackType && args.clawbackType.toLowerCase() === 'custom formula' && args.clawbackFormula) {
                    const clawbackFormulaValidationResult = await validateFormula(organizationDbConnection, args.clawbackFormula);
                    if (!clawbackFormulaValidationResult.isValid) {
                        const firstError = clawbackFormulaValidationResult.errors[0];
                        validationError[firstError] = commonLib.func.getError('', firstError).message;
                    }
                }

                // Business logic validations
                await performBusinessValidations(organizationDbConnection, args, validationError, isEditMode);

                if (Object.keys(validationError).length > 0) {
                    throw { code: 'IVE0000', validationError };
                }

                // Process add/update operation
                const result = await processAdhocAllowanceType(organizationDbConnection, args, loggedInEmpId, isEditMode);

                // return response
                return {
                    errorCode: '',
                    message: isEditMode ? 'Adhoc allowance type updated successfully.' : 'Adhoc allowance type added successfully.',
                    success: true,
                    adhocAllowanceTypeId: result.adhocAllowanceTypeId
                };
            }
            catch (mainCatchError) {
                console.log('Error in addUpdateAdhocAllowanceType function main catch block', mainCatchError);

                // Handle structured validation errors with code and validationError payload
                if ((mainCatchError && mainCatchError.code === 'IVE0000') || mainCatchError === 'IVE0000') {
                    // Extract validationError from structured error or fall back to outer scope
                    const errorPayload = mainCatchError.validationError || validationError;

                    // Get the first validation error message to use as main error message
                    const firstErrorCode = Object.keys(errorPayload)[0];
                    const firstErrorMessage = errorPayload[firstErrorCode];

                    throw new UserInputError(firstErrorMessage, { validationError: errorPayload });
                } else if (mainCatchError === '_DB0100' || mainCatchError === '_DB0101' ||
                    mainCatchError === '_DB0102' || mainCatchError === '_DB0104') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new ApolloError(errResult.message, errResult.code);
                } else {
                    const errResult = commonLib.func.getError(mainCatchError, 'PST0042');
                    throw new ApolloError(errResult.message, errResult.code);
                }
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

// Business logic validations
async function performBusinessValidations(organizationDbConnection, args, validationError, isEditMode) {
    try {
        // Check if title already exists
        const titleExistsQuery = organizationDbConnection(ehrTables.onetimeEarningTypes)
            .where('Title', args.adhocAllowanceTypeTitle)
            .first();

        if (isEditMode) {
            titleExistsQuery.whereNot('One_Time_Earning_Type_Id', args.adhocAllowanceTypeId);
        }

        const existingTitle = await titleExistsQuery;
        if (existingTitle) {
            validationError['IVE0741'] = commonLib.func.getError('', 'IVE0741').message1;
        }

        // Validate calculation type
        if (args.calculationType && !['amount', 'custom formula'].includes(args.calculationType.toLowerCase())) {
            validationError['IVE0742'] = commonLib.func.getError('', 'IVE0742').message1;
        }

        // Validate override custom formula
        if (args.overrideCustomFormula && !['yes', 'no'].includes(args.overrideCustomFormula.toLowerCase())) {
            validationError['IVE0744'] = commonLib.func.getError('', 'IVE0744').message1;
        }

        // Validate commitment period
        if (args.commitmentPeriod && !['yes', 'no'].includes(args.commitmentPeriod.toLowerCase())) {
            validationError['IVE0745'] = commonLib.func.getError('', 'IVE0745').message1;
        }

        // Validate commitment period configuration
        if (args.commitmentPeriod && args.commitmentPeriod.toLowerCase() === 'yes') {
           if (args.commitmentPeriodMonths === undefined || args.commitmentPeriodMonths === null || args.commitmentPeriodMonths === '') {
                validationError['IVE0746'] = commonLib.func.getError('', 'IVE0746').message1;
            }
        }

        // Validate default payout month from
        if (args.defaultPayoutMonthFrom && !['date of joining', 'date of confirmation', 'current payout month', 'custom'].includes(args.defaultPayoutMonthFrom.toLowerCase())) {
            validationError['IVE0752'] = commonLib.func.getError('', 'IVE0752').message1;
        }

        // Validate auto approval
        if (args.autoApproval && !['yes', 'no'].includes(args.autoApproval.toLowerCase())) {
            validationError['IVE0753'] = commonLib.func.getError('', 'IVE0753').message1;
        }

        // Validate tax inclusion
        if (args.taxInclusion && !['taxable', 'non taxable'].includes(args.taxInclusion.toLowerCase())) {
            validationError['IVE0754'] = commonLib.func.getError('', 'IVE0754').message1;
        }

        // Validate status
        if (args.status && !['active', 'inactive'].includes(args.status.toLowerCase())) {
            validationError['IVE0755'] = commonLib.func.getError('', 'IVE0755').message1;
        }

    } catch (error) {
        console.log('Error in performBusinessValidations:', error);
        throw error;
    }
}

// Process add/update adhoc allowance type
async function processAdhocAllowanceType(organizationDbConnection, args, loggedInEmpId, isEditMode) {
    return await organizationDbConnection.transaction(async (trx) => {
        // Prepare data object
        const adhocAllowanceTypeData = {
            Title: args.adhocAllowanceTypeTitle,
            Name_In_Payslip: args.nameInPayslip || null, // Optional name in payslip (defaults to Title)
            Calculation_Type: args.calculationType || 'Amount',
            Amount: args.amount || null, // Optional amount (only used for calculation type Amount)
            Custom_Formula: args.customFormula || null,
            Override_Custom_Formula: args.overrideCustomFormula || 'No',
            Commitment_Period: args.commitmentPeriod || 'No',
            Commitment_Period_Months: args.commitmentPeriodMonths ?? null,
            Clawback_Type: args.clawbackType || null,
            Clawback_Formula: args.clawbackFormula || null,
            Clawback_Amount: args.clawbackAmount || null,
            Default_Payout_Month_From: args.defaultPayoutMonthFrom,
            Default_Payout_Duration_Months: args.defaultPayoutDurationMonths ?? null,
            Auto_Approval: args.autoApproval,
            Tax_Inclusion: args.taxInclusion,
            Status: args.status,
            Description: args.adhocAllowanceTypeDescription || null
        };

        // Set audit fields
        const currentTimestamp = new Date();
        if (isEditMode) {
            adhocAllowanceTypeData.Updated_By = loggedInEmpId;
            adhocAllowanceTypeData.Updated_On = currentTimestamp;
        } else {
            adhocAllowanceTypeData.Added_By = loggedInEmpId;
            adhocAllowanceTypeData.Added_On = currentTimestamp;
        }

        let adhocAllowanceTypeId;

        if (isEditMode) {
            // Initialize validation error accumulator for edit mode
            const validationError = {};

            // Check if there are any employee one-time earnings with Applied or Approved status
            const [employeeEarningsCount, candidateEarningsCount] = await Promise.all([
                trx(ehrTables.employeeOneTimeEarnings)
                    .where('One_Time_Earning_Type_Id', args.adhocAllowanceTypeId)
                    .whereIn('Approval_Status', ['Applied', 'Approved'])
                    .count('* as count')
                    .first(),
                trx(ehrTables.candidateOneTimeEarnings)
                    .where('One_Time_Earning_Type_Id', args.adhocAllowanceTypeId)
                    .whereIn('Approval_Status', ['Applied', 'Approved'])
                    .count('* as count')
                    .first()
            ]);

            if (employeeEarningsCount && employeeEarningsCount.count > 0) {
                validationError['IVE0806'] = commonLib.func.getError('', 'IVE0806').message;
            }

            if (candidateEarningsCount && candidateEarningsCount.count > 0) {
                validationError['IVE0808'] = commonLib.func.getError('', 'IVE0808').message;
            }

            if (Object.keys(validationError).length > 0) {
                // Throw structured error with code and validationError payload
                throw { code: 'IVE0000', validationError };
            }

            // Update existing adhoc allowance type
            await trx(ehrTables.onetimeEarningTypes)
                .where('One_Time_Earning_Type_Id', args.adhocAllowanceTypeId)
                .update(adhocAllowanceTypeData);

            adhocAllowanceTypeId = args.adhocAllowanceTypeId;

            // Delete existing benefit associations
            await trx(ehrTables.adhocAllowanceBenefitAssociation)
                .where('Adhoc_Allowance_Id', adhocAllowanceTypeId)
                .del();
        } else {
            // Insert new adhoc allowance type
            const [insertId] = await trx(ehrTables.onetimeEarningTypes).insert(adhocAllowanceTypeData);
            adhocAllowanceTypeId = insertId;
        }

        // Insert benefit associations if provided
        if (args.benefitAssociation && args.benefitAssociation.length > 0) {
            const benefitAssociations = args.benefitAssociation.map(formId => ({
                Adhoc_Allowance_Id: adhocAllowanceTypeId,
                Form_Id: formId
            }));

            await trx(ehrTables.adhocAllowanceBenefitAssociation).insert(benefitAssociations);
        }

        return { adhocAllowanceTypeId };
    });
}

// Delete adhoc allowance type with checks
async function deleteAdhocAllowanceTypeWithChecks(organizationDbConnection, adhocAllowanceTypeId) {
    return await organizationDbConnection.transaction(async (trx) => {
        let validationError = {};

        // Check if adhoc allowance type exists
        const adhocAllowanceType = await trx(ehrTables.onetimeEarningTypes)
            .where('One_Time_Earning_Type_Id', adhocAllowanceTypeId)
            .first();

        if (!adhocAllowanceType) {
            throw 'IVE0757';
        }

        // Check if there are any employee one-time earnings with Applied or Approved status
        const employeeEarningsCount = await trx(ehrTables.employeeOneTimeEarnings)
            .where('One_Time_Earning_Type_Id', adhocAllowanceTypeId)
            .whereIn('Approval_Status', ['Applied', 'Approved'])
            .count('* as count')
            .first();

        if (employeeEarningsCount && employeeEarningsCount.count > 0) {
            validationError['IVE0807'] = commonLib.func.getError('', 'IVE0807').message;
        }

        // Check if there are any candidate one-time earnings with Applied or Approved status
        const candidateEarningsCount = await trx(ehrTables.candidateOneTimeEarnings)
            .where('One_Time_Earning_Type_Id', adhocAllowanceTypeId)
            .whereIn('Approval_Status', ['Applied', 'Approved'])
            .count('* as count')
            .first();

        if (candidateEarningsCount && candidateEarningsCount.count > 0) {
            validationError['IVE0809'] = commonLib.func.getError('', 'IVE0809').message;
        }

        if (Object.keys(validationError).length > 0) {
            // Throw structured error with code and validationError payload
            throw { code: 'IVE0000', validationError };
        }

        // Check if adhoc allowance type is in use (old adhoc_allowance table)
        const adhocAllowanceUsage = await trx(ehrTables.adhocAllowance)
            .where('Title', adhocAllowanceType.Title)
            .first();

        if (adhocAllowanceUsage) {
            throw 'IVE0758';
        }

        // Delete benefit associations first
        await trx(ehrTables.adhocAllowanceBenefitAssociation)
            .where('Adhoc_Allowance_Id', adhocAllowanceTypeId)
            .del();

        // Delete the adhoc allowance type
        await trx(ehrTables.onetimeEarningTypes)
            .where('One_Time_Earning_Type_Id', adhocAllowanceTypeId)
            .del();

        return {
            errorCode: '',
            message: 'Adhoc allowance type deleted successfully.',
            success: true
        };
    });
}

module.exports.resolvers = resolvers;

