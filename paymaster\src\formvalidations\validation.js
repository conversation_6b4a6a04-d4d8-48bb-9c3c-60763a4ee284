const alphaNumeric = /^[a-zA-Z0-9]+$/; // alpha numeric
const onlyNumbers = /^[0-9]+$/; /**only numbers allowed */
const decimal=/^\d{1,13}(\.\d{0,2})?$/;
const alphaNumSpace = /^[a-zA-Z0-9\ ]+$/; /**alpha num space */
const percentageValidation=/^[0-9]*(\.[0-9]{0,2})?$/;
const alphabet = /^[a-zA-Z0-9]+$/; // alpha numeric
const alphaNumberSlash = /^[a-zA-Z0-9/]+$/; /**alpha num slash */;
var multilingualNameValidation =  /^[^\d<>]*$/u;
var multilingualNameNumericValidation = /^[^<>]*$/u;
// require moment package
let moment = require('moment-timezone');
// require getEffectiveDate file
const getEffectiveDate=require('../resolvers/getEffectiveDate');

module.exports = {
    alphaNumeric: function (input) {
        return (result = alphaNumeric.test(input) ? true : false);
    },
    onlyNumbers: function (input) {
        return (result = onlyNumbers.test(input) ? true : false);
    },
    checkLength : function(input,minLength,maxLength){
        return (result = (input.length < minLength || input.length > maxLength) ? false : true);
    },
    decimalValidation : function(input){
        return (result = decimal.test(input) ? true : false);
    },
    alphaNumSpace: function (input) {
        return (result = alphaNumSpace.test(input) ? true : false);
    },
    percentageValidation: function (input) {
        return (result = percentageValidation.test(input) ? true : false);
    },
    onlyAlphabet: function (input) {
        return (result = alphabet.test(input) ? true : false);
    },
    alphaNumberSlash: function (input) {
        return (result = alphaNumberSlash.test(input) ? true : false);
    },
    checkMinMaxValue: function (input, minValue, maxValue) {
        return (result = (input < minValue || input > maxValue) ? false : true);
    },
    effectiveDateValidation:async (args,action,context)=>{
        try{
            let result='';
            let effectiveDate= args.effectiveFrom;
            let inputArgs={
                "employeeId":args.employeeId,
                "action":action
            }
            let effectiveFromValidation=await getEffectiveDate.getEffectiveDate('',inputArgs,context,'');
            if(effectiveFromValidation.effectiveDateRange.length){
                let effectiveDateArray=effectiveFromValidation.effectiveDateRange;
                for(var i=0; i<effectiveDateArray.length; i++){
                    if(moment(new Date(effectiveDateArray[i])).format('YYYY-MM-DD') == moment(new Date(effectiveDate)).format('YYYY-MM-DD')){
                      result = 'Exist';
                      break;
                    }
                  }
                  
                if(result!=='Exist'){
                    throw 'IVE0087';
                }
                else{
                    return result;
                }
            }
            else{
                return result;
            }
        }
        catch(error){
            console.log('Error in effectiveDateValidation main catch block',error);
            return error;
        }
    },
    multilingualNameValidation : function (input) {
        return (result = multilingualNameValidation.test(input) ? true : false);
    },
    multilingualNameNumericValidation : function (input) {
        return (result = multilingualNameNumericValidation.test(input) ? true : false);
    }
};
