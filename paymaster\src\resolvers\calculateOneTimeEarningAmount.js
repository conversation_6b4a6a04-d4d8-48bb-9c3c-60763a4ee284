// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const knex = require('knex');
const moment = require('moment-timezone');
const { validateFormula } = require('../common/formulaValidation');
const { evaluateFormula } = require('../common/formulaEvaluation');

// resolver definition
const resolvers = {
    Query: {
        // function to calculate one time earning amount from formula
        calculateOneTimeEarningAmount: async (parent, args, context, info) => {
            let organizationDbConnection;
            let validationError = {};
            
            try {
                console.log('Inside calculateOneTimeEarningAmount function');

                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Check if this is an internal call (skipAccessControl flag)
                const skipAccessControl = args.skipAccessControl === true;

                // Validate required inputs
                if (!skipAccessControl && !args.formId) {
                    validationError['IVE0658'] = commonLib.func.getError('', 'IVE0658').message1;
                }
                if (!args.employeeId) {
                    validationError['IVE0800'] = commonLib.func.getError('', 'IVE0800').message;
                }
                if (!args.formula || args.formula.trim().length === 0) {
                    validationError['IVE0801'] = commonLib.func.getError('', 'IVE0801').message;
                }

                if (Object.keys(validationError).length > 0) {
                    throw 'IVE0000';
                }

                // Only perform authorization checks if NOT an internal call
                if (!skipAccessControl) {
                    // Authorization check: Verify user has view access to the form
                    const checkRights = await commonLib.func.checkEmployeeAccessRights(
                        organizationDbConnection,
                        loggedInEmpId,
                        null,
                        '',
                        'UI',
                        false,
                        args.formId
                    );

                    if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                        throw '_DB0100'; // You do not have access to view this information
                    }

                    // Scope check: Allow if user is admin or querying their own employeeId
                    const isAdmin = checkRights.Employee_Role && checkRights.Employee_Role.toLowerCase() === 'admin';
                    const isOwnRecord = parseInt(args.employeeId) === parseInt(loggedInEmpId);

                    if (!isAdmin && !isOwnRecord) {
                        throw '_DB0100'; // You do not have access to view this information
                    }
                }

                // Validate formula syntax
                const validation = await validateFormula(organizationDbConnection, args.formula);
                if (!validation.isValid) {
                    throw validation.errors[0];
                }

                // Get employee's salary components (monthly amounts)
                const componentValues = await getEmployeeComponentValues(
                    organizationDbConnection,
                    args.employeeId
                );

                // Check if employee has any components
                if (Object.keys(componentValues).length === 0) {
                    throw 'PST0900'; // No salary components found for employee
                }

                // Evaluate formula (with throwOnError=true for proper error messages)
                const result = await evaluateFormula(
                    args.formula,
                    componentValues,
                    organizationDbConnection,
                    true
                );

                if (!result.success) {
                    console.log('Error evaluating formula:', result.error);
                    if (result.missingComponents) {
                        console.log('Missing components:', result.missingComponents);
                    }
                    // Throw ApolloError with custom message from formula evaluation
                    const errorMessage = result.error || 'Formula evaluation failed';
                    throw new ApolloError(errorMessage, 'PST0901');
                }

                return {
                    errorCode: '',
                    message: 'Amount calculated successfully',
                    success: true,
                    amount: result.result
                };

            } catch (error) {
                console.log('Error in calculateOneTimeEarningAmount function main catch block', error);

                // If it's already an ApolloError or UserInputError, re-throw it
                if (error instanceof ApolloError || error instanceof UserInputError) {
                    throw error;
                }

                if (error === 'IVE0000') {
                    const errResult = commonLib.func.getError('', 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }

                const errResult = commonLib.func.getError(error, 'PST0901');
                throw new ApolloError(errResult.message, errResult.code);
            } finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        },

        // function to calculate candidate one time earning amount from formula
        calculateCandidateOneTimeEarningAmount: async (parent, args, context, info) => {
            let organizationDbConnection;
            let validationError = {};

            try {
                console.log('Inside calculateCandidateOneTimeEarningAmount function');

                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Check if this is an internal call (skipAccessControl flag)
                const skipAccessControl = args.skipAccessControl === true;

                // Validate required inputs
                if (!skipAccessControl && !args.formId) {
                    validationError['IVE0658'] = commonLib.func.getError('', 'IVE0658').message1;
                }
                if (!args.candidateId) {
                    validationError['IVE0777'] = commonLib.func.getError('', 'IVE0777').message1;
                }
                if (!args.formula || args.formula.trim().length === 0) {
                    validationError['IVE0801'] = commonLib.func.getError('', 'IVE0801').message1;
                }

                if (Object.keys(validationError).length > 0) {
                    throw 'IVE0000';
                }

                // Only perform authorization checks if NOT an internal call
                if (!skipAccessControl) {
                    // Authorization check: Verify user has view access to the form
                    const checkRights = await commonLib.func.checkEmployeeAccessRights(
                        organizationDbConnection,
                        loggedInEmpId,
                        null,
                        '',
                        'UI',
                        false,
                        args.formId
                    );

                    if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                        throw '_DB0100'; // You do not have access to view this information
                    }

                    // For candidate calculations, only admins should have access
                    const isAdmin = checkRights.Employee_Role && checkRights.Employee_Role.toLowerCase() === 'admin';

                    if (!isAdmin) {
                        throw '_DB0100'; // You do not have access to view this information
                    }
                }

                // Validate formula syntax
                const validation = await validateFormula(organizationDbConnection, args.formula);
                if (!validation.isValid) {
                    throw validation.errors[0];
                }

                // Get candidate's salary components (monthly amounts)
                const componentValues = await getCandidateComponentValues(
                    organizationDbConnection,
                    args.candidateId
                );

                // Check if candidate has any components
                if (Object.keys(componentValues).length === 0) {
                    throw 'PST0900'; // No salary components found for candidate
                }

                // Evaluate formula (with throwOnError=true for proper error messages)
                const result = await evaluateFormula(
                    args.formula,
                    componentValues,
                    organizationDbConnection,
                    true
                );

                if (!result.success) {
                    console.log('Error evaluating formula:', result.error);
                    if (result.missingComponents) {
                        console.log('Missing components:', result.missingComponents);
                    }
                    // Throw ApolloError with custom message from formula evaluation
                    const errorMessage = result.error || 'Formula evaluation failed';
                    throw new ApolloError(errorMessage, 'PST0901');
                }

                return {
                    errorCode: '',
                    message: 'Amount calculated successfully',
                    success: true,
                    amount: result.result
                };

            } catch (error) {
                console.log('Error in calculateCandidateOneTimeEarningAmount function main catch block', error);

                // If it's already an ApolloError or UserInputError, re-throw it
                if (error instanceof ApolloError || error instanceof UserInputError) {
                    throw error;
                }

                if (error === 'IVE0000') {
                    const errResult = commonLib.func.getError('', 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                }

                const errResult = commonLib.func.getError(error, 'PST0901');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

/**
 * Get employee's salary component values for formula evaluation
 * @param {Object} organizationDbConnection - Database connection
 * @param {Number} employeeId - Employee ID
 * @returns {Promise<Object>} - Object with component codes as keys and amounts as values
 */
async function getEmployeeComponentValues(organizationDbConnection, employeeId) {
    try {
        const componentValues = {};

        // 1. Get EARNING components (allowances) with monthly amounts
        const allowances = await organizationDbConnection('employee_salary_allowance as ESA')
            .join('allowance_type as AT', 'AT.Allowance_Type_Id', 'ESA.Allowance_Type_Id')
            .join('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
            .select('SC.Component_Code', 'ESA.Amount')
            .where('ESA.Employee_Id', employeeId)
            .where('SC.Component_Type', 'EARNING');

        allowances.forEach(item => {
            componentValues[item.Component_Code.toLowerCase()] = parseFloat(item.Amount) || 0;
        });

        // 2. Get GROSS components from salary_gross_components
        // These are components like 'gross_salary_amount', 'gross_inclusive_of_retirals', etc.
        const grossComponents = await organizationDbConnection('salary_gross_components as SGC')
            .join('gross_configuration as GC', 'GC.Gross_Id', 'SGC.Gross_Id')
            .join('salary_components as SC', 'GC.Salary_Component_Id', 'SC.Component_Id')
            .select('SC.Component_Code', 'SGC.Amount')
            .where('SGC.Employee_Id', employeeId)
            .where('SC.Component_Type', 'GROSS');

        grossComponents.forEach(item => {
            componentValues[item.Component_Code.toLowerCase()] = parseFloat(item.Amount) || 0;
        });

        // 3. Get CTC from employee_salary_details
        // CTC component code in salary_components is 'cost_to_company_amount'
        // Value comes from employee_salary_details.Annual_Ctc
        const salaryDetails = await organizationDbConnection('employee_salary_details as ESD')
            .select('ESD.Annual_Ctc', 'ESD.Annual_Gross_Salary', 'ESD.Monthly_Gross_Salary')
            .where('ESD.Employee_Id', employeeId)
            .whereNull('ESD.Effective_To')
            .first();

        if (salaryDetails) {
            // CTC component (Annual CTC value)
            componentValues['cost_to_company_amount'] = parseFloat(salaryDetails.Annual_Ctc) || 0;

            // Also add annual_gross_salary and monthly_gross_salary for backward compatibility
            componentValues['annual_gross_salary'] = parseFloat(salaryDetails.Annual_Gross_Salary) || 0;
            componentValues['monthly_gross_salary'] = parseFloat(salaryDetails.Monthly_Gross_Salary) || 0;
        }

        return componentValues;
    } catch (error) {
        console.log('Error in getEmployeeComponentValues function:', error);
        throw error;
    }
}

/**
 * Get candidate's salary component values for formula evaluation
 * @param {Object} organizationDbConnection - Database connection
 * @param {Number} candidateId - Candidate ID
 * @returns {Promise<Object>} - Object with component codes as keys and amounts as values
 */
async function getCandidateComponentValues(organizationDbConnection, candidateId) {
    try {
        const componentValues = {};

        // 1. Get EARNING components (allowances) with monthly amounts
        const allowances = await organizationDbConnection('candidate_salary_allowance as CSA')
            .join('allowance_type as AT', 'AT.Allowance_Type_Id', 'CSA.Allowance_Type_Id')
            .join('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
            .select('SC.Component_Code', 'CSA.Amount')
            .where('CSA.Candidate_Id', candidateId)
            .where('SC.Component_Type', 'EARNING');

        allowances.forEach(item => {
            componentValues[item.Component_Code.toLowerCase()] = parseFloat(item.Amount) || 0;
        });

        // 2. Get GROSS components from candidate_gross_components
        // These are components like 'gross_salary_amount', 'gross_inclusive_of_retirals', etc.
        const grossComponents = await organizationDbConnection('candidate_gross_components as CGC')
            .join('gross_configuration as GC', 'GC.Gross_Id', 'CGC.Gross_Id')
            .join('salary_components as SC', 'GC.Salary_Component_Id', 'SC.Component_Id')
            .select('SC.Component_Code', 'CGC.Amount')
            .where('CGC.Candidate_Id', candidateId)
            .where('SC.Component_Type', 'GROSS');

        grossComponents.forEach(item => {
            componentValues[item.Component_Code.toLowerCase()] = parseFloat(item.Amount) || 0;
        });

        // 3. Get CTC from candidate_salary_details
        // CTC component code in salary_components is 'cost_to_company_amount'
        // Value comes from candidate_salary_details.Annual_Ctc
        const salaryDetails = await organizationDbConnection('candidate_salary_details as CSD')
            .select('CSD.Annual_Ctc', 'CSD.Annual_Gross_Salary', 'CSD.Monthly_Gross_Salary')
            .where('CSD.Candidate_Id', candidateId)
            .first();

        if (salaryDetails) {
            // CTC component (Annual CTC value)
            componentValues['cost_to_company_amount'] = parseFloat(salaryDetails.Annual_Ctc) || 0;

            // Also add annual_gross_salary and monthly_gross_salary for backward compatibility
            componentValues['annual_gross_salary'] = parseFloat(salaryDetails.Annual_Gross_Salary) || 0;
            componentValues['monthly_gross_salary'] = parseFloat(salaryDetails.Monthly_Gross_Salary) || 0;
        }

        return componentValues;
    } catch (error) {
        console.log('Error in getCandidateComponentValues function:', error);
        throw error;
    }
}

exports.resolvers = resolvers;

