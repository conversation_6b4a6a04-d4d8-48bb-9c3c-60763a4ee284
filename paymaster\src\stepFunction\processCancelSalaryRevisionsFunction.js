/**
 * Step Function handler for bulk salary revision cancellation
 * This function processes revisions from "Cancel In Progress" to "Cancel Applied" and initiates workflow
 */

const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const moment = require('moment-timezone');
const { ehrTables } = require('../common/tablealias');
const {
  getWorkflowProcessInstanceId,
  initaiteWorkflow,
  getEventId,
  deleteOldApprovalRecordsWithoutTrx
} = require('../common/commonfunctions');

/**
 * Step Function handler for bulk salary revision cancellation
 * @param {Object} event - Step Function event containing input parameters
 * @param {Object} context - Lambda context
 * @returns {Object} - Cancellation result
 */
const processCancelSalaryRevisionsFunction = async (event, context) => {
    let organizationDbConnection;
    let sessionId;

    try {
        console.log('=== STEP FUNCTION: Bulk Salary Revision Cancellation Started ===');

        // Extract parameters from Step Function event
        const {
            revisionIds,
            employeeIds,
            employeeId,
            sessionId: eventSessionId,
            userIp,
            orgCode,
            formId
        } = event;

        // Assign to outer scope variables
        sessionId = eventSessionId;

        // Validate required parameters
        if (!revisionIds || !employeeIds || !employeeId || !orgCode) {
            throw new Error('Missing required parameters: revisionIds, employeeIds, employeeId, or orgCode');
        }

        // Get database connection
        let connection = await commonLib.func.getDataBaseConnection({
            stageName: process.env.stageName,
            dbPrefix: process.env.dbPrefix,
            dbSecretName: process.env.dbSecretName,
            region: process.env.region,
            orgCode: orgCode
        });
        
        // Create database connection
        organizationDbConnection = knex(connection.OrganizationDb);

        // Get current timestamp
        const currentTimestamp = moment().utc().format('YYYY-MM-DD HH:mm:ss');

        // Get event ID for workflow
        const eventId = await getEventId(organizationDbConnection, formId || 360);

        // Process each revision
        let successCount = 0;
        let failureCount = 0;
        const results = [];

        for (let i = 0; i < revisionIds.length; i++) {
            const revisionId = revisionIds[i];
            const empId = employeeIds[i];

            const result = {
                revisionId: revisionId,
                employeeId: empId,
                success: false,
                errorCode: '',
                errorMessage: ''
            };

            try {
                // Get current revision details with Added_By name (matching addUpdateSalaryDetails.js)
                const currentRevision = await organizationDbConnection(ehrTables.salaryRevisionDetails + ' as srd')
                    .select(
                        'srd.Revision_Status',
                        'srd.Employee_Id',
                        'srd.Template_Id',
                        'srd.Salary_Effective_Month',
                        'srd.Effective_From',
                        'srd.Annual_Ctc',
                        'srd.Annual_Gross_Salary',
                        'srd.Monthly_Gross_Salary',
                        'srd.Payout_Month',
                        'srd.Revision_Type',
                        'srd.Revise_Ctc_By_Percentage',
                        'srd.Previous_Ctc',
                        'srd.Added_By',
                        organizationDbConnection.raw(`DATE_FORMAT(srd.Added_On, '%Y-%m-%d %H:%i:%s') as Added_On`),
                        organizationDbConnection.raw(`CONCAT_WS(" ", ep.Emp_First_Name, ep.Emp_Middle_Name, ep.Emp_Last_Name) as Added_By_Name`)
                    )
                    .leftJoin(ehrTables.empPersonalInfo + ' as ep', 'ep.Employee_Id', 'srd.Added_By')
                    .where('srd.Revision_Id', revisionId)
                    .first();

                if (!currentRevision) {
                    result.errorCode = 'PST0014';
                    result.errorMessage = 'Revision record not found';
                    failureCount++;
                    results.push(result);
                    continue;
                }

                // Check if status is "Cancel In Progress"
                const currentStatus = currentRevision.Revision_Status.toLowerCase();
                if (currentStatus !== 'cancel in progress') {
                    result.errorCode = 'PST0039';
                    result.errorMessage = `Invalid status for processing: ${currentRevision.Revision_Status}`;
                    failureCount++;
                    results.push(result);
                    continue;
                }

                // Update revision status to "Cancel Applied"
                await organizationDbConnection(ehrTables.salaryRevisionDetails)
                    .where('Revision_Id', revisionId)
                    .update({
                        Revision_Status: 'Cancel Applied',
                        Updated_On: currentTimestamp,
                        Updated_By: sessionId
                    });

                // Get workflow process instance ID
                const responseObject = await getWorkflowProcessInstanceId(
                    organizationDbConnection,
                    revisionId
                );

                // Initiate workflow if event ID exists
                if (eventId) {
                    // Get basicPay for workflow (required by workflow schema)
                    const Basic_Pay = await getBasicPay(organizationDbConnection, empId);

                    // Prepare instance data for workflow (matching addUpdateSalaryDetails.js format + workflow schema requirements)
                    const instanceData = {
                        formId: Number(formId ?? 360),
                        id: Number(revisionId ?? 0),
                        annualCTC: String(currentRevision.Annual_Ctc ?? ""),
                        employeeId: Number(empId ?? 0),
                        Employee_Id: Number(empId ?? 0),
                        templateId: Number(currentRevision.Template_Id ?? 0),
                        effectiveFrom: String(currentRevision.Effective_From ?? ""),
                        effectiveTo: "",  // Required by workflow schema but column doesn't exist in table
                        basicPay: String(Basic_Pay && Basic_Pay.Basic_Pay ? Basic_Pay.Basic_Pay : ""),  // Required by workflow schema
                        salaryEffectiveMonth: String(currentRevision.Salary_Effective_Month ?? ""),
                        Revise_Ctc_By_Percentage: String(currentRevision.Revise_Ctc_By_Percentage ?? ""),
                        annualGrossSalary: String(currentRevision.Annual_Gross_Salary ?? ""),
                        monthlyGrossSalary: String(currentRevision.Monthly_Gross_Salary ?? ""),
                        payoutMonth: String(currentRevision.Payout_Month ?? ""),
                        revisionType: String(currentRevision.Revision_Type ?? ""),
                        revisionStatus: String('Cancel Applied'),
                        previousCtc: String(currentRevision.Previous_Ctc ?? ""),
                        reviseCtcByPercentage: String(currentRevision.Revise_Ctc_By_Percentage ?? ""),
                        Updated_On: String(moment().utc().format('YYYY-MM-DD HH:mm:ss')),  // Use moment.utc()
                        Updated_By: Number(sessionId)
                    };

                    // Get employee name for workflow (matching addUpdateSalaryDetails.js lines 929-935)
                    let employeeNameObject = await getEmployeeName(organizationDbConnection, sessionId);
                    if (!employeeNameObject) {
                        console.log("login employee details not found", sessionId);
                        throw 'ETR0003';
                    }

                    instanceData.Updated_By = employeeNameObject.employeeName;

                    // Set Added_On and Added_By_Name from current revision (matching lines 936-940)
                    // NOTE: Added_By is set to sessionId (current user), not original Added_By
                    if (currentRevision) {
                        instanceData.Added_On = currentRevision.Added_On;
                        instanceData.Added_By_Name = currentRevision.Added_By_Name;
                        instanceData.Added_By = Number(sessionId);  // Current user, not original Added_By
                    }

                    // Initiate workflow (matching addUpdateSalaryDetails.js lines 953-961)
                    await initaiteWorkflow(
                        eventId,
                        instanceData,
                        orgCode,
                        formId || 360,
                        organizationDbConnection,
                        sessionId,
                        null // No transaction
                    );
                }

                // Delete old approval records if workflow process instance exists
                if (responseObject && responseObject[0]?.Process_Instance_Id) {
                    await deleteOldApprovalRecordsWithoutTrx(
                        organizationDbConnection,
                        responseObject[0].Process_Instance_Id
                    );
                }

                // Mark as successful
                result.success = true;
                successCount++;

            } catch (error) {
                // Capture error for this specific revision
                result.success = false;
                result.errorCode = typeof error === 'string' ? error : 'PST0041';
                result.errorMessage = getErrorMessage(result.errorCode);
                failureCount++;
                console.error(`Error processing revision ${revisionId}:`, error);

                // Update revision status to "Cancel Failed"
                try {
                    await organizationDbConnection(ehrTables.salaryRevisionDetails)
                        .where('Revision_Id', revisionId)
                        .update({
                            Revision_Status: 'Cancel Failed',
                            Updated_On: currentTimestamp,
                            Updated_By: sessionId
                        });
                } catch (updateError) {
                    console.error(`Error updating revision ${revisionId} to Cancel Failed:`, updateError);
                }
            }

            results.push(result);
        }

        // Log system activity
        const systemLogParam = {
            userIp: userIp || 'Step Function',
            employeeId: employeeId,
            organizationDbConnection: organizationDbConnection,
            message: `Bulk salary revision cancellation completed via Step Function: ${successCount} succeeded, ${failureCount} failed`
        };
        await commonLib.func.createSystemLogActivities(systemLogParam);

        return {
            statusCode: 200,
            success: true,
            message: `Bulk cancellation completed: ${successCount} succeeded, ${failureCount} failed`,
            successCount: successCount,
            failureCount: failureCount,
            results: results
        };

    } catch (error) {
        console.error('=== STEP FUNCTION: Bulk Salary Revision Cancellation Failed ===');
        console.error('Error:', error);

        // Return error response for Step Function
        return {
            statusCode: 500,
            success: false,
            message: 'Bulk salary revision cancellation failed',
            error: error.message || error
        };
    } finally {
        // Always destroy database connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
};

/**
 * Get employee name by employee ID (copied from addUpdateSalaryDetails.js)
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} employeeId - Employee ID
 * @returns {Promise<Object>} - Employee name object
 */
async function getEmployeeName(organizationDbConnection, employeeId) {
    try {
        return await organizationDbConnection(ehrTables.empPersonalInfo + " as EP")
            .select(organizationDbConnection.raw(`
                CONCAT_WS(" ", EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as employeeName
            `))
            .where('Employee_Id', employeeId)
            .first();
    } catch (error) {
        console.log('Error in getEmployeeName function', error);
        throw error;
    }
}

/**
 * Get basic pay for employee (copied from addUpdateSalaryDetails.js)
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} employeeId - Employee ID
 * @returns {Promise<Object>} - Basic pay object
 */
async function getBasicPay(organizationDbConnection, employeeId) {
    try {
        return await organizationDbConnection(ehrTables.employeeSalaryAllowance + ' as ESL')
            .select('ESL.Amount as Basic_Pay')
            .innerJoin(ehrTables.allowanceType + ' as AT', 'ESL.Allowance_Type_Id', 'AT.Allowance_Type_Id')
            .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
            .where('SC.Component_Code', 'basic_salary_amount')
            .where('ESL.Employee_Id', employeeId)
            .first();
    } catch (error) {
        console.log('Error in getBasicPay function', error);
        throw error;
    }
}

/**
 * Get error message for error code
 * @param {string} errorCode - Error code
 * @returns {string} - Error message
 */
function getErrorMessage(errorCode) {
    const errorMessages = {
        'PST0014': 'Salary revision record not found',
        'PST0039': 'Invalid revision status for cancellation processing',
        'PST0041': 'Error occurred during cancellation processing'
    };

    return errorMessages[errorCode] || 'Unknown error occurred';
}

// Export for both Step Function (handler) and direct call (processCancelSalaryRevisionsFunction)
module.exports = {
    handler: processCancelSalaryRevisionsFunction,
    processCancelSalaryRevisionsFunction: processCancelSalaryRevisionsFunction
};
