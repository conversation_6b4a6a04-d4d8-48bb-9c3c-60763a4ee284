// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { ehrTables } = require('../common/tablealias');
const { formId } = require('../common/appconstants');

/**
 * Calculate previous month from given month and year
 * @param {number} month - Current month (1-12)
 * @param {number} year - Current year
 * @returns {Object} - {previousMonth, previousYear}
 */
function calculatePreviousMonth(month, year) {
    if (month === 1) {
        return { previousMonth: 12, previousYear: year - 1 };
    }
    return { previousMonth: month - 1, previousYear: year };
}

/**
 * Format month and year to salary month format (M,YYYY)
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {string} - Formatted salary month (e.g., "4,2025")
 */
function formatSalaryMonth(month, year) {
    return `${month},${year}`;
}

/**
 * Get employee access control based on role
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} loginEmployeeId - Logged in employee ID
 * @param {string} orgCode - Organization code
 * @param {Object} checkRights - Access rights object
 * @returns {Promise<Object>} - {isAdmin, employeeIdsArray}
 */
async function getEmployeeAccessControl(organizationDbConnection, loginEmployeeId, orgCode, checkRights) {
    let employeeIdsArray = [];
    let isAdmin = false;

    // Check if user is admin
    if (checkRights.Employee_Role && checkRights.Employee_Role.toLowerCase() === 'admin') {
        isAdmin = true;
        return { isAdmin, employeeIdsArray };
    }

    // Check service provider admin
    const serviceProviderAdmin = await commonLib.func.checkEmployeeAccessRights(
        organizationDbConnection,
        loginEmployeeId,
        '',
        'Role_Update',
        '',
        false,
        formId.serviceProviderAdmin
    );

    if (serviceProviderAdmin) {
        const serviceProviderEmployeeIds = await commonLib.func.getServiceProviderEmpIdsForFieldForce(
            organizationDbConnection,
            loginEmployeeId,
            orgCode
        );
        employeeIdsArray = employeeIdsArray.concat(serviceProviderEmployeeIds);
    } else if (checkRights.Is_Manager === 1) {
        // Manager access
        const managerAccessDetails = await commonLib.func.getManagerHierarchy(
            organizationDbConnection,
            loginEmployeeId,
            0,
            null
        );

        const orgDetails = await organizationDbConnection(ehrTables.orgDetails)
            .select('Restrict_Financial_Access_For_Manager')
            .where('Org_Code', orgCode)
            .first();

        // When Restrict_Financial_Access_For_Manager = 1, manager IS restricted (no financial access)
        // When Restrict_Financial_Access_For_Manager ≠ 1, manager has access to reportees
        if (orgDetails && orgDetails.Restrict_Financial_Access_For_Manager !== 1) {
            // Manager has access to financial data - include reportees
            employeeIdsArray = employeeIdsArray.concat(managerAccessDetails);
        } else {
            // Manager is restricted - use sentinel value to ensure fail-closed security
            employeeIdsArray = [-1];
        }
    } else {
        // Regular employee - only their own records
        employeeIdsArray.push(loginEmployeeId);
    }

    return { isAdmin, employeeIdsArray };
}

/**
 * Get payslips for a specific month with employee details
 * @param {Object} organizationDbConnection - Database connection
 * @param {string} salaryMonth - Salary month in format "M,YYYY"
 * @param {number} employeeId - Optional employee ID filter
 * @param {number} limit - Pagination limit
 * @param {number} offset - Pagination offset
 * @param {boolean} isAdmin - Is user admin
 * @param {Array} employeeIdsArray - Array of employee IDs user has access to (for access control)
 * @param {Array} employeeIdsFilter - Optional explicit array of employee IDs to filter (overrides access control)
 * @returns {Promise<Array>} - Array of payslip records with employee details
 */
async function getPayslipsForMonth(organizationDbConnection, salaryMonth, employeeId, limit, offset, isAdmin, employeeIdsArray, employeeIdsFilter = null) {
    let query = organizationDbConnection(ehrTables.salaryPayslip + ' as SP')
        .select(
            'SP.Payslip_Id',
            'SP.Employee_Id',
             organizationDbConnection.raw(
            '(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE SP.Employee_Id END) as User_Defined_EmpId'
          ),
            'SP.Salary_Month',
            'SP.Basic_Salary',
            'SP.Total_Earning',
            'SP.Total_Deduction',
            'SP.Total_Salary',
            'SP.Unpaid_Leave_Days',
            'SP.Salary_Calc_Emp_Worked_Days',
            organizationDbConnection.raw(
                '(SELECT AT.Name_In_Payslip FROM ?? ESA INNER JOIN ?? AT ON ESA.Allowance_Type_Id = AT.Allowance_Type_Id INNER JOIN ?? SC ON AT.Salary_Component_Id = SC.Component_Id WHERE ESA.Employee_Id = SP.Employee_Id AND LOWER(SC.Component_Code) = ? LIMIT 1) as Name_In_Payslip',
                [ehrTables.employeeSalaryAllowance, ehrTables.allowanceType, ehrTables.salaryComponents, 'basic_salary_amount']
            ),
            organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Employee_Name"),
            'EJ.Date_Of_Join',
            'R.Resignation_Date'
        )
        .innerJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'SP.Employee_Id')
        .leftJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'SP.Employee_Id')
        .leftJoin(ehrTables.empResignation + ' as R', function () {
            this.on('R.Employee_Id', '=', 'SP.Employee_Id')
                .onIn('R.Approval_Status', organizationDbConnection.raw('(?,?)', ['Applied', 'Approved']))
        })
        .where('SP.Salary_Month', salaryMonth)
        .where('EPI.Form_Status', 1);

    // Apply employee filter if provided
    if (employeeId) {
        query.where('SP.Employee_Id', employeeId);
    }

    // Apply explicit employee IDs filter if provided (takes precedence over access control)
    if (employeeIdsFilter !== null && Array.isArray(employeeIdsFilter)) {
        if (employeeIdsFilter.length === 0) {
            // Empty filter means no results - fail-closed security
            query.where('SP.Employee_Id', -1);
        } else {
            query.whereIn('SP.Employee_Id', employeeIdsFilter);
        }
    } else {
        // Apply access control - fail-closed security policy
        if (!isAdmin) {
            // Non-admin users MUST have employee access list
            // Empty array should never happen due to sentinel value, but enforce it here too
            if (employeeIdsArray.length === 0) {
                // No access - return empty result by using impossible condition
                query.where('SP.Employee_Id', -1);
            } else {
                query.whereIn('SP.Employee_Id', employeeIdsArray);
            }
        }
    }

    // Apply pagination only if limit and offset are provided
    if (limit !== undefined && limit !== null) {
        query.limit(limit);
    }
    if (offset !== undefined && offset !== null) {
        query.offset(offset);
    }

    return await query;
}

/**
 * Get earnings (taxable and non-taxable) for payslips
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} payslipIds - Array of payslip IDs
 * @returns {Promise<Object>} - Object with payslipId as key and earnings array as value
 */
async function getEarningsForPayslips(organizationDbConnection, payslipIds) {
    if (!payslipIds || payslipIds.length === 0) {
        return {};
    }

    // Get taxable earnings
    const taxableEarnings = await organizationDbConnection(ehrTables.taxableEarnings)
        .select('Payslip_Id', 'Earning_Name', 'Incentive_Amount', 'Order_By')
        .whereIn('Payslip_Id', payslipIds)
        .orderBy('Order_By');

    // Get non-taxable earnings
    const nontaxableEarnings = await organizationDbConnection(ehrTables.nontaxableEarnings)
        .select('Payslip_Id', 'Earning_Name', 'Incentive_Amount', 'Order_By')
        .whereIn('Payslip_Id', payslipIds)
        .orderBy('Order_By');

    // Combine and group by payslip ID
    const earningsMap = {};

    [...taxableEarnings, ...nontaxableEarnings].forEach(earning => {
        if (!earningsMap[earning.Payslip_Id]) {
            earningsMap[earning.Payslip_Id] = [];
        }
        earningsMap[earning.Payslip_Id].push({
            Component: earning.Earning_Name || 'Unnamed Earning',
            Amount: parseFloat(earning.Incentive_Amount) || 0,
            Order_By: earning.Order_By
        });
    });

    // Sort earnings by Order_By within each payslip
    Object.keys(earningsMap).forEach(payslipId => {
        earningsMap[payslipId].sort((a, b) => a.Order_By - b.Order_By);
    });

    return earningsMap;
}


/**
 * Get deductions for payslips
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} payslipIds - Array of payslip IDs
 * @returns {Promise<Object>} - Object with payslipId as key and deductions array as value
 */
async function getDeductionsForPayslips(organizationDbConnection, payslipIds) {
    if (!payslipIds || payslipIds.length === 0) {
        return {};
    }

    const deductions = await organizationDbConnection(ehrTables.salaryDeduction)
        .select('Payslip_Id', 'Deduction_Display_Name', 'Deduction_Amount', 'Order_By')
        .whereIn('Payslip_Id', payslipIds)
        .orderBy('Order_By');

    // Group by payslip ID
    const deductionsMap = {};

    deductions.forEach(deduction => {
        if (!deductionsMap[deduction.Payslip_Id]) {
            deductionsMap[deduction.Payslip_Id] = [];
        }
        deductionsMap[deduction.Payslip_Id].push({
            Component: deduction.Deduction_Display_Name || 'Unnamed Deduction',
            Amount: parseFloat(deduction.Deduction_Amount) || 0,
            Order_By: deduction.Order_By
        });
    });

    // Sort deductions by Order_By within each payslip
    Object.keys(deductionsMap).forEach(payslipId => {
        deductionsMap[payslipId].sort((a, b) => a.Order_By - b.Order_By);
    });

    return deductionsMap;
}

/**
 * Round number to 2 decimal places to fix floating-point precision issues
 * @param {number} num - Number to round
 * @returns {number} - Rounded number
 */
function roundToTwoDecimals(num) {
    return parseFloat(num.toFixed(2));
}

/**
 * Build salary details comparison array
 * @param {Object} currentPayslip - Current month payslip
 * @param {Object} previousPayslip - Previous month payslip
 * @param {Array} currentEarnings - Current month earnings
 * @param {Array} previousEarnings - Previous month earnings
 * @param {Array} currentDeductions - Current month deductions
 * @param {Array} previousDeductions - Previous month deductions
 * @returns {Array} - Array of salary component comparisons
 */
function buildSalaryDetailsComparison(currentPayslip, previousPayslip, currentEarnings, previousEarnings, currentDeductions, previousDeductions) {
    const salaryDetails = [];

    // 1. Basic Salary
    const currentBasic = parseFloat(currentPayslip.Basic_Salary) || 0;
    const previousBasic = previousPayslip ? (parseFloat(previousPayslip.Basic_Salary) || 0) : 0;

    salaryDetails.push({
        Component: currentPayslip.Name_In_Payslip || 'Basic Salary',
        Previous_Amount: previousBasic,
        Current_Amount: currentBasic,
        Difference: roundToTwoDecimals(currentBasic - previousBasic)
    });

    // 2. Taxable and Non-Taxable Earnings
    const currentEarningsArray = currentEarnings || [];
    const previousEarningsArray = previousEarnings || [];

    // Create a map of all unique earning components
    const earningComponentsMap = new Map();

    currentEarningsArray.forEach(earning => {
        earningComponentsMap.set(earning.Component, { current: earning.Amount, previous: 0 });
    });

    previousEarningsArray.forEach(earning => {
        if (earningComponentsMap.has(earning.Component)) {
            earningComponentsMap.get(earning.Component).previous = earning.Amount;
        } else {
            earningComponentsMap.set(earning.Component, { current: 0, previous: earning.Amount });
        }
    });

    // Add earnings to salary details
    earningComponentsMap.forEach((amounts, component) => {
        salaryDetails.push({
            Component: component,
            Previous_Amount: amounts.previous,
            Current_Amount: amounts.current,
            Difference: roundToTwoDecimals(amounts.current - amounts.previous)
        });
    });

    // 3. Total Earnings
    const currentTotalEarning = parseFloat(currentPayslip.Total_Earning) || 0;
    const previousTotalEarning = previousPayslip ? (parseFloat(previousPayslip.Total_Earning) || 0) : 0;
    salaryDetails.push({
        Component: 'Total Earnings',
        Previous_Amount: previousTotalEarning,
        Current_Amount: currentTotalEarning,
        Difference: roundToTwoDecimals(currentTotalEarning - previousTotalEarning)
    });

    // 4. Deductions
    const currentDeductionsArray = currentDeductions || [];
    const previousDeductionsArray = previousDeductions || [];

    // Create a map of all unique deduction components
    const deductionComponentsMap = new Map();

    currentDeductionsArray.forEach(deduction => {
        deductionComponentsMap.set(deduction.Component, { current: deduction.Amount, previous: 0 });
    });

    previousDeductionsArray.forEach(deduction => {
        if (deductionComponentsMap.has(deduction.Component)) {
            deductionComponentsMap.get(deduction.Component).previous = deduction.Amount;
        } else {
            deductionComponentsMap.set(deduction.Component, { current: 0, previous: deduction.Amount });
        }
    });

    // Add deductions to salary details
    deductionComponentsMap.forEach((amounts, component) => {
        salaryDetails.push({
            Component: component,
            Previous_Amount: amounts.previous,
            Current_Amount: amounts.current,
            Difference: roundToTwoDecimals(amounts.current - amounts.previous)
        });
    });

    // 5. Total Deduction - Calculate by summing all individual deductions
    let currentTotalDeduction = 0;

    currentDeductionsArray.forEach(deduction => {
        currentTotalDeduction += parseFloat(deduction.Amount) || 0;
    });

    let previousTotalDeduction = 0;
    previousDeductionsArray.forEach(deduction => {
        previousTotalDeduction += parseFloat(deduction.Amount) || 0;
    });

    salaryDetails.push({
        Component: 'Total Deductions',
        Previous_Amount: roundToTwoDecimals(previousTotalDeduction),
        Current_Amount: roundToTwoDecimals(currentTotalDeduction),
        Difference: roundToTwoDecimals(currentTotalDeduction - previousTotalDeduction)
    });

    // 6. Total Salary (final row)
    const currentTotalSalary = parseFloat(currentPayslip.Total_Salary) || 0;
    const previousTotalSalary = previousPayslip ? (parseFloat(previousPayslip.Total_Salary) || 0) : 0;

    salaryDetails.push({
        Component: 'Net Pay',
        Previous_Amount: previousTotalSalary,
        Current_Amount: currentTotalSalary,
        Difference: roundToTwoDecimals(currentTotalSalary - previousTotalSalary)
    });

    return salaryDetails;
}


/**
 * Main resolver function to get payroll reconciliation data
 * @param {Object} parent - Parent object
 * @param {Object} args - Arguments passed to the resolver
 * @param {Object} context - Context object containing user info
 * @param {Object} info - GraphQL info object
 * @returns {Object} - Response with payroll reconciliation data
 */
const getPayrollReconciliation = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError = {};

    try {
        console.log('Inside getPayrollReconciliation function');

        const loggedInEmpId = context.logInEmpId;
        const orgCode = context.orgCode;

        // Validate required inputs
        if (!args.payslipMonth) {
            validationError['IVE0802'] = commonLib.func.getError('', 'IVE0802').message;
        }
        if (!args.payslipYear) {
            validationError['IVE0803'] = commonLib.func.getError('', 'IVE0803').message;
        }
        if (args.payslipMonth && (args.payslipMonth < 1 || args.payslipMonth > 12)) {
            validationError['IVE0804'] = commonLib.func.getError('', 'IVE0804').message;
        }
        if (args.payslipYear && (args.payslipYear < 1900 || args.payslipYear > 2100)) {
            validationError['IVE0805'] = commonLib.func.getError('', 'IVE0805').message;
        }

        if (Object.keys(validationError).length > 0) {
            throw 'IVE0000';
        }

        // Get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Check access rights
        const accessFormId = args.formId
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            loggedInEmpId,
            null,
            '',
            'UI',
            false,
            accessFormId
        );

        if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
            throw '_DB0100'; // View access denied
        }

        // Get employee access control
        const { isAdmin, employeeIdsArray } = await getEmployeeAccessControl(
            organizationDbConnection,
            loggedInEmpId,
            orgCode,
            checkRights
        );

        // Calculate previous month
        const { previousMonth, previousYear } = calculatePreviousMonth(args.payslipMonth, args.payslipYear);

        // Format salary months
        const currentSalaryMonth = formatSalaryMonth(args.payslipMonth, args.payslipYear);
        const previousSalaryMonth = formatSalaryMonth(previousMonth, previousYear);


        // Get current month payslips with optional pagination
        const currentPayslips = await getPayslipsForMonth(
            organizationDbConnection,
            currentSalaryMonth,
            args.employeeId,
            args.limit,
            args.offset,
            isAdmin,
            employeeIdsArray,
            null // No explicit filter - use access control
        );

        if (!currentPayslips || currentPayslips.length === 0) {
            throw 'PST0902'; // No payslips found
        }

        // Get employee IDs from current payslips
        const employeeIds = currentPayslips.map(p => p.Employee_Id);

        // Get previous month payslips for the same employees (no pagination)
        // Use explicit employee IDs filter to ensure we only get data for authorized employees
        const previousPayslips = await getPayslipsForMonth(
            organizationDbConnection,
            previousSalaryMonth,
            null, // No single employee filter
            null, // No limit
            null, // No offset
            isAdmin,
            employeeIdsArray,
            employeeIds // Explicit filter: only get previous payslips for current month's employees
        );

        // Create a map of previous payslips by employee ID
        const previousPayslipsMap = {};
        previousPayslips.forEach(payslip => {
            previousPayslipsMap[payslip.Employee_Id] = payslip;
        });

        // Get all payslip IDs (current and previous)
        const currentPayslipIds = currentPayslips.map(p => p.Payslip_Id);
        const previousPayslipIds = previousPayslips.map(p => p.Payslip_Id);
        const allPayslipIds = [...currentPayslipIds, ...previousPayslipIds];

        // Get earnings and deductions for all payslips
        const [earningsMap, deductionsMap] = await Promise.all([
            getEarningsForPayslips(organizationDbConnection, allPayslipIds),
            getDeductionsForPayslips(organizationDbConnection, allPayslipIds)
        ]);

        // Build reconciliation response
        const payrollReconciliation = currentPayslips.map(currentPayslip => {
            const previousPayslip = previousPayslipsMap[currentPayslip.Employee_Id];

            const currentEarnings = earningsMap[currentPayslip.Payslip_Id] || [];
            const previousEarnings = previousPayslip ? (earningsMap[previousPayslip.Payslip_Id] || []) : [];

            const currentDeductions = deductionsMap[currentPayslip.Payslip_Id] || [];
            const previousDeductions = previousPayslip ? (deductionsMap[previousPayslip.Payslip_Id] || []) : [];

            let salaryDetails = buildSalaryDetailsComparison(
                currentPayslip,
                previousPayslip,
                currentEarnings,
                previousEarnings,
                currentDeductions,
                previousDeductions
            );

            // Filter out components where both amounts are 0, but keep summary rows
            const summaryComponents = ['Total Earnings', 'Total Deductions', 'Net Pay'];
            salaryDetails = salaryDetails.filter(item => {
                // Always keep summary components
                if (summaryComponents.includes(item.Component)) {
                    return true;
                }
                // Filter out components where both amounts are 0
                return !(item.Previous_Amount === 0 && item.Current_Amount === 0);
            });


            // Calculate Worked Days for current and previous months
            let currentWorkedDays = (parseFloat(currentPayslip.Salary_Calc_Emp_Worked_Days) || 0) - (parseFloat(currentPayslip.Unpaid_Leave_Days) || 0);
            let previousWorkedDays = previousPayslip ? ((parseFloat(previousPayslip.Salary_Calc_Emp_Worked_Days) || 0) - (parseFloat(previousPayslip.Unpaid_Leave_Days) || 0)) : 0;

            currentWorkedDays = currentWorkedDays < 0 ? 0 : currentWorkedDays;
            previousWorkedDays = previousWorkedDays < 0 ? 0 : previousWorkedDays;

            return {
                Employee_Id: currentPayslip.Employee_Id,
                User_Defined_EmpId: currentPayslip.User_Defined_EmpId || null,
                Employee_Name: currentPayslip.Employee_Name,
                Date_Of_Join: currentPayslip.Date_Of_Join || null,
                Resignation_Date: currentPayslip.Resignation_Date || null,
                Unpaid_Leave_Days: parseFloat(currentPayslip.Unpaid_Leave_Days) || 0,
                Previous_Worked_Days: previousWorkedDays,
                Current_Worked_Days: currentWorkedDays,
                Name_In_Payslip: currentPayslip.Name_In_Payslip || null,
                Salary_Details: salaryDetails
            };
        });

        return {
            success: true,
            msg: 'Payroll reconciliation data retrieved successfully.',
            payrollReconciliation: JSON.stringify(payrollReconciliation)
        };

    } catch (mainCatchError) {
        console.log('Error in getPayrollReconciliation function main catch block', mainCatchError);

        if (mainCatchError === 'IVE0000') {
            const errResult = commonLib.func.getError('', mainCatchError);
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else if (mainCatchError === '_DB0100' || mainCatchError === 'PST0902') {
            const errResult = commonLib.func.getError('', mainCatchError);
            throw new ApolloError(errResult.message, errResult.code);
        } else {
            const errResult = commonLib.func.getError(mainCatchError, 'PST0903');
            throw new ApolloError(errResult.message, errResult.code);
        }
    } finally {
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
};

// resolver definition
const resolvers = {
    Query: {
        getPayrollReconciliation
    }
};

module.exports = { resolvers };
