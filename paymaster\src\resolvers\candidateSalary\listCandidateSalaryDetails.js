// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const commonFunctions = require('../../common/salaryTemplateCommonFunctions');

/**
 * Main resolver function to get candidate salary details
 * @param {Object} parent - Parent object
 * @param {Object} args - Arguments passed to the resolver
 * @param {Object} context - Context object containing user info
 * @param {Object} info - GraphQL info object
 * @returns {Object} - Response with candidate salary details
 */
const listCandidateSalaryDetails = async (parent, args, context, info) => {
  let organizationDbConnection;

  try {
    console.log('Inside listCandidateSalaryDetails function', context);
    let responseData = [];
    const { logInEmpId: loginEmployeeId } = context;
    const userFormId = args.formId
    const isViewMode = args.isViewMode || false;

    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Check access rights for candidate salary
    const accessFormId = args.accessFormId || userFormId;
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      '',
      'UI',
      false,
      accessFormId
    );
    if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
      throw '_DB0100';
    }
    // Get currency symbol and round off settings in parallel
    const [currencySymbolDetails, roundOffDetails] = await Promise.all([
      organizationDbConnection(ehrTables.payrollGeneralSettings)
        .select('Payroll_Currency')
        .first(),
      getRoundOffSettings(organizationDbConnection)
    ]);

    const currencySymbol = currencySymbolDetails?.Payroll_Currency || '₹';

    const mainTableData = await getCandidateMainTableData(organizationDbConnection, args);

    // Get all candidate IDs upfront
    const candidateIds = mainTableData.map(data => data.Candidate_Id);

    // Get all components in parallel
    const [
      allowanceComponentsByRecord,
      retiralsComponentsByRecord,
      grossComponentsByRecord
    ] = await Promise.all([
      getCandidateAllowanceComponentsByRecord(organizationDbConnection, candidateIds),
      getCandidateRetiralsComponentsByRecord(organizationDbConnection, candidateIds),
      getCandidateGrossComponentsByRecord(organizationDbConnection, candidateIds)
    ]);

    // Build response data
    responseData = mainTableData.map(data => {
      const candidateId = data.Candidate_Id;
      let recordData = {
        ...formatCandidateMainTableData(data),
      };

      if (isViewMode) {
        recordData.allowances = {
          allowanceArray: allowanceComponentsByRecord[candidateId]?.Allowance || [],
          fixedAllowanceArray: allowanceComponentsByRecord[candidateId]?.Fixed_Allowance || [],
          bonusArray: allowanceComponentsByRecord[candidateId]?.Bonus || [],
          flexiBenefitPlanArray: allowanceComponentsByRecord[candidateId]?.Flexible_Benefit_Plan || [],
          reimbursementArray: allowanceComponentsByRecord[candidateId]?.Reimbursement || [],
          basicPayArray: allowanceComponentsByRecord[candidateId]?.Basic_Pay || []
        };
        recordData.retirals = retiralsComponentsByRecord[candidateId] || [];
        recordData.grossComponents = grossComponentsByRecord[candidateId] || [];
      }

      return recordData;
    });

    // Get PF settings separately
    const pfSettings = await organizationDbConnection(ehrTables.providentFundSettings)
      .select('Admin_Charge', 'Admin_Charge_Max_Amount', 'EDLI_Charge', 'EDLI_Charge_Max_Amount')
      .first();

    // Get PF configuration separately
    const pfConfig = await organizationDbConnection(ehrTables.providentFund)
      .select('Admin_Charge_Part_Of_CTC', 'Edli_Charge_Part_Of_CTC')
      .first();

    return {
      errorCode: "",
      message: "Candidate salary details retrieved successfully",
      currencySymbol: currencySymbol,
      candidateSalaryDetails: JSON.stringify(responseData),
      roundOffSettings: JSON.stringify(roundOffDetails),
      pfSettings: JSON.stringify({
        ...(pfSettings || {}),
        ...(pfConfig || {})
      })
    };
  } catch (error) {
    console.log('Error in listCandidateSalaryDetails() function main catch block', error);

    // Handle error response
    const errResult = commonLib.func.getError(error, 'PFF0021');
    throw new ApolloError(errResult.message, errResult.code);
  } finally {
    // Clean up database connections
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
};

/**
 * Get candidate main table data with filtering and ordering
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} args - Query arguments
 * @returns {Promise<Array>} - Array of records from candidate salary table
 */
async function getCandidateMainTableData(organizationDbConnection, args) {
  let query = organizationDbConnection(`${ehrTables.candidateSalaryDetails} as CSD`)
    .select(
      'CSD.*',
      'ST.Template_Name',
      organizationDbConnection.raw("CONCAT_WS(' ', CPI.Emp_First_Name, CPI.Emp_Middle_Name, CPI.Emp_Last_Name) as CandidateName"),
      organizationDbConnection.raw(`(
        SELECT CSA.Amount
        FROM ?? as CSA
        INNER JOIN ?? as AT ON CSA.Allowance_Type_Id = AT.Allowance_Type_Id
        LEFT JOIN salary_components as SC ON AT.Salary_Component_Id = SC.Component_Id
        WHERE CSA.Candidate_Id = CSD.Candidate_Id
        AND SC.Component_Code = ?
        limit 1
      ) as Basic_Pay`, [
        ehrTables.candidateSalaryAllowance,
        ehrTables.allowanceType,
        'basic_salary_amount'
      ])
    )
    .leftJoin(`${ehrTables.salaryTemplate} as ST`, 'CSD.Template_Id', 'ST.Template_Id')
    .leftJoin('candidate_personal_info as CPI', 'CSD.Candidate_Id', 'CPI.Candidate_Id');

  // Common joins for AddedBy/UpdatedBy
  query
    .leftJoin(`${ehrTables.empPersonalInfo} as EPI`, 'EPI.Employee_Id', 'CSD.Added_By')
    .leftJoin(`${ehrTables.empPersonalInfo} as EPI2`, 'EPI2.Employee_Id', 'CSD.Updated_By')
    .select([
      organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as AddedByName"),
      organizationDbConnection.raw("CONCAT_WS(' ', EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as UpdatedByName"),
    ]);

  // Conditional filtering
  if (args.candidateId) {
    query.where('CSD.Candidate_Id', args.candidateId);
  }

  if (args.templateId) {
    query.where('CSD.Template_Id', args.templateId);
  }

  // Ordering
  query.orderBy('CSD.Added_On', 'desc');

  const result = await query;
  return result.length > 0 ? result : [];
}



/**
 * Format candidate main table data
 * @param {Object} data - Raw data from database
 * @returns {Object} - Formatted data object
 */
function formatCandidateMainTableData(data) {
  return {
    'Candidate_Id': data.Candidate_Id,
    'Template_Id': data.Template_Id,
    'Template_Name': data.Template_Name,
    'CandidateName': data.CandidateName,
    'Annual_CTC': data.Annual_Ctc,
    'Annual_Gross_Salary': data.Annual_Gross_Salary,
    'Monthly_Gross_Salary': data.Monthly_Gross_Salary,
    'Salary_Effective_Month': data.Salary_Effective_Month,
    'Basic_Pay': data.Basic_Pay,
    'Effective_From': data.Effective_From,
    'Effective_To': data.Effective_To,
    'Added_On': data.Added_On,
    'Updated_On': data.Updated_On,
    'AddedByName': data.AddedByName,
    'UpdatedByName': data.UpdatedByName
  };
}

/**
 * Get candidate allowance components for multiple records in one query
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} candidateIds - Array of candidate IDs
 * @returns {Promise<Object>} - Object mapping candidate IDs to their allowance components
 */
async function getCandidateAllowanceComponentsByRecord(organizationDbConnection, candidateIds) {
  try {
    const allowanceTypeDetails = await organizationDbConnection
      .select(
        'CSA.*',
        'AT.Allowance_Amount as Org_Amount',
        'AT.*',
        'SC.Component_Code',
        organizationDbConnection.raw('GROUP_CONCAT(BA.Form_Id) as Form_Id'),
        organizationDbConnection.raw('AT.FBP_Max_Declaration_Amount as FBP_Max_Declaration'),
        organizationDbConnection.raw('GROUP_CONCAT(A2.Allowance_Type_Id) as Allowance_Ids'),
      )
      .from(`${ehrTables.candidateSalaryAllowance} as CSA`)
      .leftJoin(`${ehrTables.allowanceType} as AT`, 'AT.Allowance_Type_Id', 'CSA.Allowance_Type_Id')
      .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
      .leftJoin('allowance_type_benefit_association as BA',
        'AT.Allowance_Type_Id', 'BA.Allowance_Type_Id')
      .leftJoin(ehrTables.allowanceType + ' as A2', 'A2.Allowance_Type_Id', 'BA.Allowance_Type_Id')
      .leftJoin(ehrTables.ehrForms + ' as EF', 'EF.Form_Id', 'BA.Form_Id')
      .groupBy('CSA.Candidate_Id', 'CSA.Allowance_Type_Id')
      .whereIn('CSA.Candidate_Id', candidateIds);

    // Group allowance components by candidate ID
    const componentsByCandidate = {};
    for (let allowanceData of allowanceTypeDetails) {
      const candidateId = allowanceData.Candidate_Id;
      if (!componentsByCandidate[candidateId]) {
        componentsByCandidate[candidateId] = {
          Allowance: [],
          Fixed_Allowance: [],
          Bonus: [],
          Flexible_Benefit_Plan: [],
          Reimbursement: [],
          Basic_Pay: []
        };
      }

      const response = await commonFunctions.benefitAssociation(allowanceData);
      const mappedData = mapCandidateAllowanceData(response);



      if (allowanceData['Component_Code']?.toLowerCase() === 'fixed_allowance_amount') {
        componentsByCandidate[candidateId].Fixed_Allowance.push(mappedData);
      } else if (
        allowanceData['Is_Claim_From_Reimbursement'] && allowanceData['Is_Claim_From_Reimbursement'].toLowerCase() === 'yes'
      ) {
        componentsByCandidate[candidateId].Reimbursement.push(mappedData);
      }
      else if (
        allowanceData['Is_Flexi_Benefit_Plan'] && allowanceData['Is_Flexi_Benefit_Plan'].toLowerCase() === 'yes'
      ) {
        componentsByCandidate[candidateId].Flexible_Benefit_Plan.push(mappedData);
      }
      else if (allowanceData['Allowance_Mode'] && allowanceData['Allowance_Mode'].toLowerCase() === 'bonus') {
        componentsByCandidate[candidateId].Bonus.push(mappedData);
      }
      else if (allowanceData['Allowance_Mode'] && allowanceData['Allowance_Mode'].toLowerCase() === 'non bonus' &&
               allowanceData['Component_Code']?.toLowerCase() !== 'basic_salary_amount') {
        componentsByCandidate[candidateId].Allowance.push(mappedData);
      }
      else if (allowanceData['Component_Code']?.toLowerCase() === 'basic_salary_amount'){
        componentsByCandidate[candidateId].Basic_Pay.push(mappedData);
      }
    }

    return componentsByCandidate;
  } catch (error) {
    console.log('Error in getCandidateAllowanceComponentsByRecord catch block', error);
    throw error;
  }
}

/**
 * Get candidate retirals components for multiple records in one query
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} candidateIds - Array of candidate IDs
 * @returns {Promise<Object>} - Object mapping candidate IDs to their retirals components
 */
async function getCandidateRetiralsComponentsByRecord(organizationDbConnection, candidateIds) {
  try {
    const retiralsComponents = await organizationDbConnection
      .select(
        'CSR.*',
        'IC.Insurance_Name AS Insurance_Name',
        'IC.Insurance_Type AS Insurance_Type',
        'IC.Override_Insurance_Contribution_At_Employee_Level',
        'IC.Payment_Frequency',
        'EF.Form_Name as Retirals_Name',
        organizationDbConnection.raw('CASE WHEN CSR.Form_Id = 126 THEN PGS.Slab_Wise_NPS WHEN CSR.Form_Id = 52 THEN PGS.Slab_Wise_PF ELSE IC.Slab_Wise_Insurance END as Slab_Wise'),
        organizationDbConnection.raw('GROUP_CONCAT(A2.Allowance_Type_Id) as Allowance_Ids'),
        'PFP.Override_PF_Contribution_Rate_At_Employee_Level'
      )
      .from(ehrTables.candidateSalaryRetirals + ' as CSR')
      .leftJoin('allowance_type_benefit_association as BA', 'CSR.Form_Id', 'BA.Form_Id')
      .leftJoin(ehrTables.allowanceType + ' as A2', 'A2.Allowance_Type_Id', 'BA.Allowance_Type_Id')
      .innerJoin(ehrTables.ehrForms + ' as EF', 'EF.Form_Id', 'CSR.Form_Id')
      .leftJoin(ehrTables.insuranceConfiguration + ' as IC', 'IC.InsuranceType_Id', 'CSR.Retirals_Id')
      .leftJoin(ehrTables.payrollGeneralSettings + ' as PGS', organizationDbConnection.raw('1=1'))
      .leftJoin(ehrTables.providentFund + ' as PFP', organizationDbConnection.raw('1=1'))
      .whereIn('CSR.Candidate_Id', candidateIds)
      .groupBy('CSR.Candidate_Id', 'CSR.Form_Id', 'CSR.Retirals_Id');

    // Group retirals by candidate ID and add PF settings
    const retiralsByCandidate = {};
    for (let retiralsData of retiralsComponents) {
      const candidateId = retiralsData.Candidate_Id;
      if (!retiralsByCandidate[candidateId]) {
        retiralsByCandidate[candidateId] = [];
      }
      retiralsByCandidate[candidateId].push(retiralsData);
    }

    return retiralsByCandidate;
  } catch (error) {
    console.log('Error in getCandidateRetiralsComponentsByRecord catch block', error);
    throw error;
  }
}

/**
 * Get candidate gross components for multiple records in one query
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} candidateIds - Array of candidate IDs
 * @returns {Promise<Object>} - Object mapping candidate IDs to their gross components
 */
async function getCandidateGrossComponentsByRecord(organizationDbConnection, candidateIds) {
  try {
    const grossComponents = await organizationDbConnection
      .select(
        'CGC.*',
        'GC.Gross_Name',
        'GC.Display_Name',
        'GC.Calculation_Type',
        'GC.Custom_Formula',
        'GC.Salary_Component_Id',
        'GC.Description as Gross_Description',
        'SC.Component_Name as Salary_Component_Name',
        'SC.Component_Code as Salary_Component_Code',
        'SC.Component_Type as Salary_Component_Type'
      )
      .from(`${ehrTables.candidateGrossComponents} as CGC`)
      .innerJoin(`${ehrTables.grossConfiguration} as GC`, 'GC.Gross_Id', 'CGC.Gross_Id')
      .leftJoin(`${ehrTables.salaryComponents} as SC`, 'SC.Component_Id', 'GC.Salary_Component_Id')
      .whereIn('CGC.Candidate_Id', candidateIds)
      .where('GC.Status', 'Active')
      .orderBy('GC.Gross_Name', 'asc');

    // Group gross components by candidate ID
    const grossByCandidate = {};
    for (let grossData of grossComponents) {
      const candidateId = grossData.Candidate_Id;
      if (!grossByCandidate[candidateId]) {
        grossByCandidate[candidateId] = [];
      }

      const mappedData = mapCandidateGrossData(grossData);
      grossByCandidate[candidateId].push(mappedData);
    }

    return grossByCandidate;
  } catch (error) {
    console.log('Error in getCandidateGrossComponentsByRecord catch block', error);
    throw error;
  }
}

/**
 * Map candidate gross data to consistent format
 * @param {Object} response - Raw gross data
 * @returns {Object} - Formatted gross data
 */
function mapCandidateGrossData(response) {
  return {
    'Gross_Id': response['Gross_Id'],
    'Gross_Name': response['Gross_Name'],
    'Display_Name': response['Display_Name'],
    'Calculation_Type': response['Calculation_Type'],
    'Custom_Formula': response['Custom_Formula'],
    'Amount': response['Amount'],
    'Gross_Description': response['Gross_Description'],
    'Salary_Component_Id': response['Salary_Component_Id'],
    'Salary_Component_Name': response['Salary_Component_Name'],
    'Salary_Component_Code': response['Salary_Component_Code'],
    'Salary_Component_Type': response['Salary_Component_Type']
  };
}

/**
 * Map candidate allowance data to response format
 * @param {Object} allowanceData - Raw allowance data from database
 * @returns {Object} - Mapped allowance data
 */
function mapCandidateAllowanceData(allowanceData) {
  return {
    'Allowance_Type_Id': allowanceData.Allowance_Type_Id,
    'Allowance_Name': allowanceData.Allowance_Name,
    'Name_In_Payslip': allowanceData.Name_In_Payslip,
    'Allowance_Type': allowanceData.Calculation_Type,
    'Percentage': allowanceData.Percentage,
    'Amount': allowanceData.Amount,
    'Allowance_Wages': allowanceData.Allowance_Wages,
    'Period': allowanceData.Period,
    'Tax_Inclusion': allowanceData.Tax_Inclusion,
    'Formula_Based': allowanceData.Formula_Based, // Keep for backward compatibility
    'Component_Code': allowanceData.Component_Code,
    'Allowance_Mode': allowanceData.Allowance_Mode,
    'Is_Claim_From_Reimbursement': allowanceData.Is_Claim_From_Reimbursement,
    'Is_Flexi_Benefit_Plan': allowanceData.Is_Flexi_Benefit_Plan,
    'Is_Basic_Pay': allowanceData.Is_Basic_Pay, // Keep for backward compatibility
    'FBP_Max_Declaration': allowanceData.FBP_Max_Declaration,
    'Form_Id': allowanceData.Form_Id,
    'Custom_Formula': allowanceData.Custom_Formula,
    'Allowance_Ids': allowanceData.Allowance_Ids
  };
}

/**
 * Get round off settings from database
 * @param {Object} organizationDbConnection - Database connection
 * @returns {Promise<Array>} - Array of round off settings
 */
async function getRoundOffSettings(organizationDbConnection) {
  try {
    const roundOffSettings = await organizationDbConnection(ehrTables.payrollRoundOffSettings)
      .select('Round_Off_Settings_Id', 'Multiples_Of', 'Round_Off_For', 'Form_Id');
    return roundOffSettings || [];
  } catch (error) {
    console.error('Error in getRoundOffSettings:', error);
    throw error;
  }
}

// Export resolvers
const resolvers = {
  Query: {
    listCandidateSalaryDetails
  }
};

module.exports = { resolvers };
