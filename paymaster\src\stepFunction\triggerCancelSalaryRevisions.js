const { ApolloError, UserInputError } = require('apollo-server-lambda');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const moment = require('moment-timezone');
const { ehrTables } = require('../common/tablealias');
const {
  getWorkflowProcessInstanceId,
  deleteOldApprovalRecordsWithoutTrx
} = require('../common/commonfunctions');

/**
 * Trigger bulk salary revision cancellation via Step Function
 * @param {Object} parent - Parent object
 * @param {Object} args - Arguments containing revisionIds array
 * @param {Object} context - Context object containing user info
 * @returns {Object} - Response with trigger status
 */
module.exports.triggerCancelSalaryRevisions = async (parent, args, context, info) => {
    let organizationDbConnection;

    try {
        console.log('=== TRIGGERING BULK SALARY REVISION CANCELLATION STEP FUNCTION ===');

        const employeeId = context.logInEmpId;
        const userIp = context.userIp;
        const sessionId = employeeId;
        const orgCode = context.orgCode;

        // Validate employee ID
        if (!employeeId) {
            throw new Error('Employee_Id is required but not found in context');
        }

        // Create database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Validate input array
        if (!args.revisionIds || args.revisionIds.length === 0) {
            throw 'PST0034'; // No revision IDs provided for cancellation
        }

        // Check access rights for bulk cancellation
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            employeeId,
            null,
            '',
            'UI',
            false,
            args.formId
        );

        if (Object.keys(checkRights).length === 0 || checkRights.Role_Update === 0) {
            throw '_DB0102'; // No update access
        }

        // Get all revision records to determine cancellation strategy
        const revisions = await organizationDbConnection(ehrTables.salaryRevisionDetails)
            .select('Revision_Id', 'Revision_Status', 'Employee_Id')
            .whereIn('Revision_Id', args.revisionIds);

        // Validate all revisions exist
        if (revisions.length !== args.revisionIds.length) {
            throw 'PST0014'; // Some revision records not found
        }

        // Extract employee IDs from revisions
        const employeeIds = revisions.map(r => r.Employee_Id);

        // Validate each revision
        for (const revision of revisions) {
            // Check status is Applied or Approved
            const status = revision.Revision_Status.toLowerCase();
            if (status !== 'applied' && status !== 'approved') {
                throw 'PST0036'; // Can only cancel Applied or Approved revisions
            }

            // Check if already in cancellation process
            if (status === 'cancel in progress' || status === 'cancel applied' || status === 'cancel failed') {
                throw 'PST0036'; // Revision already in cancellation process
            }

            // Validate no payroll processed after revision effective date
            await validateNoPayrollProcessedAfterRevision(
                organizationDbConnection,
                revision.Revision_Id
            );
        }

        // Determine cancellation strategy
        const allApplied = revisions.every(r => r.Revision_Status.toLowerCase() === 'applied');
        const currentTimestamp = moment().utc().format('YYYY-MM-DD HH:mm:ss');

        if (allApplied) {
            // DIRECT CANCELLATION - All revisions are "Applied", cancel directly without workflow

            // Delete old approval records for each revision
            for (const revision of revisions) {
                const responseObject = await getWorkflowProcessInstanceId(
                    organizationDbConnection,
                    revision.Revision_Id
                );

                if (responseObject && responseObject[0]?.Process_Instance_Id) {
                    await deleteOldApprovalRecordsWithoutTrx(
                        organizationDbConnection,
                        responseObject[0].Process_Instance_Id
                    );
                }
            }

            // Update all revisions to "Cancelled"
            await organizationDbConnection(ehrTables.salaryRevisionDetails)
                .whereIn('Revision_Id', args.revisionIds)
                .update({
                    Revision_Status: 'Cancelled',
                    Updated_On: currentTimestamp,
                    Updated_By: employeeId
                });

            // Create system log
            await commonLib.func.createSystemLogActivities({
                userIp,
                employeeId: employeeId,
                organizationDbConnection,
                message: `Bulk salary revision cancellation completed directly: ${args.revisionIds.length} revisions cancelled (all Applied status)`
            });

            return {
                errorCode: '',
                message: `All ${args.revisionIds.length} revisions cancelled successfully (direct cancellation - no workflow required)`,
                cancellationStrategy: 'DIRECT',
                totalCount: args.revisionIds.length
            };

        } else {
            // WORKFLOW CANCELLATION - Some revisions are "Approved", use step function
            // Update all revisions to "Cancel In Progress"
            await organizationDbConnection(ehrTables.salaryRevisionDetails)
                .whereIn('Revision_Id', args.revisionIds)
                .update({
                    Revision_Status: 'Cancel In Progress',
                    Updated_On: currentTimestamp,
                    Updated_By: employeeId
                });

            // Prepare Step Function input
            const stepFunctionInput = {
                revisionIds: args.revisionIds,
                employeeIds: employeeIds,
                employeeId: employeeId,
                sessionId: sessionId,
                userIp: userIp,
                timestamp: moment.utc().toISOString(),
                requestId: context.requestId || `cancel-salary-revisions-${Date.now()}`,
                orgCode: orgCode,
                formId: args.formId
            };

            // Trigger Step Function for asynchronous processing
            await commonLib.stepFunctions.triggerStepFunction(
                process.env.processCancelSalaryRevisionsFunction,
                'processCancelSalaryRevisionsFunction',
                '',
                stepFunctionInput
            );

            return {
                errorCode: '',
                message: `Bulk salary revision cancellation initiated successfully for ${args.revisionIds.length} revision(s). Processing will continue asynchronously.`,
                cancellationStrategy: 'WORKFLOW',
                totalCount: args.revisionIds.length
            };
        }

    } catch (error) {
        console.error('Error triggering bulk salary revision cancellation:', error);

        // If revisions were updated to "Cancel In Progress", revert them to original status
        if (organizationDbConnection && args.revisionIds && args.revisionIds.length > 0) {
            try {
                // Update failed revisions to "Cancel Failed" status
                await organizationDbConnection(ehrTables.salaryRevisionDetails)
                    .whereIn('Revision_Id', args.revisionIds)
                    .where('Revision_Status', 'Cancel In Progress')
                    .update({
                        Revision_Status: 'Cancel Failed',
                        Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                        Updated_By: context.logInEmpId || 0
                    });
            } catch (updateError) {
                console.error('Error updating revision status to Cancel Failed:', updateError);
            }
        }

        const errResult = commonLib.func.getError(error, 'PST0035');

        // Custom error messages
        if (error === 'PST0034') {
            errResult.message = 'Bulk cancellation already in progress for one or more revisions';
        } else if (error === 'PST0029') {
            errResult.message = 'Cannot cancel revision. Payroll has been processed after revision effective date';
        }

        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        // Always destroy database connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
};

/**
 * Validate that no payroll has been processed after the revision payout month
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} revisionId - Revision ID
 */
async function validateNoPayrollProcessedAfterRevision(organizationDbConnection, revisionId) {
    try {
        // Get revision details to check payout month
        const revisionDetails = await organizationDbConnection(ehrTables.salaryRevisionDetails)
            .select('Payout_Month', 'Employee_Id')
            .where('Revision_Id', revisionId)
            .first();

        if (!revisionDetails || !revisionDetails.Payout_Month) {
            return; // Skip validation if no payout month found
        }

        // Get employee salary type to determine which payslip table to check
        const employeeSalaryType = await commonLib.payroll.getEmployeeSalaryType(
            organizationDbConnection,
            revisionDetails.Employee_Id
        );
        const payslipTable = employeeSalaryType && employeeSalaryType.toLowerCase() === 'monthly'
            ? ehrTables.salaryPayslip
            : ehrTables.hourlywagesPayslip;

        // Parse payout month (format: "M,YYYY")
        const [payoutMonth, payoutYear] = revisionDetails.Payout_Month.split(',');
        const payoutDate = moment(`${payoutYear}-${payoutMonth.padStart(2, '0')}-01`);

        // Check for any payslips processed after the payout month
        // Salary_Month format is "M,YYYY" - need to parse and compare
        const payslipsAfterRevision = await organizationDbConnection(payslipTable)
            .select('Payslip_Id')
            .where('Employee_Id', revisionDetails.Employee_Id)
            .whereRaw(
                `STR_TO_DATE(CONCAT(
                    SUBSTRING_INDEX(Salary_Month, ',', -1),
                    '-',
                    LPAD(SUBSTRING_INDEX(Salary_Month, ',', 1), 2, '0'),
                    '-01'
                ), '%Y-%m-%d') > ?`,
                [payoutDate.format('YYYY-MM-DD')]
            )
            .limit(1);

        if (payslipsAfterRevision.length > 0) {
            throw 'PST0040'; // Cannot cancel - payroll processed after revision payout month
        }

        return true;
    } catch (error) {
        throw error;
    }
}

