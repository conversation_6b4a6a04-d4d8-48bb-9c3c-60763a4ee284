// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formId } = require('../common/appconstants');
const moment = require('moment');

//function to generate indonesia report
let organizationDbConnection;
module.exports.generateIndonesiaReports = async (parent, args, context, info) => {
    try {
        console.log("Inside generateIndonesiaReports function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // check their rights
        // let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, 'Role_View', 'Back-end', null, formId.pfId);
        // if (!checkRights) {
        //     throw '_DB0100';
        // }

        if (args.reportType === 'BPJSTK Exit') {

            // First day of the month
            const startDate = moment(`${args.year}-${args.month}`, "YYYY-MM").startOf('month').format("YYYY-MM-DD");
            // Last day of the month
            const endDate = moment(`${args.year}-${args.month}`, "YYYY-MM").endOf('month').format("YYYY-MM-DD");

            let exitReportResult = await organizationDbConnection(ehrTables.empResignation + ' as ER')
                .select('EJ.User_Defined_EmpId as Employee_Identification_Number', 'ER.Reason_Id as Reason_Id',
                    organizationDbConnection.raw("CASE WHEN EPI.Aadhaar_Card_Number IS NOT NULL THEN EPI.Aadhaar_Card_Number ELSE '' END as Population_Identification"),
                    organizationDbConnection.raw(" \'\' as Participant_Number"),
                    organizationDbConnection.raw("CASE WHEN SD.Monthly_Gross_Salary IS NOT NULL THEN SD.Monthly_Gross_Salary ELSE 0 END as Final_Wages"),
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Full_Name_Worker"),
                    "EPI.DOB as Birth_Date")
                .join(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'ER.Employee_Id')
                .leftJoin(ehrTables.salaryDetails + ' as SD', 'SD.Employee_Id', 'ER.Employee_Id')
                .join(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EPI.Employee_Id')
                .where('ER.Approval_Status', 'Approved')
                .andWhereBetween('ER.Resignation_Date', [startDate, endDate]);

            let exitReportHeaders = [
                { key: "Auto_Increment", header: "Number" },
                { key: "Employee_Identification_Number", header: "Employee Identification Number" },
                { key: "Participant_Number", header: "Participant's Number" },
                { key: "Population_Identification", header: "Population Identification Number (NIK) / Passport Number (for Foreign TK)" },
                { key: "Full_Name_Worker", header: "Full Name of Worker (According to the KTP, the title/title is placed at the back)" },
                { key: "Final_Wages", header: "Final Wages (in IDR)" },
                { key: "Birth_Date", header: "Date of Birth (Date/Month/Year)" },
                { key: "Reason_Id", header: "Information" },
            ]

            exitReportResult = exitReportResult.map((item, index) => {
                return {
                    ...item,
                    Auto_Increment: index + 1 // Adding 1 to start sequence from 1
                };
            });

            return { errorCode: "", message: "BPJSTk exit report has been fetched successfully.", headers: JSON.stringify(exitReportHeaders), reportData: JSON.stringify(exitReportResult) }
        }

        //Retrieve report details
        let employeeETFDetails = args.reportType === 'Employee BPJSTK' || args.reportType === 'BPJSTK Mutation' ? await organizationDbConnection(ehrTables.empEtfPayment)
            .select('Org_ShareAmount as NPS_Org_ShareAmount', 'Employee_Id',
                'Emp_ShareAmount as NPS_Emp_ShareAmount', 'Salary_Month as NPS_Salary_Month')
            .where(function () {
                if (args.month && args.year) {
                    let salaryMonth = `${args.month},${args.year}`;
                    this.where('Salary_Month', salaryMonth)

                } else if (args.year) {
                    let arrayOfSalaryMonths = getMonthsForYear(args.year);
                    this.whereIn('Salary_Month', arrayOfSalaryMonths)
                }
            }) : []

        let employeePFDetails = args.reportType === 'Employee BPJSTK' || args.reportType === 'BPJSTK Pension' || args.reportType === 'BPJSTK Mutation' ? await organizationDbConnection(ehrTables.empPfPayment)
            .select('Org_ShareAmount as PF_Org_ShareAmount', 'Employee_Id',
                'Emp_ShareAmount as PF_Emp_ShareAmount', 'Salary_Month as PF_Salary_Month')
            .where(function () {
                if (args.month && args.year) {
                    let salaryMonth = `${args.month},${args.year}`;
                    this.where('Salary_Month', salaryMonth)

                } else if (args.year) {
                    let arrayOfSalaryMonths = getMonthsForYear(args.year);
                    this.whereIn('Salary_Month', arrayOfSalaryMonths)
                }
            }) : []

        let insuranceTypeDetails = args.reportType === 'Employee BPJSTK' || args.reportType === 'BPJSTK Contribution' || args.reportType === 'BPJSTK Mutation' ? await organizationDbConnection(ehrTables.empInsurancePayment + " as EIP")
            .select(
                'EIP.Employee_Id', 'EIP.Salary_Month',
                'EIP.Org_ShareAmount as Insurance_Org_ShareAmount', 'EIP.Emp_ShareAmount as Insurance_Emp_ShareAmount',
                'IT.Insurance_Name',
            )
            .innerJoin(ehrTables.insuranceType + " as IT", "IT.InsuranceType_Id", "EIP.InsuranceType_Id")
            .where(function () {
                if (args.month && args.year) {
                    let salaryMonth = `${args.month},${args.year}`;
                    this.where('EIP.Salary_Month', salaryMonth)
                } else if (args.year) {
                    let arrayOfSalaryMonths = getMonthsForYear(args.year);
                    this.whereIn('EIP.Salary_Month', arrayOfSalaryMonths)
                }
            }) : []

        let uniqueEmployeeIds = employeeETFDetails.map((el) => el.Employee_Id).concat(insuranceTypeDetails.map((el) => el.Employee_Id)).concat(employeePFDetails.map((el) => el.Employee_Id))

        uniqueEmployeeIds = [...new Set(uniqueEmployeeIds)];

        let employeePersonalDetails = await organizationDbConnection(ehrTables.empPersonalInfo + " as EPI")
            .select(organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Name"),
                organizationDbConnection.raw(`CASE WHEN Gender = 'Male' THEN 'M' WHEN Gender = 'Female' THEN 'F' WHEN Gender = 'Others' THEN 'O' END as Gender`),
                'EPI.UAN', 'EPI.Aadhaar_Card_Number', 'EPI.DOB', 'SD.Monthly_Gross_Salary', 'SP.Salary_Month as SP_Salary_Month', 'EPI.Employee_Id', 'SP.Basic_Salary'
            )
            .innerJoin(ehrTables.salaryPayslip + " as SP", "SP.Employee_Id", "EPI.Employee_Id")
            .innerJoin(ehrTables.salaryDetails + " as SD", "SD.Employee_Id", "EPI.Employee_Id")
            .whereIn('EPI.Employee_Id', uniqueEmployeeIds)


        let { formedData, headers } = formReportData(employeeETFDetails, employeePFDetails, insuranceTypeDetails, employeePersonalDetails, args.reportType, args.year)
        
        return { errorCode: "", message: "Report was generated successfully.", headers: JSON.stringify(headers), reportData: JSON.stringify(formedData) };

    }
    catch (e) {
        console.log('Error in generateIndonesiaReports function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'ODM0018');
        throw new ApolloError(errResult.message, errResult.code);
    }
    finally {
        //Destroy DB connection
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}

function formReportData(employeeETFDetails, employeePFDetails, insuranceTypeDetails, employeePersonalDetails, reportType, year) {
    try {
        let headers = [];
        let result = [];
        let autoIncrement = 1; // Initialize the auto-increment counter

        if (reportType === 'BPJSTK Contribution') {
            headers = [
                { key: "Auto_Increment", header: "No." },
                { key: "Name", header: "Name" },
                { key: "Gopak_Retired", header: "Gapok/Retired" },
                { key: "Fixed_Allowance", header: "Fixed Allowance" },
                { key: "Total_Salary", header: "Total Salary" },
                { key: "Calculation", header: "Basis for Contribution Calculation" },
                { key: "Org_ShareAmount", header: "Company" },
                { key: "Emp_ShareAmount", header: "Employee" },
                { key: "Total", header: "Total Contribution" }
            ];

            // Organize insuranceTypeDetails by Employee_Id and Insurance_Name
            let data = organizeDataByEmployeeIdAndDate(insuranceTypeDetails, 'Employee_Id', 'Insurance_Name');

            employeePersonalDetails.forEach((element) => {
                // Create the key for the formed data
                let key = `${element.Employee_Id}|BPJS Health Care`;

                // Retrieve the formed data
                let formedData = ensureArray(data, key);

                // Check if formedData contains valid items
                if (formedData && formedData.length > 0) {
                    let firstItem = formedData[0]; // Access the first item of formedData array
                    let shareEmpAmount = firstItem.Insurance_Emp_ShareAmount !== null ? firstItem.Insurance_Emp_ShareAmount : 0;
                    let shareOrgAmount = firstItem.Insurance_Org_ShareAmount !== null ? firstItem.Insurance_Org_ShareAmount : 0;

                    // Create the form object
                    let formObj = {
                        'Auto_Increment': autoIncrement++, // Increment the counter for each entry
                        'Name': element.Name,
                        'Gopak_Retired': element.Basic_Salary,
                        'Fixed_Allowance': 0,
                        'Total_Salary': element.Basic_Salary,
                        'Calculation': element.Basic_Salary,
                        'Org_ShareAmount': shareOrgAmount,
                        'Emp_ShareAmount': shareEmpAmount,
                        'Total': shareOrgAmount + shareEmpAmount
                    };

                    // Push the form object into the result array
                    result.push(formObj);
                }
            });
        }
        else if (reportType === 'Employee BPJSTK') {
            headers = [
                { key: "Auto_Increment", header: "No." },
                { key: "Participant_Number", header: "Participant's Number" },
                { key: "Worker_Name", header: "Worker Name" },
                { key: "DOB", header: "Date Of Birth" },
                { key: "Wages", header: "Wages" },
                { key: "Arrears", header: "Arrears" },
                { key: "Total", header: "Total" },
                { key: "JKK_Contribution", header: "JKK Contribution (Rp)" }, //BPJS Working Accident Protection
                { key: "JKM_Contribution", header: "JKM Contribution (Rp)" },
                { key: "JHT_Contribution_Employer", header: "JHT Contribution Employer (Rp)" },
                { key: "JHT_Contribution_Worker", header: "JHT Contribution Worker (Rp)" },
                { key: "JP_Contribution_Employer", header: "JP Contribution Employer (Rp)" },
                { key: "JP_Contribution_Worker", header: "JP Contribution Worker (Rp)" },
                { key: "Amount_Of_Contribution", header: "Amount of Contribution (Rp)" },
            ];

            // Organize insuranceTypeDetails by Employee_Id and Insurance_Name
            let insuranceData = organizeDataByEmployeeIdAndDate(insuranceTypeDetails, 'Employee_Id', 'Insurance_Name');
            let pfData = organizeDataByEmployeeIdAndDate(employeePFDetails, 'Employee_Id')
            let npsData = organizeDataByEmployeeIdAndDate(employeeETFDetails, 'Employee_Id')

            employeePersonalDetails.forEach((element) => {
                // Create the key for the formed data
                let jkkContributionKey = `${element.Employee_Id}|BPJS Working Accident Protection`;
                // Retrieve the formed data
                let jkkContributionData = ensureArray(insuranceData, jkkContributionKey);
                let jkkContributionAmount = 0
                // Check if formedData contains valid items
                if (jkkContributionData && jkkContributionData.length > 0) {
                    let firstItem = jkkContributionData[0]; // Access the first item of formedData array
                    jkkContributionAmount = firstItem.Insurance_Org_ShareAmount !== null ? firstItem.Insurance_Org_ShareAmount : 0;
                }

                // Create the key for the formed data
                let jkmContributionKey = `${element.Employee_Id}|BPJS Life Insurance`;
                // Retrieve the formed data
                let jkmContributionData = ensureArray(insuranceData, jkmContributionKey);
                let jkmContributionAmount = 0
                // Check if formedData contains valid items
                if (jkmContributionData && jkmContributionData.length > 0) {
                    let firstItem = jkmContributionData[0]; // Access the first item of formedData array
                    jkmContributionAmount = firstItem.Insurance_Org_ShareAmount !== null ? firstItem.Insurance_Org_ShareAmount : 0;
                }

                let jhtContributionData = ensureArray(pfData, element.Employee_Id);
                let jhtContributionWorkerAmount = 0, jhtContributionEmployerAmount = 0
                // Check if formedData contains valid items
                if (jhtContributionData && jhtContributionData.length > 0) {
                    let firstItem = jhtContributionData[0]; // Access the first item of formedData array
                    jhtContributionEmployerAmount = firstItem.PF_Org_ShareAmount !== null ? firstItem.PF_Org_ShareAmount : 0;
                    jhtContributionWorkerAmount = firstItem.PF_Emp_ShareAmount !== null ? firstItem.PF_Emp_ShareAmount : 0;
                }


                let jpContributionData = ensureArray(npsData, element.Employee_Id);
                let jpContributionWorkerAmount = 0, jpContributionEmployerAmount = 0
                // Check if formedData contains valid items
                if (jpContributionData && jpContributionData.length > 0) {
                    let firstItem = jpContributionData[0]; // Access the first item of formedData array
                    jpContributionEmployerAmount = firstItem.NPS_Org_ShareAmount !== null ? firstItem.NPS_Org_ShareAmount : 0;
                    jpContributionWorkerAmount = firstItem.NPS_Emp_ShareAmount !== null ? firstItem.NPS_Emp_ShareAmount : 0;
                }


                // Create the form object
                let formObj = {
                    'Auto_Increment': autoIncrement++, // Increment the counter for each entry
                    'Participant_Number': element.Employee_Id, // Assuming Employee_Id is the participant number
                    'Worker_Name': element.Name,
                    'DOB': element.DOB,
                    'Wages': element.Basic_Salary, // Assuming Basic_Salary is the wages
                    'Arrears': 0, // Default value for Arrears, modify if necessary
                    'Total': element.Basic_Salary,
                    'JKK_Contribution': jkkContributionAmount, // Set these fields based on your data if available
                    'JKM_Contribution': jkmContributionAmount,
                    'JHT_Contribution_Employer': jhtContributionEmployerAmount,
                    'JHT_Contribution_Worker': jhtContributionWorkerAmount,
                    'JP_Contribution_Employer': jpContributionEmployerAmount,
                    'JP_Contribution_Worker': jpContributionWorkerAmount,
                    'Amount_Of_Contribution': jkkContributionAmount + jkmContributionAmount + jhtContributionEmployerAmount + jhtContributionWorkerAmount + jpContributionEmployerAmount + jpContributionWorkerAmount,
                };

                // Push the form object into the result array
                result.push(formObj);
            });
        }
        else if (reportType === 'BPJSTK Pension') {
            headers = [
                { key: "Auto_Increment", header: "No." },
                { key: "Name", header: "Name" },
                { key: "FD_Register_No", header: "FDRegisterNo" },
                { key: "Gender", header: "M/F" },
                { key: "DOB", header: "Birth Date" },
                { key: "UAN", header: "Social Security No" },
                { key: "January", header: "January" },
                { key: "February", header: "February" },
                { key: "March", header: "March" },
                { key: "April", header: "April" },
                { key: "May", header: "May" },
                { key: "June", header: "June" },
                { key: "July", header: "July" },
                { key: "August", header: "August" },
                { key: "September", header: "September" },
                { key: "October", header: "October" },
                { key: "November", header: "November" },
                { key: "December", header: "December" },
                { key: "Total", header: "Total" }
            ]

            let pfData = organizeDataByEmployeeIdAndDate(employeePFDetails, 'Employee_Id', 'PF_Salary_Month')

            employeePersonalDetails.forEach((element) => {
                let months = { 1: 'January', 2: 'February', 3: 'March', 4: 'April', 5: 'May', 6: 'June', 7: 'July', 8: 'August', 9: 'September', 10: 'October', 11: 'November', 12: 'December' }

                let formObj = {
                    'Auto_Increment': autoIncrement++,
                    'Name': element.Name,
                    'FD_Register_No': 0,
                    'Gender': element.Gender,
                    'DOB': element.DOB,
                    'UAN': element.UAN ? element.UAN : '-',
                };

                let totalContributionAmount = 0

                for (let key in months) {
                    let totalPFAmount = 0
                    if (months.hasOwnProperty(key)) {
                        let monthId = key;
                        let keyString = `${element.Employee_Id}|${monthId},${year}`;
                        let formedData = ensureArray(pfData, keyString);

                        if (formedData && formedData.length > 0) {
                            let firstItem = formedData[0];
                            let empShareAmount = firstItem.PF_Emp_ShareAmount !== null ? firstItem.PF_Emp_ShareAmount : 0;
                            let orgShareAmount = firstItem.PF_Org_ShareAmount !== null ? firstItem.PF_Org_ShareAmount : 0;
                            totalPFAmount = empShareAmount + orgShareAmount;
                        } else {
                            totalPFAmount = 0
                        }
                        formObj[months[monthId]] = totalPFAmount
                        totalContributionAmount = totalContributionAmount + totalPFAmount
                    }
                }
                formObj.Total = totalContributionAmount;
                result.push(formObj);

            })
        } else {
            headers = [
                { key: "Auto_Increment", header: "No." },
                { key: "Name", header: "Name" },
                { key: "FD_Register_No", header: "FDRegisterNo" },
                { key: "Gender", header: "M/F" },
                { key: "DOB", header: "Birth Date" },
                { key: "UAN", header: "Social Security No" },
                { key: "No_Jamsostek", header: "No.Jamsostek" },
                { key: "January", header: "January" },
                { key: "February", header: "February" },
                { key: "March", header: "March" },
                { key: "April", header: "April" },
                { key: "May", header: "May" },
                { key: "June", header: "June" },
                { key: "July", header: "July" },
                { key: "August", header: "August" },
                { key: "September", header: "September" },
                { key: "October", header: "October" },
                { key: "November", header: "November" },
                { key: "December", header: "December" },
                { key: "Total", header: "Total" }
            ]

            let insuranceData = organizeDataByEmployeeIdAndDate(insuranceTypeDetails, 'Employee_Id', 'Salary_Month');
            let pfData = organizeDataByEmployeeIdAndDate(employeePFDetails, 'Employee_Id')
            let npsData = organizeDataByEmployeeIdAndDate(employeeETFDetails, 'Employee_Id')

            employeePersonalDetails.forEach((element) => {
                let months = { 1: 'January', 2: 'February', 3: 'March', 4: 'April', 5: 'May', 6: 'June', 7: 'July', 8: 'August', 9: 'September', 10: 'October', 11: 'November', 12: 'December' }
                let formObj = {
                    'Auto_Increment': autoIncrement++,
                    'Name': element.Name,
                    'FD_Register_No': 0,
                    'Gender': element.Gender,
                    'DOB': element.DOB,
                    'UAN': element.UAN,
                    'No_Jamsostek': 0,
                };
                let employeeTotalContributionAmount = 0
                for (let key in months) {
                    let pfTotalAmount = 0, npsTotalAmount = 0, totalInsuranceAmount = 0, monthTotalContributionAmount = 0
                    if (months.hasOwnProperty(key)) {
                        let monthId = key;
                        let keyString = `${element.Employee_Id}|${monthId},${year}`;
                        let pfFormedData = ensureArray(pfData, keyString);
                        if (pfFormedData && pfFormedData.length > 0) {
                            let firstItem = pfFormedData[0];
                            let empPFShareAmount = firstItem.PF_Emp_ShareAmount !== null ? firstItem.PF_Emp_ShareAmount : 0;
                            let orgPFShareAmount = firstItem.PF_Org_ShareAmount !== null ? firstItem.PF_Org_ShareAmount : 0;
                            pfTotalAmount = empPFShareAmount + orgPFShareAmount;
                        }
                        //NPS
                        let npsFormedData = ensureArray(npsData, keyString);
                        if (npsFormedData && npsFormedData.length > 0) {
                            let firstItem = npsFormedData[0];
                            let empNPSShareAmount = firstItem.NPS_Emp_ShareAmount !== null ? firstItem.NPS_Emp_ShareAmount : 0;
                            let orgNPSShareAmount = firstItem.NPS_Org_ShareAmount !== null ? firstItem.NPS_Org_ShareAmount : 0;
                            npsTotalAmount = empNPSShareAmount + orgNPSShareAmount;
                        }
                        //BPJS Working Accident Protection
                        let allInsuranceData = ensureArray(insuranceData, keyString);
                        if (allInsuranceData && allInsuranceData.length > 0) {
                            for(let insurance of allInsuranceData){
                                if(insurance.Insurance_Name === 'BPJS Working Accident Protection' || insurance.Insurance_Name === 'BPJS Life Insurance'){
                                    let firstItem = insurance;
                                    let empInsuranceShareAmount = firstItem.Insurance_Emp_ShareAmount !== null ? firstItem.Insurance_Emp_ShareAmount : 0;
                                    let orgInsuranceShareAmount = firstItem.Insurance_Org_ShareAmount !== null ? firstItem.Insurance_Org_ShareAmount : 0;
                                    totalInsuranceAmount = totalInsuranceAmount + orgInsuranceShareAmount + empInsuranceShareAmount;
                                }
                            }
                        }

                        monthTotalContributionAmount = pfTotalAmount + npsTotalAmount + totalInsuranceAmount

                        formObj[months[monthId]] = monthTotalContributionAmount
                        employeeTotalContributionAmount = employeeTotalContributionAmount + monthTotalContributionAmount
                    }
                }

                formObj['Total'] = employeeTotalContributionAmount;
                result.push(formObj);
            });
        }
        // Return the result and headers
        return { formedData: result, headers: headers };
    } catch (err) {
        console.log('Error in formReportData', err);
        throw err;
    }
}

function getMonthsForYear(year) {
    const months = [];
    for (let month = 1; month <= 12; month++) {
        months.push(`${month},${year}`);
    }
    return months;
}

function organizeDataByEmployeeIdAndDate(data, employeeIdKey, dateKey = null) {
    let organizedData = {};

    data.forEach(item => {
        let employeeId = item[employeeIdKey];
        let key;

        if (dateKey) {
            let date = item[dateKey];
            key = `${employeeId}|${date}`;
        } else {
            key = employeeId;
        }

        if (!organizedData[key]) {
            organizedData[key] = [];
        }

        organizedData[key].push(item);
    });

    return organizedData;
}

function ensureArray(obj, key) {
    if (obj.hasOwnProperty(key)) {
        return Array.isArray(obj[key]) ? obj[key] : [obj[key]];
    }
    return null;
}
