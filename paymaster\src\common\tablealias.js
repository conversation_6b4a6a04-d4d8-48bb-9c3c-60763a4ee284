/** Alias name for ehr tables */
module.exports.ehrTables = {
    location        : 'location',
    salaryTemplate  : 'salary_template',
    allowanceType   : 'allowance_type',
    allowances      : 'allowances',
    grossConfiguration: 'gross_configuration',
    templateAllowanceComponents : 'template_allowance_components',
    templateRetiralComponents   : 'template_retiral_components',
    forms : 'forms',
    ehrForms : 'ehr_forms',
    benefitForms: 'benefit_forms',
    perquisites: 'perquisites',
    customizationForms : 'customization_forms',
    variableInsurance : 'variable_insurance',
    fixedInsurance : 'fixed_insurance',
    insuranceType : 'insurance_type',
    fixedHealthInsurance : 'fixed_health_insurance',
    fixedHealthInsuranceType : 'fixed_health_insurance_type',
    unitData : 'unit_data',
    employeeSalaryDetails: 'employee_salary_details',
    orgEtf:'org_etf',
    insurancetypeGrade:'insurancetype_grade',
    gratuitySettings:'gratuity_settings',
    orgPf:'org_pf',
    employeeSalaryAllowance:'employee_salary_allowance',
    employeeSalaryRetirals:'employee_salary_retirals',

    // Candidate Salary Tables
    candidateSalaryDetails: 'candidate_salary_details',
    candidateSalaryAllowance: 'candidate_salary_allowance',
    candidateSalaryRetirals: 'candidate_salary_retirals',
    candidateGrossComponents: 'candidate_gross_components',
    candidateOneTimeEarnings: 'candidate_one_time_earnings',

    // Employee One Time Earnings
    employeeOneTimeEarnings: 'employee_one_time_earnings',

    designation: 'designation',
    empJob: 'emp_job',
    employeeSalaryConfiguration:'employee_salary_configuration',
    allowanceBenefitAssociation:'allowance_type_benefit_association',
    dataSetupDashboard:'datasetup_dashboard',
    salaryIncentive:'taxable_earnings',
    taxableEarnings:'taxable_earnings',
    nontaxableEarnings:'nontaxable_earnings',
    salaryPayslip:'salary_payslip',
    bwdSalaryPayslip: 'bwd_salary_payslip',
    hourlywagesPayslip:'hourlywages_payslip',
    salaryDeduction : 'salary_deduction',
    hourlywageDeduction : 'hourlywage_deduction',
    overtimeDetails:'overtime_details',

    providentFund: 'provident_fund',
    orgDetails: 'org_details',
    taxConfiguration: 'tax_configuration',
    empLeaves: 'emp_leaves',
    leaveTypes: 'leave_types',
    empPfPayment:'emp_pf_payment',
    empInsurancePayment: 'emp_insurance_payment',
    empEtfPayment: 'emp_etf_payment',
    empResignation: 'emp_resignation',
    empInsurance: 'emp_insurancepolicyno',
    city: 'city',
    state: 'state',
    country:'country',
    tdsPayment: 'tds_payment',
    tdsPaymentTracker: 'tds_payment_tracker',
    empTdsHistory: 'emp_tds_history',
    salaryDetails: 'salary_details',
    monthlyForm16Snapshot:'monthly_form16_snapshot',
    contactDetails:'contact_details',
    insuranceConfiguration:'insurance_configuration',
    payrollGeneralSettings:'payroll_general_settings',
    salaryRevisionDetails:'salary_revision_details',
    salaryRevisionAllowance:'salary_revision_allowance',
    salaryRevisionRetirals:'salary_revision_retirals',
    philHealthSlabs:'philhelath_slabs',
    benefitForms:'benefit_forms',
    providentFundSettings:'provident_fund_settings',
    payrollRoundOffSettings:'payroll_round_off_settings',
    socialSecurityScheme:'social_security_scheme_slabs',
    npsSlab:'nps_slabs',
    insuranceContributionConfiguration:'insurance_contribution_configuration',
    workflows:'workflows',
    workflowModule:'workflow_module',
    esiStatutoryConfiguration:'esi_statutory_configuration',
    payslipRetiralsRevisionDetails:'payslip_retirals_revision_details',
    revisionPayslipDetails:'revision_payslip_details',
    payslipRetiralsRevisionDetails:'payslip_retirals_revision_details',
    taUserTask: 'ta_user_task',
    taProcessInstance: 'ta_process_instance',
    taProcessInstanceHistory: 'ta_process_instance_history',
    taWorkFlow: 'ta_workflow',
    taUserTaskHistory: 'ta_user_task_history',
    expenseTypes:'expense_head',
    perquisiteTracker:'perquisite_tracker',
    perquisiteTrackerAmt:'perquisite_tracker_amount',
    empBonus: 'emp_bonus',
    compensatoryOffBalance: 'compensatory_off_balance',
    hourlyWages: 'hourly_wages',
    salary: 'salary_details',
    empPersonalInfo: 'emp_personal_info',
    empGrade: 'emp_grade',
    npsRules: 'nps_rules',
    candidateRecruitmentInfo: 'candidate_recruitment_info',
    salaryComponents: 'salary_components',
    templateGrossComponents: 'template_gross_components',
    revisionGrossComponents: 'revision_gross_components',
    salaryGrossComponents: 'salary_gross_components',
    salaryHistoryGrossComponents: 'salary_history_gross_components',
    sectionInvestmentCategory: 'section_investment_category',
    onetimeEarningTypes: 'onetime_earning_types',
    adhocAllowance: 'adhoc_allowance',
    adhocAllowanceBenefitAssociation: 'adhoc_allowance_benefit_association',
    employeeSalaryHistory: 'employee_salary_history'
};