// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const { ApolloError,UserInputError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const validation = require('../formvalidations/validation');
const {
  getWorkflowProcessInstanceId,
  initaiteWorkflow,
  getEventId,
  deleteOldApprovalRecordsWithoutTrx,
  checkSalaryMigrationAllowed,
  getFiscalMonthYear
} = require('../common/commonfunctions');
const { calculateSalary } = require('../roresolvers/salary/calculateSalary');
const { calculateSalaryArrears } = require('../roresolvers/salary/calculateSalaryArrears');
const { formId } = require('../common/appconstants');

/**
 * Main resolver function to add/update salary details
 * @param {Object} args - Arguments passed to the resolver
 * @param {Object} context - Context object containing user info
 * @returns {Object} - Response with operation status
 */
const addUpdateSalaryDetails = async (_, args, context) => {
  let organizationDbConnection;
  let validationError = {};
  try {
    console.log('Inside addUpdateSalaryDetails function');

    // Get user info from context
    const {
      logInEmpId: loginEmployeeId,
      orgCode,
      userIp
    } = context;

    const formId = args.formId // Default to salary form
    const isEditMode = args.isEditMode || false;

    // Initialize database connection
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Get table configuration based on formId
    const tableConfig = getTableConfig(formId);

    // Determine access form ID (use accessFormId if provided, otherwise use formId)
    const accessFormId = args.accessFormId || formId;

    // Check access rights based on operation type
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      '',
      'UI',
      false,
      accessFormId
    );

    // Verify user has appropriate access rights
    if (isEditMode) {
      // Check edit access
      if (Object.keys(checkRights).length === 0 || checkRights.Role_Update !== 1) {
        throw '_DB0102'; // No edit access
      }
    } else {
      // Check add access
      if (Object.keys(checkRights).length === 0 || checkRights.Role_Add !== 1) {
        throw '_DB0101'; // No add access
      }
    }

    // Check if this is an FBP allowance update for formId 207
    const isFBPAllowanceUpdate = await checkIfFBPAllowanceUpdate(organizationDbConnection, args, accessFormId);

    if (isFBPAllowanceUpdate) {
      // Current UTC timestamp for FBP update
      const currentTimestamp = moment().utc().format('YYYY-MM-DD HH:mm:ss');

      // Handle FBP allowance update separately
      const result = await handleFBPAllowanceUpdate(organizationDbConnection, args, loginEmployeeId, tableConfig, currentTimestamp, orgCode, userIp);
      return result;
    }

    // Validate inputs based on form type
    if (formId === 206) {
      // Validate salary template inputs
      if (!validation.multilingualNameNumericValidation(args.templateName)) {
        validationError['IVE0609'] = commonLib.func.getError('', 'IVE0609').message1;
      }
      if (args.templateName && args.templateName.length < 2 || args.templateName.length > 100) {
        validationError['IVE0608'] = commonLib.func.getError('', 'IVE0608').message1;
      }
      if(args.description && args.description.length > 0 && !validation.multilingualNameNumericValidation(args.description)){
        validationError['IVE0610'] = commonLib.func.getError('', 'IVE0610').message1;
      }
      if(args.description && (args.description.length <2 ||args.description.length > 600)) {
        validationError['IVE0611'] = commonLib.func.getError('', 'IVE0611').message1;
      }
    }
    if(args.formId === 207 || args.formId === 360){
      let employeeSalaryType = await commonLib.payroll.getEmployeeSalaryType(organizationDbConnection, args.employeeId);
      let employeeMaxPayslipMonth = await commonLib.func.maxPayslipMonth(organizationDbConnection, args.employeeId, employeeSalaryType);
      if(args.formId === 207  && args.isEditMode){
      if(employeeMaxPayslipMonth) {
        // Convert YYYY-MM-00 format to YYYY-MM-01 (first day of month)
        const [year, month] = employeeMaxPayslipMonth.split('-');
        const payslipFirstDay = moment(`${year}-${month}-01`, 'YYYY-MM-DD');

        if(payslipFirstDay.isValid() && payslipFirstDay.isAfter(moment(args.effectiveFrom))){
          throw 'IVE0712';
        }
      }
     }
    if (args.formId === 360 && args.payoutMonth) {
      let {Resignation_Date} = await getEmployeeDetails(organizationDbConnection, args.employeeId);
     if (Resignation_Date && employeeMaxPayslipMonth) {
      // Convert YYYY-MM-00 format to YYYY-MM-01 (first day of month) for resignation check
      const [year, month] = employeeMaxPayslipMonth.split('-');
      const payslipFirstDay = moment(`${year}-${month}-01`, 'YYYY-MM-DD');

      if (payslipFirstDay.isValid() && moment(Resignation_Date).isSameOrBefore(payslipFirstDay)) {
        throw 'IVE0619';
      }
    }
     if (employeeMaxPayslipMonth) {
        const payoutMoment = moment(args.payoutMonth, 'M,YYYY').startOf('month');
        const [year, month] = employeeMaxPayslipMonth.split('-');
        const payslipMoment = moment(`${year}-${month}`, 'YYYY-MM').startOf('month');

        // Min validation: payout month should be > maxPayslipMonth
        if (payoutMoment.isSameOrBefore(payslipMoment)) {
          validationError['IVE0612'] = commonLib.func.getError('', 'IVE0612').message1;
        }
      }

      // Max validation: payout month should be <= last fiscal month
      // Always run this validation (removed employeeMaxPayslipMonth gate)
      const payoutMoment = moment(args.payoutMonth, 'M,YYYY').startOf('month');

      // Get organization details to fetch fiscal end month (scoped by Org_Code)
      const orgDetails = await organizationDbConnection(ehrTables.orgDetails)
        .select('Fiscal_StartMonth', 'Assessment_Year')
        .where('Org_Code', orgCode)
        .first();

      if (orgDetails && orgDetails.Fiscal_StartMonth && orgDetails.Assessment_Year) {
        const fiscalMonthArray = await getFiscalMonthYear(organizationDbConnection, orgCode, orgDetails.Assessment_Year);
        if (fiscalMonthArray && fiscalMonthArray.length > 0) {
          const lastFiscalMonth = fiscalMonthArray[fiscalMonthArray.length - 1];
          const [lastMonth, lastYear] = lastFiscalMonth.split(',');
          const lastFiscalMoment = moment(`${lastYear}-${lastMonth}`, 'YYYY-M').startOf('month');
          if (payoutMoment.isAfter(lastFiscalMoment)) {
            validationError['IVE0776'] = commonLib.func.getError('', 'IVE0776').message1;
          }
        }
      }
    }
     if (args.formId === 360 && args.employeeId) {
      await validateSalaryRevision(organizationDbConnection, args, isEditMode,employeeMaxPayslipMonth);
    }

    // Validate and auto-fill Salary_Effective_To for formId 360 (Salary Revision)
    // This field is required only when revisionWithoutArrear is false
    // Using employeeMaxPayslipMonth already fetched above
    if (args.formId === 360 && !args.revisionWithoutArrear && employeeMaxPayslipMonth) {
      // Calculate expected Salary_Effective_To as maxPayslipMonth (same month)
      const [year, month] = employeeMaxPayslipMonth.split('-');
      const maxPayslipMoment = moment(`${year}-${month}`, 'YYYY-MM');
      const expectedEffectiveTo = maxPayslipMoment.format('M,YYYY');

      // Auto-fill if not provided
      if (!args.salaryEffectiveTo) {
        args.salaryEffectiveTo = expectedEffectiveTo;
      } else {
        // Validate that user-provided salaryEffectiveTo matches expected value
        const userProvidedEffectiveTo = moment(args.salaryEffectiveTo, 'M,YYYY').format('M,YYYY');

        if (userProvidedEffectiveTo !== expectedEffectiveTo) {
          validationError['IVE0775'] = commonLib.func.getError('', 'IVE0775').message1;
        }
      }
    }
  }
    if (args.formId === 207 || args.formId === 360) {
      const {Date_Of_Join} = await getEmployeeDetails(organizationDbConnection, args.employeeId);

      if (args.salaryEffectiveMonth && Date_Of_Join) {
        const [inputMonth, inputYear] = args.salaryEffectiveMonth.split(',');
        const effectiveMonthDate = moment(`${inputYear}-${inputMonth.padStart(2, '0')}-01`);
        const doj = moment(Date_Of_Join);
        // const minAllowedDate = moment().startOf('month').subtract(6, 'months');
      
        // 1. Check if Date of Join is after salary effective month
        if (doj.isAfter(effectiveMonthDate)) {
          throw 'IVE0617';
        }
      }
    }

    // Validation for formId 360 (Salary Revision) - Check for duplicate Applied status and salary effective month validation
    if (
      args.formId === 360 &&
      (!args.salaryEffectiveMonth || !moment(args.salaryEffectiveMonth, 'M,YYYY').isSame(moment(args.payoutMonth, 'M,YYYY'), 'month'))
    ){
      const monthList = getMonthsBetweenDates(
        moment(args.salaryEffectiveMonth, 'M,YYYY').format('YYYY-M'),
        moment(args.payoutMonth, 'M,YYYY').format('YYYY-M')
    );

    const { salaryPayslips, resignationDate } = await getSalaryPayslipWithResignation(
        organizationDbConnection,
        args.employeeId,
        monthList
    );
    if(!salaryPayslips?.length){
        throw 'PST0024';
    }
  }

    // Validation for formId 360 - Prevent editing records in cancellation process
    if (args.formId === 360 && isEditMode && args.id) {
      const currentRevision = await organizationDbConnection(ehrTables.salaryRevisionDetails)
        .select('Revision_Status')
        .where('Revision_Id', args.id)
        .first();

      if (currentRevision) {
        const status = currentRevision.Revision_Status.toLowerCase();
        // Prevent editing if record is in cancellation process
        if (status === 'cancel in progress' || status === 'cancel applied') {
          throw 'PST0120'; // Cannot edit record in cancellation process
        }
      }
    }

    // Validate mandatory fields for both add and update operations
    switch (parseInt(args.formId)) {
      case 207: // Salary Form
        if (!args.employeeId) {
          validationError['IVE0644'] = commonLib.func.getError('', 'IVE0644').message1;
        }
        if (!args.templateId) {
          validationError['IVE0645'] = commonLib.func.getError('', 'IVE0645').message1;
        }
        if (!args.annualCTC) {
          validationError['IVE0647'] = commonLib.func.getError('', 'IVE0647').message1;
        }
        if (!args.annualGrossSalary) {
          validationError['IVE0648'] = commonLib.func.getError('', 'IVE0648').message1;
        }
        if (!args.monthlyGrossSalary) {
          validationError['IVE0649'] = commonLib.func.getError('', 'IVE0649').message1;
        }
        break;
      case 360: // Salary Revision
        if (!args.employeeId) {
          validationError['IVE0651'] = commonLib.func.getError('', 'IVE0651').message1;
        }
        if (!args.templateId) {
          validationError['IVE0652'] = commonLib.func.getError('', 'IVE0652').message1;
        }
        if (!args.annualCTC) {
          validationError['IVE0654'] = commonLib.func.getError('', 'IVE0654').message1;
        }
        if (!args.annualGrossSalary) {
          validationError['IVE0655'] = commonLib.func.getError('', 'IVE0655').message1;
        }
        if (!args.monthlyGrossSalary) {
          validationError['IVE0656'] = commonLib.func.getError('', 'IVE0656').message1;
        }
        break;
    }

    if (Object.keys(validationError).length > 0) {
        throw 'IVE0000';
    }

    // Current UTC timestamp
    const currentTimestamp = moment().utc().format('YYYY-MM-DD HH:mm:ss');

    if((args.formId === 360 || args.formId === 207 )&& args.salaryEffectiveMonth && args.salaryEffectiveMonth.length > 0){
      const [month, year] = args.salaryEffectiveMonth.split(',')
      let { Last_SalaryDate } = await commonLib.func.getSalaryDay(
      orgCode,
          organizationDbConnection,
          month,
          year
      );
      if (moment(Last_SalaryDate, 'YYYY-MM-DD', true).isValid()) {
          args.salaryEffectiveMonth = moment(Last_SalaryDate).format('M,YYYY');   
      }
    }
       // Call calculateSalary function for salary details (formId 207)
       if (parseInt(args.formId) === 207) {
        try {
          const salaryCalculationResult = await callCalculateSalary(
            organizationDbConnection,
            args,
            args.id,
            orgCode
          );
          if (salaryCalculationResult.errorCode && salaryCalculationResult.errorCode.length > 0) {
            throw salaryCalculationResult.errorCode;
          }
          const isValid = validateRetiralsMatch(salaryCalculationResult, args);
          if (!isValid) {
            console.log("Retirals mismatch")
            throw 'PST0013';
          }
        } catch (calculationError) {
          console.log('Error in salary calculation:', calculationError);
          throw calculationError;
        }
      }

      let insertId;
    if (isEditMode) {
      // For edit mode, update existing record
      await updateSalaryDetails(
        organizationDbConnection,
        args,
        loginEmployeeId,
        tableConfig,
        currentTimestamp,
        orgCode,
        userIp
      );
      insertId = args.id;

      return {
        errorCode: "",
        message: `${tableConfig.entityName} updated successfully`
      };
    } else {
      insertId = await insertSalaryDetails(
        organizationDbConnection,
        args,
        loginEmployeeId,
        tableConfig,
        currentTimestamp,
        orgCode,
        userIp
      );

      if (
        args.formId === 360 &&
        (!args.salaryEffectiveMonth || !moment(args.salaryEffectiveMonth, 'M,YYYY').isSame(moment(args.payoutMonth, 'M,YYYY'), 'month')
       && !args.revisionWithoutArrear
      )
      ){
        try{
        args.revisionId=insertId;
        let context={
          orgdb:organizationDbConnection
        }
        await calculateSalaryArrears(
          null,
          args,
          context,
          null
        );
        }catch (error) {
          // Cleanup: Delete all records added during this operation (only for add scenario, not update)
          if (!args.isEditMode && insertId) {
            try {
                await organizationDbConnection(ehrTables.salaryRevisionAllowance)
                  .where('Revision_Id', insertId)
                  .delete();
                await organizationDbConnection(ehrTables.salaryRevisionRetirals)
                  .where('Revision_Id', insertId)
                  .delete();
                await organizationDbConnection(ehrTables.salaryRevisionDetails)
                  .where('Revision_Id', insertId)
                  .delete();

                  const responseObject = await getWorkflowProcessInstanceId(
                    organizationDbConnection,
                    insertId
                  );
                  if (responseObject && responseObject[0]?.Process_Instance_Id) {
                    await deleteOldApprovalRecordsWithoutTrx(
                      organizationDbConnection,
                      responseObject[0].Process_Instance_Id
                    );
                  }
                  throw error;
            } catch(error) {
              console.log('Error while calling calculateSalaryArrears function', error);
              throw 'PST0025';
            }
          }
        
          throw 'PST0025';
        }
        
      }

      return {
        errorCode: "",
        message: `${tableConfig.entityName} added successfully`
      };
    }
  } catch (error) {
    console.log('Error in addUpdateSalaryDetails function main catch block', error);

    if (error === 'IVE0000') {
      errResult = commonLib.func.getError('', 'IVE0000');
      throw new UserInputError(errResult.message, { validationError: validationError });
    }
    else{
     let errorCode = error?.code === 'ER_DUP_ENTRY' ? 'PST0023' : error || 'PST0013';
    let errResult = commonLib.func.getError(errorCode, 'PST0013');
 
    if(errorCode === 'PST0025'){
      errResult.message = 'Oops! Something went wrong while adding salary revision details, please contact the platform administrator.';
    }
    if (errorCode === 'PST0023') {
      if (args.formId === 207) {
        errResult.message = 'Duplicate Employee Salary: The employee salary details already exist. Please choose a different employee.';
      } else if (args.formId === 360) {
        errResult.message = 'Duplicate Salary Revision: The salary revision already exists. Please choose a different revision period.';
      } else if (args.formId === 206) {
        errResult.message = 'Duplicate Salary Template: The salary template already exists. Please choose a different template name.';
      }
    }
    if(errorCode === 'PST0024'){
      errResult.message = 'No salary payslips found for the given employee ID and month list';
    }

    throw new ApolloError(errResult.message, errResult.code);
  }
  }
   finally {
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
 
};

/**
 * Validate allowance amounts against template limits
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} args - Arguments containing allowances and templateId
 * @param {Object} trx - Database transaction
 * @returns {Promise<void>} - Throws error if validation fails
 */
async function validateAllowanceTemplateLimits(organizationDbConnection, args, trx) {
  try {
    // Only validate for formId 207 with template and allowances
    if (parseInt(args.formId) !== 207 || !args.templateId || !args.allowance || args.allowance.length === 0) {
      return;
    }

    // Get template allowance limits for all allowance types (not just FBP)
    const templateAllowances = await organizationDbConnection(ehrTables.templateAllowanceComponents + ' as TAC')
      .select('TAC.Allowance_Type_Id', 'TAC.FBP_Max_Declaration', 'AT.FBP_Max_Declaration_Amount')
      .innerJoin(ehrTables.allowanceType + ' as AT', 'TAC.Allowance_Type_Id', 'AT.Allowance_Type_Id')
      .where('TAC.Template_Id', args.templateId)
      .transacting(trx);

    // Create template limits map
    const templateLimits = {};
    templateAllowances.forEach(ta => {
      // Use template's FBP_Max_Declaration if available, otherwise use allowance type's value
      templateLimits[ta.Allowance_Type_Id] = parseFloat(ta.FBP_Max_Declaration) ||
                                             parseFloat(ta.FBP_Max_Declaration_Amount) || 0;
    });

    // Validate each allowance against template limit
    for (const allowanceItem of args.allowance) {
      if (allowanceItem.fbpMaxDeclaration) {
        const templateLimit = templateLimits[allowanceItem.allowanceTypeId] || 0;
        const requestedFBPMax = parseFloat(allowanceItem.fbpMaxDeclaration) || 0;

        if (templateLimit > 0 && requestedFBPMax > templateLimit) {
          throw 'IVE0707'; // FBP max declaration exceeds template limit
        }
      }
    }
  } catch (error) {
    console.log('Error in validateAllowanceTemplateLimits function:', error);
    throw error;
  }
}

/**
 * Get table configuration based on form ID
 * @param {number} formId - Form ID to determine table configuration
 * @returns {Object} - Configuration object with table details
 */
function getTableConfig(formId) {
  switch (parseInt(formId)) {
    case 206: // Salary Template
      return {
        entityName: 'Salary template',
        mainTable: ehrTables.salaryTemplate,
        allowanceTable: ehrTables.templateAllowanceComponents,
        retiralsTable: ehrTables.templateRetiralComponents,
        grossTable: ehrTables.templateGrossComponents,
        primaryKey: 'Template_Id',
        foreignKey: 'Template_Id',
        statusField: 'Template_Status'
      };
    case 207: // Salary Form
      return {
        entityName: 'Salary details',
        mainTable: ehrTables.employeeSalaryDetails,
        allowanceTable: ehrTables.employeeSalaryAllowance,
        retiralsTable: ehrTables.employeeSalaryRetirals,
        grossTable: ehrTables.salaryGrossComponents,
        primaryKey: 'Employee_Id',
        foreignKey: 'Employee_Id'
      };
    case 360: // Salary Revision
      return {
        entityName: 'Salary revision',
        mainTable: ehrTables.salaryRevisionDetails,
        allowanceTable: ehrTables.salaryRevisionAllowance,
        retiralsTable: ehrTables.salaryRevisionRetirals,
        grossTable: ehrTables.revisionGrossComponents,
        primaryKey: 'Revision_Id',
        foreignKey: 'Revision_Id'
      };
    default:
      throw new Error(`Invalid formId: ${formId}`);
  }
}

/**
 * Insert new salary details
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} args - Arguments from resolver
 * @param {string} loginEmployeeId - ID of logged in employee
 * @param {Object} tableConfig - Table configuration
 * @param {string} currentTimestamp - Current UTC timestamp
 * @param {string} orgCode - Organization code
 * @param {string} userIp - User IP address
 * @returns {Promise<string>} - Success message
 */
async function insertSalaryDetails(organizationDbConnection, args, loginEmployeeId, tableConfig, currentTimestamp, orgCode, userIp) {
  try {
    return await organizationDbConnection.transaction(async (trx) => {
      // Prepare main record data based on form type
      let mainTableData = {};

      // Common fields for all forms
      const commonFields = {
        Added_On: currentTimestamp,
        Added_By: loginEmployeeId
      };
      switch (parseInt(args.formId)) {
        case 206: // Salary Template
          if (!args.templateName) {
            throw new Error('Template name is required for salary template');
          }
          mainTableData = {
            Template_Name: args.templateName,
            External_Template_Id: args.externalTemplateId || null,
            Annual_Ctc: args.annualCTC,
            Annual_Gross_Salary: args.annualGrossSalary,
            Monthly_Gross_Salary: args.monthlyGrossSalary,
            Description: args.description || '',
            Template_Status: args.templateStatus || 'Active',
            ...commonFields
          };
          break;

        case 207: // Salary Form
          mainTableData = {
            Employee_Id: args.employeeId,
            Template_Id: args.templateId,
            Effective_From: args.effectiveFrom,
            Annual_Ctc: args.annualCTC,
            Annual_Gross_Salary: args.annualGrossSalary,
            Monthly_Gross_Salary: args.monthlyGrossSalary,
            Salary_Effective_Month: args.salaryEffectiveMonth,
            ...commonFields
          };
          break;

        case 360: // Salary Revision
          mainTableData = {
            Employee_Id: args.employeeId,
            Template_Id: args.templateId,
            Salary_Effective_Month: args.salaryEffectiveMonth,
            Salary_Effective_To: args.salaryEffectiveTo || null,
            Effective_From: args.effectiveFrom,
            Revision_Type: args.revisionType,
            Revision_Status: args.revisionStatus,
            Previous_Ctc: args.previousCtc,
            Revise_Ctc_By_Percentage: args.reviseCtcByPercentage || null,
            Payout_Month: args.payoutMonth || null,
            Annual_Ctc: args.annualCTC,
            Annual_Gross_Salary: args.annualGrossSalary,
            Monthly_Gross_Salary: args.monthlyGrossSalary,
            ...commonFields
          };
          break;

        default:
          throw new Error(`Invalid formId: ${args.formId}`);
      }

      // Insert main record
      const [insertId] = await organizationDbConnection(tableConfig.mainTable)
        .insert(mainTableData)
        .transacting(trx);

      if (parseInt(args.formId) === 360) {
        const Basic_Pay= await getBasicPay(organizationDbConnection, args.employeeId);
        const instanceData = {
          formId: Number(args.formId ?? 360),
          id: Number(insertId ?? 0),
          annualCTC: String(args.annualCTC ?? ""),
          employeeId: Number(args.employeeId ?? 0),
          Employee_Id: Number(args.employeeId ?? 0),
          templateId: Number(args.templateId ?? 0),
          salaryEffectiveMonth: String(args.salaryEffectiveMonth ?? ""),
          effectiveFrom: String(args.effectiveFrom ?? ""),
          annualGrossSalary: String(args.annualGrossSalary ?? ""),
          Revise_Ctc_By_Percentage:  String(args.reviseCtcByPercentage ?? ""),
          basicPay: String(Basic_Pay && Basic_Pay.Basic_Pay? Basic_Pay.Basic_Pay : ""),
          effectiveTo: String(args.effectiveTo ?? ""),
          monthlyGrossSalary: String(args.monthlyGrossSalary ?? ""),
          payoutMonth: String(args.payoutMonth ?? ""),
          revisionType: String(args.revisionType ?? ''),
          salaryEffectiveTo: String(args.salaryEffectiveTo ?? ''),
          revisionStatus: String(args.revisionStatus ?? ''),
          previousCtc: String(args.previousCtc ?? ''),
          Added_On: String(currentTimestamp),
          Added_By: Number(loginEmployeeId)
        };
        // Get employee name for workflow
        let employeeNameObject = await getEmployeeName(organizationDbConnection, loginEmployeeId);
        if (!employeeNameObject) {
          console.log("login employee details not found", loginEmployeeId);
          throw 'ETR0003';
        }

        instanceData.Added_By_Name = employeeNameObject.employeeName;

        // Get event ID and initiate workflow
        const eventId = await getEventId(organizationDbConnection, args.formId);
        if (eventId && (!args.revisionStatus || args.revisionStatus.toLowerCase() !== 'rejected')) {
          await initaiteWorkflow(
            eventId,
            instanceData,
            orgCode,
            args.formId,
            organizationDbConnection,
            loginEmployeeId,
            trx
          );
        }
      }
      // Process allowances
      if (args.allowance && args.allowance.length > 0) {
        // Validate allowance template limits for all allowances
        await validateAllowanceTemplateLimits(organizationDbConnection, args, trx);

        // Validate allowances before processing
        for (let item of args.allowance) {
          if (item.allowanceType.toLowerCase() === 'percentage' && (!item.allowanceWages || item.allowanceWages.trim() === '')) {
            throw 'IVE0615'
          }
        }

        const allowanceData = args.allowance.map(item => ({
          [`${tableConfig.foreignKey}`]: args.formId === 207 ? args.employeeId : insertId,
          Allowance_Type_Id: item.allowanceTypeId,
          Allowance_Type: item.allowanceType,
          Allowance_Wages: item.allowanceWages || null,
          Percentage: item.percentage || null,
          Amount: item.amount || null,
          FBP_Max_Declaration: item.fbpMaxDeclaration || null
        }));

        await organizationDbConnection(tableConfig.allowanceTable)
          .insert(allowanceData)
          .transacting(trx);
      }

      // Process retirals - only for salary form and revision
      if (args.retirals && args.retirals.length > 0) {
        for(let item of args.retirals){
          let retiralIdForFunction=null;
          if(item.formId==58){
            retiralIdForFunction=item.retiralsId
          }
          const {Slab_Wise_PF ,Slab_Wise_NPS,Slab_Wise_Insurance}= await slabWiseData(organizationDbConnection,retiralIdForFunction);
          if(item.formId==52 && Slab_Wise_PF==='Yes'){
            continue;
          }
          else if(item.formId==126 && Slab_Wise_NPS==='Yes'){
            continue;
          }
          else if(item.formId==58 && Slab_Wise_Insurance==='Yes'){
            continue;
          }
          if(item.retiralsType.toLowerCase() === 'percentage' && (!item.employeeRetiralWages || item.employeeRetiralWages.trim() === '') && (!item.employerRetiralWages || item.employerRetiralWages.trim() === '')){
            throw 'IVE0616'
          }
        }
        const retiralsData = args.retirals.map(item => ({
          [`${tableConfig.foreignKey}`]: args.formId === 207 ? args.employeeId : insertId,
          Form_Id: item.formId,
          Retirals_Id: item.retiralsId,
          Retirals_Type: item.retiralsType,
          Employee_Retiral_Wages: item.employeeRetiralWages || null,
          Employer_Retiral_Wages: item.employerRetiralWages || null,
          Employee_Share_Percentage: item.employeeSharePercentage || null,
          Employer_Share_Percentage: item.employerSharePercentage || null,
          Employee_Share_Amount: item.employeeShareAmount || null,
          Employer_Share_Amount: item.employerShareAmount || null,
          PF_Employee_Contribution: item.pfEmployeeContribution || null,
          PF_Employer_Contribution: item.pfEmployerContribution || null,
          Employee_Statutory_Limit: item.employeeStatutoryLimit || null,
          Employer_Statutory_Limit: item.employerStatutoryLimit || null,
          Eligible_For_EPS: item.eligibleForEPS || 0,
          Contribute_EPF_Actual_PF_Wage: item.contributeEpfActualPfWage || 0,
          Admin_Charge: item.adminCharge || null,
          EDLI_Charge: item.edliCharge || null,
          Contribution_End_Month: item.contributionEndMonth || null,
          Contribution_Period_Completed: item.contributionPeriodCompleted || null
        }));

        await organizationDbConnection(tableConfig.retiralsTable)
          .insert(retiralsData)
          .transacting(trx);
      }

      //Process gross
      if (args.gross && args.gross.length > 0) {
        const grossData = args.gross.map(item => ({
          [`${tableConfig.foreignKey}`]: args.formId === 207 ? args.employeeId : insertId,
          Gross_Id: item.grossId,
          Amount: item.amount,
        }));

        await organizationDbConnection(tableConfig.grossTable)
          .insert(grossData)
          .transacting(trx);
      }

      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection,
        message: `Salary revision for employee ${args.employeeId} ${args.isEditMode ? 'updated' : 'added'} successfully`
      });

      // Handle old salary table operations for formId 207
      if (parseInt(args.formId) === 207) {
        // Check if salary template is enabled before proceeding
        const isMigrationAllowed = await checkSalaryMigrationAllowed(organizationDbConnection, trx);

        if (isMigrationAllowed) {
          console.log('Salary template is enabled, proceeding with old salary table operations');
          args.id = insertId; // Set the inserted ID for the old table operations
          await commonLib.func.handleOldSalaryTableOperations(
            organizationDbConnection,
            args,
            loginEmployeeId,
            currentTimestamp,
            trx
          );
          await updateDataSetupDashboard(trx);
        } else {
          console.log('Salary template is not enabled, skipping old salary table operations');
        }
      }

      return insertId;
    });
  } catch (error) {
    console.log('Error in insertSalaryDetails function', error);
    throw error;
  }
}

/**
 * Update existing salary details
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} args - Arguments from resolver
 * @param {string} loginEmployeeId - ID of logged in employee
 * @param {Object} tableConfig - Table configuration
 * @param {string} currentTimestamp - Current UTC timestamp
 * @param {string} orgCode - Organization code
 * @param {string} userIp - User IP address
 * @returns {Promise<string>} - Success message
 */

async function updateDataSetupDashboard(trx) {
  try {
      // Update the status to indicate data has been modified
      await trx(ehrTables.dataSetupDashboard)
          .where('Form_Id', 37)
          .update({
              Status: 'Completed'
          });
  } catch (error) {
      console.log('Error in updateDataSetupDashboard:', error);
      // Don't throw error as this is not critical
  }
}
async function updateSalaryDetails(organizationDbConnection, args, loginEmployeeId, tableConfig, currentTimestamp, orgCode, userIp) {
  try {
    return await organizationDbConnection.transaction(async (trx) => {
      // Get the record to update
      const recordToUpdate = await organizationDbConnection(tableConfig.mainTable)
        .select('*')
        .where(tableConfig.primaryKey, args.formId === 207 ? args.employeeId : args.id)
        .first()
        .transacting(trx);
        console.log('recordToUpdate', recordToUpdate);

      if (!recordToUpdate) {
        throw 'PST0014'; // Record not found
      }

      // Common update fields
      const commonFields = {
        Updated_On: currentTimestamp,
        Updated_By: loginEmployeeId
      };

      // Prepare main record data based on form type
      let mainTableData = {};

      switch (parseInt(args.formId)) {
        case 206: // Salary Template
          // For template, we need to ensure Template_Name exists
          if (args.templateName === '' && !recordToUpdate.Template_Name) {
            throw new Error('Template name is required for salary template');
          }
          mainTableData = {
            Template_Name: args.templateName,
            External_Template_Id: args.externalTemplateId || null,
            Annual_Ctc: args.annualCTC,
            Annual_Gross_Salary: args.annualGrossSalary,
            Monthly_Gross_Salary: args.monthlyGrossSalary,
            Description: args.description,
            Template_Status: args.templateStatus,
            ...commonFields
          };
          break;
        case 207: // Salary Form
          // For salary form, check if we need to update Effective_To on previous record
          if (args.effectiveFrom && args.effectiveFrom !== recordToUpdate.Effective_From) {
            // This is a new effective date, so we should handle it appropriately
            mainTableData = {
              Employee_Id: args.employeeId,
              Template_Id: args.templateId,
              Effective_From: args.effectiveFrom,
              Annual_Ctc: args.annualCTC,
              Annual_Gross_Salary: args.annualGrossSalary,
              Monthly_Gross_Salary: args.monthlyGrossSalary,
              Salary_Effective_Month: args.salaryEffectiveMonth,
              ...commonFields
            };
          } else {
            // Regular update without changing effective date
            mainTableData = {
              Employee_Id: args.employeeId,
              Template_Id: args.templateId,
              Effective_From: args.effectiveFrom,
              Annual_Ctc: args.annualCTC,
              Annual_Gross_Salary: args.annualGrossSalary,
              Monthly_Gross_Salary: args.monthlyGrossSalary,
              Salary_Effective_Month: args.salaryEffectiveMonth,
              ...commonFields
            };
          }
          break;

        case 360: // Salary Revision
          mainTableData = {
            Employee_Id: args.employeeId,
            Template_Id: args.templateId,
            Effective_From: args.effectiveFrom,
            Salary_Effective_Month: args.salaryEffectiveMonth,
            Salary_Effective_To: args.salaryEffectiveTo || null,
            Payout_Month: args.payoutMonth,
            Revision_Type: args.revisionType,
            Revision_Status: args.revisionStatus,
            Previous_Ctc: args.previousCtc,
            Revise_Ctc_By_Percentage: args.reviseCtcByPercentage,
            Annual_Ctc: args.annualCTC,
            Annual_Gross_Salary: args.annualGrossSalary,
            Monthly_Gross_Salary: args.monthlyGrossSalary,
            ...commonFields
          };
          break;

        default:
          throw new Error(`Invalid formId: ${args.formId}`);
      }

      // Update main record
      await organizationDbConnection(tableConfig.mainTable)
        .update(mainTableData)
        .where(tableConfig.primaryKey, args.formId === 207 ? args.employeeId : args.id)
        .transacting(trx);

      // Handle workflow for formId 360 updates
      if (parseInt(args.formId) === 360) {
        // Get workflow process instance ID
        const responseObject = await getWorkflowProcessInstanceId(
          organizationDbConnection,
          args.id
        );

        // Prepare instance data for workflow
        const instanceData = {
          formId: Number(args.formId ?? 360),
          id: Number(args.id ?? 0),
          annualCTC: String(args.annualCTC ?? ""),
          employeeId: Number(args.employeeId ?? 0),
          Employee_Id: Number(args.employeeId ?? 0),
          templateId: Number(args.templateId ?? 0),
          effectiveFrom: String(args.effectiveFrom ?? ""),
          salaryEffectiveMonth: String(args.salaryEffectiveMonth ?? ""),
          Revise_Ctc_By_Percentage:  String(args.reviseCtcByPercentage ?? ""),
          annualGrossSalary: String(args.annualGrossSalary ?? ""),
          monthlyGrossSalary: String(args.monthlyGrossSalary ?? ""),
          payoutMonth: String(args.payoutMonth ?? ""),
          revisionType: String(args.revisionType ?? ""),
          revisionStatus: String(args.revisionStatus ?? ""),
          previousCtc: String(args.previousCtc ?? ""),
          reviseCtcByPercentage: String(args.reviseCtcByPercentage ?? ""),
          Updated_On: String(currentTimestamp),
          Updated_By: Number(loginEmployeeId)
        };
        // Get employee name for workflow
        let employeeNameObject = await getEmployeeName(organizationDbConnection, loginEmployeeId);
        if (!employeeNameObject) {
          console.log("login employee details not found", loginEmployeeId);
          throw 'ETR0003';
        }

        instanceData.Updated_By = employeeNameObject.employeeName;
        if (responseObject[0]) {
          instanceData.Added_On = responseObject[0].Added_On;
          instanceData.Added_By_Name = responseObject[0].Added_By_Name;
          instanceData.Added_By = Number(loginEmployeeId);
        }

        // Get event ID and initiate workflow
        const eventId = await getEventId(organizationDbConnection, args.formId);
        if (eventId && args.revisionStatus && args.revisionStatus.toLowerCase() !== 'rejected') {
          await initaiteWorkflow(
            eventId,
            instanceData,
            orgCode,
            args.formId,
            organizationDbConnection,
            loginEmployeeId,
            trx
          );

          // Delete old approval records if process instance exists
          if (responseObject && responseObject[0]?.Process_Instance_Id) {
            await commonLib.func.deleteOldApprovalRecords(
              organizationDbConnection,
              responseObject[0].Process_Instance_Id,
              trx
            );
          }
        }
      }

      // Handle allowances - delete and reinsert
      if (args.allowance !== undefined) {  // If allowance parameter is provided (even if empty array)
        // Always delete existing allowances first to ensure clean state
        const deleteValue = args.formId === 207 ? args.employeeId : args.id;
        await organizationDbConnection(tableConfig.allowanceTable)
          .where(tableConfig.foreignKey, deleteValue)
          .delete()
          .transacting(trx);

        // Insert new allowances only if array has items
        if (args.allowance.length > 0) {
          // Validate allowance template limits for all allowances
          await validateAllowanceTemplateLimits(organizationDbConnection, args, trx);

          // Validate allowances before processing
          for (let item of args.allowance) {
            if (item.percentage && (!item.allowanceWages || item.allowanceWages.trim() === '')) {
              throw new Error('Allowance wages is required when percentage is provided');
            }
          }

          // Insert new allowances
          for(let item of args.allowance){
            if(item.allowanceType.toLowerCase() === 'percentage' && (!item.allowanceWages || item.allowanceWages.trim() === '')){
              throw 'IVE0615'
            }
          }
          const allowanceData = args.allowance.map(item => ({
            [`${tableConfig.foreignKey}`]: args.formId === 207 ? args.employeeId : args.id,
            Allowance_Type_Id: item.allowanceTypeId,
            Allowance_Type: item.allowanceType,
            Allowance_Wages: item.allowanceWages || null,
            Percentage: item.percentage || null,
            Amount: item.amount || null,
            FBP_Max_Declaration: item.fbpMaxDeclaration || null
          }));

          await organizationDbConnection(tableConfig.allowanceTable)
            .insert(allowanceData)
            .transacting(trx);
        }
      }

      // Handle retirals - delete and reinsert (only for salary form and revision)
      if (args.retirals !== undefined) {  // If retirals parameter is provided (even if empty array)
        // Always delete existing retirals first to ensure clean state
        const retiralsDeleteValue = args.formId === 207 ? args.employeeId : args.id;
        await organizationDbConnection(tableConfig.retiralsTable)
          .where(tableConfig.foreignKey, retiralsDeleteValue)
          .delete()
          .transacting(trx);

        // Insert new retirals only if array has items
        if (args.retirals.length > 0) {
          for(let item of args.retirals){
          let retiralIdForFunction=null;
          if(item.formId==58){
            retiralIdForFunction=item.retiralsId
          }
          const {Slab_Wise_PF ,Slab_Wise_NPS,Slab_Wise_Insurance}= await slabWiseData(organizationDbConnection,retiralIdForFunction);
          if(item.formId==52 && Slab_Wise_PF==='Yes'){
            continue;
          }
          else if(item.formId==126 && Slab_Wise_NPS==='Yes'){
            continue;
          }
          else if(item.formId==58 && Slab_Wise_Insurance==='Yes'){
            continue;
          }
          if(item.retiralsType.toLowerCase() === 'percentage' && (!item.employeeRetiralWages || item.employeeRetiralWages.trim() === '') && (!item.employerRetiralWages || item.employerRetiralWages.trim() === '')){
            throw 'IVE0616'
          }
        }
        const retiralsData = args.retirals.map(item => ({
          [`${tableConfig.foreignKey}`]: args.formId === 207 ? args.employeeId : args.id,
          Form_Id: item.formId,
          Retirals_Id: item.retiralsId,
          Retirals_Type: item.retiralsType,
          Employee_Retiral_Wages: item.employeeRetiralWages || null,
          Employer_Retiral_Wages: item.employerRetiralWages || null,
          Employee_Share_Percentage: item.employeeSharePercentage || null,
          Employer_Share_Percentage: item.employerSharePercentage || null,
          Employee_Share_Amount: item.employeeShareAmount || null,
          Employer_Share_Amount: item.employerShareAmount || null,
          PF_Employee_Contribution: item.pfEmployeeContribution || null,
          PF_Employer_Contribution: item.pfEmployerContribution || null,
          Employee_Statutory_Limit: item.employeeStatutoryLimit || null,
          Employer_Statutory_Limit: item.employerStatutoryLimit || null,
          Eligible_For_EPS: item.eligibleForEPS || 0,
          Contribute_EPF_Actual_PF_Wage: item.contributeEpfActualPfWage || 0,
          Admin_Charge: item.adminCharge || null,
          EDLI_Charge: item.edliCharge || null,
          Contribution_End_Month: item.contributionEndMonth || null,
          Contribution_Period_Completed: item.contributionPeriodCompleted || null
        }));

        await organizationDbConnection(tableConfig.retiralsTable)
          .insert(retiralsData)
          .transacting(trx);
        }
      }

      // Handle gross - delete and reinsert
      if (args.gross !== undefined) {  // If gross parameter is provided (even if empty array)
        // Always delete existing gross first to ensure clean state
        const grossDeleteValue = args.formId === 207 ? args.employeeId : args.id;
        await organizationDbConnection(tableConfig.grossTable)
          .where(tableConfig.foreignKey, grossDeleteValue)
          .delete()
          .transacting(trx);

        // Insert new gross only if array has items
        if (args.gross.length > 0) {
          const grossData = args.gross.map(item => ({
            [`${tableConfig.foreignKey}`]: args.formId === 207 ? args.employeeId : args.id,
            Gross_Id: item.grossId,
            Amount: item.amount,
          }));

          await organizationDbConnection(tableConfig.grossTable)
            .insert(grossData)
            .transacting(trx);
        }
      }

      // Handle old salary table operations for formId 207
      if (parseInt(args.formId) === 207) {
        // Check if salary template is enabled before proceeding
        const isMigrationAllowed = await checkSalaryMigrationAllowed(organizationDbConnection, trx);

        if (isMigrationAllowed) {
          console.log('Salary template is enabled, proceeding with old salary table operations');
          await commonLib.func.handleOldSalaryTableOperations(
            organizationDbConnection,
            args,
            loginEmployeeId,
            currentTimestamp,
            trx
          );
        } else {
          console.log('Salary template is not enabled, skipping old salary table operations');
        }
      }

      return 'success';
    });
  } catch (error) {
    console.log('Error in updateSalaryDetails function', error);
    throw error;
  }
}

/**
 * Get employee name by employee ID
 * @param {Object} organizationDbConnection - Database connection
 * @param {string} employeeId - Employee ID
 * @returns {Promise<Object>} - Employee name object
 */
async function getEmployeeName(organizationDbConnection, employeeId) {
  try {
    return await organizationDbConnection(ehrTables.empPersonalInfo + " as EP")
      .select( organizationDbConnection.raw(`
        CONCAT_WS(" ", EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as employeeName
      `),)
      .where('Employee_Id', employeeId)
      .first();
  } catch (error) {
    console.log('Error in getEmployeeName function', error);
    throw error;
  }
}

/**
 * Helper function to call calculateSalary with proper data formatting
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} args - Arguments from resolver
 * @param {number} recordId - Record ID (insertId or args.id)
 * @param {string} orgCode - Organization code
 * @returns {Promise<Object>} - Calculation result
 */
async function callCalculateSalary(organizationDbConnection, args, recordId, orgCode) {
  try {
    // Get employee status from emp_job table and Provident_Fund_Configuration from org_details
    const [employeeJobData, providentFundConfigData] = await Promise.all([
      organizationDbConnection(ehrTables.empJob)
        .select('Emp_Status')
        .where('Employee_Id', args.employeeId)
        .first(),
      organizationDbConnection(ehrTables.orgDetails)
        .select('Provident_Fund_Configuration')
        .first()
    ]);

    const employeeStatus = employeeJobData ? employeeJobData.Emp_Status : 'Active';
    const providentFundConfigurationValue = providentFundConfigData?.Provident_Fund_Configuration;

    // Prepare the salaryDetails object exactly as expected
    const salaryDetails = {
      Employee_Salary_Id: recordId,
      Employee_Id: args.employeeId,
      Template_Id: args.templateId,
      Annual_Ctc: parseFloat(args.annualCTC),
      Effective_From: args.effectiveFrom,
      Annual_Gross_Salary: args.annualGrossSalary ? parseFloat(args.annualGrossSalary) : null,
      Monthly_Gross_Salary: args.monthlyGrossSalary ? parseFloat(args.monthlyGrossSalary) : null,
      Salary_Effective_Month: args.salaryEffectiveMonth || null,
      ESI_Contribution_End_Date: null,
      Status: employeeStatus
    };

    // Format allowance details exactly as expected
    const allowanceDetails = args.allowance ? args.allowance.map(item => ({
      Employee_Salary_Id: recordId,
      Allowance_Type_Id: item.allowanceTypeId,
      Allowance_Type: item.allowanceType,
      Allowance_Wages: item.allowanceWages ? parseFloat(item.allowanceWages) : null,
      Percentage: item.percentage ? parseFloat(item.percentage) : null,
      Amount: item.amount ? parseFloat(item.amount) : null
    })) : [];

    // Format retiral details exactly as expected
    const retiralDetails = args.retirals ? args.retirals.map(item => ({
      Employee_Salary_Id: recordId,
      Form_Id: parseInt(item.formId),
      Retirals_Id: parseInt(item.retiralsId),
      Retirals_Type: item.retiralsType,
      Employee_Retiral_Wages: item.employeeRetiralWages ? parseFloat(item.employeeRetiralWages) : null,
      Employer_Retiral_Wages: item.employerRetiralWages ? parseFloat(item.employerRetiralWages) : null,
      Employee_Share_Percentage: item.employeeSharePercentage ? parseFloat(item.employeeSharePercentage) : null,
      Employer_Share_Percentage: item.employerSharePercentage ? parseFloat(item.employerSharePercentage) : null,
      Employee_Share_Amount: item.employeeShareAmount ? parseFloat(item.employeeShareAmount) : null,
      Employer_Share_Amount: item.employerShareAmount ? parseFloat(item.employerShareAmount) : null,
      PF_Employee_Contribution: item.pfEmployeeContribution || null,
      PF_Employer_Contribution: item.pfEmployerContribution || null,
      Employee_Statutory_Limit: item.employeeStatutoryLimit ? parseFloat(item.employeeStatutoryLimit) : null,
      Employer_Statutory_Limit: item.employerStatutoryLimit ? parseFloat(item.employerStatutoryLimit) : null,
      Eligible_For_EPS: item.eligibleForEPS || 0,
      Contribute_EPF_Actual_PF_Wage: item.contributeEpfActualPfWage || 0,
      Admin_Charge: item.adminCharge ? parseFloat(item.adminCharge) : null,
      EDLI_Charge: item.edliCharge ? parseFloat(item.edliCharge) : null,
      Contribution_End_Month: item.contributionEndMonth || null,
      Contribution_Period_Completed: item.contributionPeriodCompleted || null
    })) : [];

    // Create context object for calculateSalary
    const context = {
      Org_Code: orgCode,
      orgdb: organizationDbConnection
    };
    // Call calculateSalary function with exact format as your example
    const result = await calculateSalary(
      null, // parent
      {
        employeeId: args.employeeId,
        retiralDetails: JSON.stringify(retiralDetails),
        allowanceDetails: JSON.stringify(allowanceDetails),
        salaryDetails: JSON.stringify(salaryDetails),
        providentFundConfigurationValue: providentFundConfigurationValue,
        grossIds: Array.isArray(args.gross) ? args.gross.map(item => item.grossId) : [],
        revisionWithoutArrear: args.revisionWithoutArrear || false
      },
      context,
      null // info
    );
    if(result.errorCode){
      throw result.errorCode;
    }
    return result;
  } catch (error) {
    console.log('Error in callCalculateSalary function', error);
    throw error;
  }
}











// Export resolvers
const resolvers = {
  Mutation: {
    addUpdateSalaryDetails
  }
};

async function getBasicPay(organizationDbConnection, employeeId){
  try {
    return await organizationDbConnection(ehrTables.employeeSalaryAllowance + ' as ESL')
      .select('ESL.Amount as Basic_Pay')
      .innerJoin(ehrTables.allowanceType + ' as AT', 'ESL.Allowance_Type_Id', 'AT.Allowance_Type_Id')
      .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
      .where('SC.Component_Code', 'basic_salary_amount')
      .where('ESL.Employee_Id', employeeId)
      .first();
  } catch (error) {
    console.log('Error in getBasicPay function', error);
    throw error;
  }
}

async function getEmployeeDetails(organizationDbConnection, employeeId){
  try {
    return await organizationDbConnection(ehrTables.empJob + ' as EJ')
      .select('EJ.*', 'R.Resignation_Date')
      .leftJoin(ehrTables.empResignation+' as R' , function () {
        this.on('EJ.Employee_Id', '=', 'R.Employee_Id')
          .onIn('R.Approval_Status', organizationDbConnection.raw('(?,?)', ['Applied', 'Approved']))
      })
      .where('EJ.Employee_Id', employeeId)
      .first();
  } catch (error) {
    console.log('Error in getEmployeeDetails function', error);
    throw error;
  }
}

function validateRetiralsMatch(responseData, args) {
  let apiRetirals;
  if (!responseData || !responseData.employeeRetiralDetails) return false;
  if (typeof responseData.employeeRetiralDetails === 'string') {
    try {
      apiRetirals = JSON.parse(responseData.employeeRetiralDetails).employeeSalaryRetirals;
    } catch {
      return false;
    }
  } else {
    apiRetirals = responseData.employeeRetiralDetails.employeeSalaryRetirals;
  }
  if (!Array.isArray(apiRetirals) || !Array.isArray(args?.retirals)) return false;
  if (apiRetirals.length !== args.retirals.length) return false;
  const toNum = v => parseInt(v, 10);
  return args.retirals.every(inputItem =>{
    return apiRetirals.some(apiItem =>{
      return toNum(inputItem.formId) === apiItem.Form_Id &&
      toNum(inputItem.retiralsId) === apiItem.Retirals_Id
    }
    )
  }
  );
}



/**
 * Validate salary revision for duplicate Applied status and salary effective month
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} args - Arguments from resolver
 * @param {boolean} isEditMode - Whether this is an edit operation
 * @param {string} employeeMaxPayslipMonth - Maximum payslip month in YYYY-MM-00 format
 */
async function validateSalaryRevision(organizationDbConnection, args, isEditMode,employeeMaxPayslipMonth) {
  try {
    // Check for existing Applied status records
    let query = organizationDbConnection(ehrTables.salaryRevisionDetails)
      .select('*')
      .where('Employee_Id', args.employeeId)
      .where('Revision_Status', 'Applied');

    // For edit mode, exclude the current record being edited
    if (isEditMode && args.id) {
      query = query.whereNot('Revision_Id', args.id);
    }

    const existingAppliedRecord = await query.first();
    if (existingAppliedRecord) {
      throw 'PST0023';
    }

    // Check for approved records with salary effective month validation
    if (args.payoutMonth) {
      let approvedQuery = organizationDbConnection(ehrTables.salaryRevisionDetails + ' as SRD')
        .select('*')
        .where('Employee_Id', args.employeeId)
        .where('Revision_Status', 'Approved')
        .orderBy('Payout_Month', 'desc'); // Sort by payout month from latest to earliest

      if (employeeMaxPayslipMonth) {
        // Convert YYYY-MM-00 format to M,YYYY format for comparison
        const [year, month] = employeeMaxPayslipMonth.split('-');
        const maxPayslipMonthFormatted = `${parseInt(month)},${year}`;
        approvedQuery = approvedQuery.where('Payout_Month', '>', maxPayslipMonthFormatted);
      }

      // For edit mode, exclude the current record being edited
      if (isEditMode && args.id) {
        approvedQuery = approvedQuery.whereNot('Revision_Id', args.id);
      }

      const existingApprovedRecord = await approvedQuery.first();

      if (existingApprovedRecord &&
          existingApprovedRecord.Salary_Effective_Month &&
          existingApprovedRecord.Payout_Month &&
          args.salaryEffectiveMonth &&
          args.payoutMonth) {
        const existingApprovedSalaryEffectiveMoment = moment(existingApprovedRecord.Salary_Effective_Month, 'M,YYYY');
        const existingApprovedPayoutMoment = moment(existingApprovedRecord.Payout_Month, 'M,YYYY');
        const newSalaryEffectiveMoment = moment(args.salaryEffectiveMonth, 'M,YYYY');
        const newPayoutMoment = moment(args.payoutMonth, 'M,YYYY');

        // Validate that all moment objects are valid before comparison
        if (existingApprovedSalaryEffectiveMoment.isValid() &&
            existingApprovedPayoutMoment.isValid() &&
            newSalaryEffectiveMoment.isValid() &&
            newPayoutMoment.isValid()) {

          if (existingApprovedSalaryEffectiveMoment.isSameOrAfter(newSalaryEffectiveMoment, 'month') ||
              existingApprovedSalaryEffectiveMoment.isSameOrAfter(newPayoutMoment, 'month')) {
            throw 'PST0023';
          }
        }
      }
    }

    return true;
  } catch (error) {
    console.log('Error in validateSalaryRevision function:', error);
    throw error;
  }
}
async function getSalaryPayslipWithResignation(db, employeeId, monthList) {
  try {
      if (monthList.length === 0) return [];

      const [salaryPayslips, resignationData] = await Promise.all([
          db(ehrTables.salaryPayslip + ' as SP')
              .select('SP.*', 'EJ.Date_Of_Join')
              .innerJoin(ehrTables.empJob + ' as EJ', 'SP.Employee_Id', 'EJ.Employee_Id')
              .where('SP.Employee_Id', employeeId)
              .whereIn('SP.Salary_Month', monthList),

          db(ehrTables.empResignation)
              .select('Resignation_Date')
              .where('Approval_Status', 'Approved')
              .where('Employee_Id', employeeId)
              .first()
      ]);

      if (resignationData) {
          //Check if the resignation comes between the monthList
          const resignationMonth = moment(resignationData.Resignation_Date).format('M,YYYY');
          const resignationIndex = monthList.indexOf(resignationMonth);
          if (resignationIndex !== -1) {
              monthList.splice(resignationIndex, 1);
          }
      }

      return {
          salaryPayslips,
          resignationDate: resignationData?.Resignation_Date
      };
  } catch (err) {
      console.error('Error in getSalaryPayslipWithResignation function:', err);
      throw err;
  }
}

function getMonthsBetweenDates(startMonthYear, endMonthYear) {
  try {
      if (!startMonthYear || !endMonthYear) {
          throw new Error('Start and end month-year are required');
      }

      const parseMonthYear = (monthYear) => {
          const [year, month] = monthYear.split('-').map(num => parseInt(num.trim()));
          if (isNaN(month) || isNaN(year) || month < 1 || month > 12) {
              throw new Error(`Invalid month-year format: ${monthYear}`);
          }
          return { month, year };
      };

      const start = parseMonthYear(startMonthYear);
      const end = parseMonthYear(endMonthYear);

      const months = [];
      let current = { ...start };

      while (
          current.year < end.year ||
          (current.year === end.year && current.month <= end.month)
      ) {
          months.push(`${current.month},${current.year}`);

          current.month++;
          if (current.month > 12) {
              current.month = 1;
              current.year++;
          }
      }

      return months;
  } catch (err) {
      console.error('Error in getMonthsBetweenDates:', err);
      throw err;
  }
}
async function slabWiseData(db, retiralsId = null) {
  try {
    const pgSettingsQuery = db(ehrTables.payrollGeneralSettings)
      .select('Slab_Wise_PF', 'Slab_Wise_NPS')
      .first();

    const insuranceQuery = retiralsId
      ? db(ehrTables.insuranceConfiguration)
          .select('Slab_Wise_Insurance')
          .where({
            InsuranceType_Status: 'Active',
            InsuranceType_Id: retiralsId
          })
          .first()
      : Promise.resolve(null);

    const [pgSettings, insuranceSettings] = await Promise.all([
      pgSettingsQuery,
      insuranceQuery
    ]);

    return {
      Slab_Wise_PF: pgSettings?.Slab_Wise_PF,
      Slab_Wise_NPS: pgSettings?.Slab_Wise_NPS,
      Slab_Wise_Insurance: insuranceSettings?.Slab_Wise_Insurance ?? null
    };
  } catch (error) {
    console.error('Error in slabWiseData:', error);
    throw error;
  }
}


/**
 * Check if this is an FBP allowance update request
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} args - Request arguments
 * @param {number} accessFormId - Access form ID for permission checking
 * @returns {Promise<boolean>} - True if FBP allowance update
 */
async function checkIfFBPAllowanceUpdate(organizationDbConnection, args, accessFormId) {
  try {
          // Second validation: Check if all allowances are FBP allowances
    const allowanceTypeIds = args.allowance.map(a => a.allowanceTypeId);
    // If accessFormId is 388, this is specifically an FBP operation
    const fbpAllowances = await organizationDbConnection(ehrTables.allowanceType + ' as AT')
    .select('AT.Allowance_Type_Id', 'AT.Is_Flexi_Benefit_Plan', 'AT.Restrict_Employee_FBP_Override')
    .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
    .whereIn('AT.Allowance_Type_Id', allowanceTypeIds)
    .where(function() {
      this.where('AT.Is_Flexi_Benefit_Plan', 'Yes')
        .orWhere('SC.Component_Code', 'fixed_allowance_amount');
    });
    if (parseInt(accessFormId) === 388) {
      if (parseInt(args.formId) !== 207 || !args.isEditMode || !args.allowance || args.allowance.length === 0) {
        throw 'IVE0706'; // Only FBP allowances can be updated through this process
      }

      // All allowances must be FBP allowances
      if (fbpAllowances.length !== args.allowance.length) {
        throw 'IVE0706'; // Only FBP allowances can be updated through this process
      }

      return true;
    }

    // For non-388 accessFormId, use original logic
    // Only for formId 207 (employee level) and update mode with allowances
    if (parseInt(args.formId) !== 207 || !args.isEditMode || !args.allowance || args.allowance.length === 0) {
      return false;
    }

    // All allowances must be FBP allowances
    return fbpAllowances.length === args.allowance.length;

  } catch (error) {
    throw error;
  }
}

/**
 * Handle FBP allowance update with validations
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} args - Request arguments
 * @param {string} loginEmployeeId - Logged in employee ID
 * @param {Object} tableConfig - Table configuration
 * @param {string} currentTimestamp - Current timestamp
 * @param {string} orgCode - Organization code
 * @param {string} userIp - User IP address
 * @returns {Promise<Object>} - Success response
 */
async function handleFBPAllowanceUpdate(organizationDbConnection, args, loginEmployeeId, tableConfig, currentTimestamp, orgCode, userIp) {
  try {
    return await organizationDbConnection.transaction(async (trx) => {

      // Get allowance type details with FBP settings
      const allowanceTypeIds = args.allowance.map(a => a.allowanceTypeId);

      const allowanceTypes = await organizationDbConnection(ehrTables.allowanceType + ' as AT')
        .select('AT.Allowance_Type_Id', 'AT.Allowance_Name','AT.Is_Flexi_Benefit_Plan', 'AT.Restrict_Employee_FBP_Override', 'AT.FBP_Max_Declaration_Amount', 'SC.Component_Code')
        .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
        .whereIn('AT.Allowance_Type_Id', allowanceTypeIds)
        .transacting(trx);

      // Create a map for easy lookup
      const allowanceTypeMap = {};
      allowanceTypes.forEach(at => {
        allowanceTypeMap[at.Allowance_Type_Id] = at;
      });

      // For formId 207, get template FBP max declarations for validation
      let templateFBPLimits = {};
      if (parseInt(args.formId) === 207 && args.templateId) {
        const templateAllowances = await organizationDbConnection(ehrTables.templateAllowanceComponents + ' as TAC')
          .select('TAC.Allowance_Type_Id', 'TAC.FBP_Max_Declaration', 'AT.FBP_Max_Declaration_Amount')
          .innerJoin(ehrTables.allowanceType + ' as AT', 'TAC.Allowance_Type_Id', 'AT.Allowance_Type_Id')
          .where('TAC.Template_Id', args.templateId)
          .where('AT.Is_Flexi_Benefit_Plan', 'Yes')
          .transacting(trx);

        templateAllowances.forEach(ta => {
          // Use template's FBP_Max_Declaration if available, otherwise use allowance type's value
          templateFBPLimits[ta.Allowance_Type_Id] = parseFloat(ta.FBP_Max_Declaration) || 0;
        });
      }

      // Validate each allowance
      for (const allowanceItem of args.allowance) {
        const allowanceType = allowanceTypeMap[allowanceItem.allowanceTypeId];

        if (!allowanceType) {
          throw 'IVE0706'; // Only FBP allowances can be updated
        }

        if (allowanceType.Is_Flexi_Benefit_Plan !== 'Yes' && allowanceType.Component_Code?.toLowerCase() !== 'fixed_allowance_amount') {
          throw 'IVE0706'; // Only FBP allowances can be updated
        }

        // Template limit validation is now handled by validateAllowanceTemplateLimits function

        // Validate amount against FBP max declaration
        // Use individual allowance's FBP_Max_Declaration if available, otherwise use allowance type's value
        const maxDeclaration = parseFloat(allowanceItem.fbpMaxDeclaration) ||
                               await getFBPDeclarationAmount(organizationDbConnection, args.employeeId, allowanceItem.allowanceTypeId);
        const requestedAmount = parseFloat(allowanceItem.amount) || 0;
        if (maxDeclaration > 0 && requestedAmount > maxDeclaration) {
          throw 'IVE0705'; // Amount exceeds maximum declaration limit
        }
      }

      // Update only the allowance amounts in employee_salary_allowance table using individual updates
      if (args.allowance && args.allowance.length > 0) {
        // Process updates in a single transaction but as individual queries to avoid SQL injection
        const updatePromises = [];

        args.allowance.forEach(item => {
          const allowanceTypeId = parseInt(item.allowanceTypeId);
          const amount = parseFloat(item.amount) || 0;

          // Build update data for this specific allowance type
          const updateData = { Amount: amount };

          // Add FBP declaration if provided
          if (item.fbpMaxDeclaration !== undefined && item.fbpMaxDeclaration !== null) {
            updateData.FBP_Max_Declaration = parseFloat(item.fbpMaxDeclaration) || 0;
          }

          // Create individual update promise
          const updatePromise = organizationDbConnection(ehrTables.employeeSalaryAllowance)
            .where('Employee_Id', args.employeeId)
            .where('Allowance_Type_Id', allowanceTypeId)
            .update(updateData)
            .transacting(trx);

          updatePromises.push(updatePromise);
        });

        // Execute all updates in parallel within the same transaction
        const results = await Promise.all(updatePromises);

        // Verify that records were actually updated
        const totalUpdated = results.reduce((sum, result) => sum + result, 0);
        if (totalUpdated === 0) {
          console.warn('No records were updated. Employee may not have these allowance types configured.');
        }

        // Update employee_salary_details table with audit information
        await organizationDbConnection(ehrTables.employeeSalaryDetails)
          .where('Employee_Id', args.employeeId)
          .update({
            Updated_On: currentTimestamp,
            Updated_By: loginEmployeeId
          })
          .transacting(trx);
      }

      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection,
        message: `FBP Allowance updated successfully`
      });

      return {
        errorCode: '',
        message: 'FBP allowances updated successfully.'
      };

    });

  } catch (error) {
    console.log('Error in handleFBPAllowanceUpdate:', error);
    throw error;
  }
}

async function getFBPDeclarationAmount(organizationDbConnection, employeeId, allowanceTypeId) {
  try {
    const result = await organizationDbConnection(ehrTables.employeeSalaryAllowance)
      .select('FBP_Max_Declaration')
      .where('Employee_Id', employeeId)
      .where('Allowance_Type_Id', allowanceTypeId)
      .first();

    return result ? result.FBP_Max_Declaration : null;
  } catch (error) {
    console.error('Error in getFBPDeclarationAmount:', error);
    throw error;
  }
}

exports.resolvers = resolvers;
