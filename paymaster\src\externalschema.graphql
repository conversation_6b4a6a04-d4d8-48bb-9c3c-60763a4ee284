# defining custom data type
scalar Date

type Query {
  listSalaryTemplateDetails(
    formId: Int!
    accessFormId: Int
    templateId: Int
    id: Int
    employeeId: Int
    isViewMode: Boolean
    isDropdown: Boolean
    includeHistoricalRecords: Boolean
  ): listSalaryTemplateResponse!
  calculateSalary(employeeId: Int, candidateId: Int, retiralDetails: String!, allowanceDetails: String!, salaryDetails: String!, providentFundConfigurationValue: String!, grossIds: [Int],revisionWithoutArrear: Boolean): calculateSalaryResponse
  calculateSalaryArrears(employeeId: Int!, revisionId: Int!): calculateSalaryArrearsResponse
}

type Mutation {
  addUpdateCandidateSalaryDetails(
    formId: Int
    isEditMode: Boolean!
    candidateId: Int!
    templateId: Int
    effectiveFrom: String
    annualCTC: String!
    annualGrossSalary: String!
    monthlyGrossSalary: String!
    salaryEffectiveMonth: String
    allowance: [CandidateAllowanceInput]
    retirals: [CandidateRetiralInput]
    gross: [CandidateGrossInput]
  ): CandidateSalaryMutationResponse!
}

type listSalaryTemplateResponse {
  errorCode: String
  message: String
  currencySymbol: String
  templateDetails: String
  roundOffSettings: String
  pfSettings: String
  fiscalMonthArray: String
}

type calculateSalaryArrearsResponse{
  errorCode: String
  message: String
}

type calculateSalaryResponse{
  errorCode: String
  message: String
  employeeRetiralDetails: String
  salaryStructure: String
}

type CandidateSalaryMutationResponse {
  errorCode: String
  message: String
}

input CandidateAllowanceInput {
  allowanceTypeId: Int!
  allowanceType: String
  percentage: Float
  amount: Float!
  allowanceWages: String
  fbpMaxDeclaration: Float
}

input CandidateRetiralInput {
  formId: String!
  retiralsId: String
  retiralsType: String!
  employeeRetiralWages: String
  employerRetiralWages: String
  employeeSharePercentage: String
  employerSharePercentage: String
  employeeShareAmount: String
  employerShareAmount: String
  pfEmployeeContribution: String
  pfEmployerContribution: String
  employeeStatutoryLimit: String
  employerStatutoryLimit: String
  eligibleForEPS: Int!
  contributeEpfActualPfWage: Int
  adminCharge: String
  edliCharge: String
  contributionEndMonth: String
  contributionPeriodCompleted: String
}

input CandidateGrossInput {
  grossId: Int!
  amount: Float!
}

schema {
  query: Query
  mutation: Mutation
}

