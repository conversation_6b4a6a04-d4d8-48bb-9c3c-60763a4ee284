//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const { ehrTables } = require('../../common/tablealias');


//Rf1 philhealth - employeers remittance report
module.exports.getAnnualInformationReturnReport = async (parent, args, context, info) => {

    let organizationDbConnection;
    try{
        console.log("Inside getAnnualInformationReturnReport() function ")
        organizationDbConnection= knex(context.connection.OrganizationDb);

        const assessmentYear =  getMonthSequence(args.assessmentYear);

        const[orgDetails, taxDetails, companyLocation, paymentTracker] = await Promise.all([ 

            organizationDbConnection(ehrTables.orgDetails).select('Org_Name').limit(1),

            organizationDbConnection(ehrTables.taxConfiguration).select('TAN').limit(1),

            organizationDbConnection(ehrTables.location+" as L")
            .select('L.Phone','L.Street1', 'L.Street2', 'city.City_Name', 'state.State_Name', 'country.Country_Name', 'L.Pincode')
            .leftJoin(ehrTables.country, 'L.Country_Code','country.Country_Code')
            .leftJoin(ehrTables.state, 'L.State_Id','state.State_Id')
            .leftJoin(ehrTables.city, 'L.City_Id', 'city.City_Id')
            .where('L.Location_Type','MainBranch'),

            organizationDbConnection(ehrTables.tdsPayment +' as TDSP')
            .select('TDSPT.Payment_Date as paymentDate', 'TDSPT.Bank_Name as bankName', 'TDSPT.Document_No as documentNo', 'TDSP.Total_Amount as totalAmount')
            .leftJoin( ehrTables.tdsPaymentTracker + ' as TDSPT', 'TDSPT.Payment_Id', 'TDSP.Payment_Id')
            .whereIn('TDSP.Salary_Month', assessmentYear)
        ]);

        const result = {
            maillingAddress: {
                street1:  companyLocation[0]?.Street1,
                street2: companyLocation[0]?.Street2,
                cityName: companyLocation[0]?.City_Name,
                stateName: companyLocation[0]?.State_Name,
                countryName: companyLocation[0]?.Country_Name,
                pincode: companyLocation[0]?.Pincode,
                phone: companyLocation[0]?.Phone
            },
            identificationNumber: taxDetails[0]?.TAN,
            agentName: orgDetails[0]?.Org_Name,
            paymentDetails: paymentTracker,
            amenededReturn: 'No',
            numberOfSheetAttach: 1,
            withholdingAgent: 'No',
            yearEndAdjustment: 'No',
            
        }

        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return result;

    } catch(err){
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.error('Error in getAnnualInformationReturnReport function main catch block.', err);
        let errResult = commonLib.func.getError(err, 'PFF0015');
        throw new ApolloError(errResult.message, errResult.code)
    }
}

const getMonthSequence = (year) => {
    const months = [];
    let month = 1; // January is 1 
    // Iterate until December of the following year
    while (month <= 12) {
      months.push(`${month},${year}`);
        month++;
    }
    return months;
  };