const { ehrTables } = require("../../common/tablealias");
const knex = require("knex");
const { ApolloError } = require("apollo-server-lambda");
const { formatFieldBasedPadding } = require("../../common/reportsCommonFunctions");
const commonLib = require("@cksiva09/hrapp-corelib").CommonLib;
module.exports.getPND53Report = async (parent, args, context, info) => {
  let organizationDbConnection,errorMessage;
  console.log("inside getPND53Report function");
  try {
    organizationDbConnection = knex(context.connection.OrganizationDb);
    let [employeeDetails, tdsPayment] = await Promise.all([
      getEmployeeDetails(organizationDbConnection, args),
      organizationDbConnection(ehrTables.tdsPayment + " as TDSP")
        .select("TDSPT.Payment_Date as paymentDate")
        .leftJoin(
          ehrTables.tdsPaymentTracker + " as TDSPT",
          "TDSPT.Payment_Id",
          "TDSP.Payment_Id"
        )
        .where("TDSP.Salary_Month", args.payRollMonth),
    ]);
    if(!employeeDetails.length || !tdsPayment.length){
      if (!employeeDetails.length) {
        errorMessage = "Payslip details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!tdsPayment.length) {
        errorMessage = "TDS payment details are unavailable. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    }
    employeeDetails=employeeDetails.map((x,i) => {
      const finalObj = {
        Sequence_Number: formatFieldBasedPadding(i + 1, fieldLengths.sequenceNumber),
        Amount_Paid: formatFieldBasedPadding(x.taxableSalaryCount || 0,fieldLengths.taxableSalaryCount),
        Amount_of_Tax_Deducted: formatFieldBasedPadding(x.deductionSalaryCount || 0,fieldLengths.deductionSalaryCount),
        Taxpayer_Identification_Number: formatFieldBasedPadding(
          String(x.taxpayerIdentificationNumber || ''),
          fieldLengths.taxpayerIdentificationNumber
        ),
        Tax_Rate: "5%",
        Tax_Deduction_Conditions:
          "Tax is deducted at the time of payment based on the service provided or contract terms",
        Name: formatFieldBasedPadding(String(x.name || ''), fieldLengths.name),
        Building_Name: formatFieldBasedPadding(String(x.pApartment_Name || ''),fieldLengths.pApartment_Name),
        Street: formatFieldBasedPadding(String(x.pStreet_Name || ''),fieldLengths.pStreet_Name),
        District: formatFieldBasedPadding(String(x.pCity || ''),fieldLengths.pCity),
        Province: formatFieldBasedPadding(String(x.pState || ''),fieldLengths.pState),
        Postal_Code: formatFieldBasedPadding(String(x.pPincode || ''),fieldLengths.pPincode),
        Payment_Date:tdsPayment[0] && tdsPayment[0].paymentDate?tdsPayment[0].paymentDate:"",
        Blank: ' '.repeat(fieldLengths.Blank)
      };
      return finalObj;
    });
    const withHoldTaxDetail = {
      employeeDetails: JSON.stringify(employeeDetails),
      errorCode: "",
      message: "PND 53 Report has been fetched successfully.",
    };

    organizationDbConnection.destroy();

    return withHoldTaxDetail;
  } catch (err) {
    organizationDbConnection.destroy();
    console.error("Error in getPND53Report function main catch block.", err);
    if(err == "PFF0018"){
      throw new ApolloError(errorMessage, err);
    }
    throw new ApolloError(
      commonLib.func.getError(err, "PFF0017").message,
      commonLib.func.getError(err, "PFF0017").code
    );
  }
};

async function getEmployeeDetails(organizationDbConnection, args) {
  return organizationDbConnection(ehrTables.empJob + " as EJ")
    .select(
      "EJ.Employee_Id as employeeId",
      "MFS.Current_Month_Taxable_Salary as taxableSalaryCount",
      "SD.Deduction_Amount as deductionSalaryCount",
      "EPI.Aadhaar_Card_Number as taxpayerIdentificationNumber",
      organizationDbConnection.raw(
        "CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as name"
      ),
      "CD.pApartment_Name",
      "CD.pStreet_Name",
      "CD.pCity",
      "CD.pState",
      "CD.pCountry",
      "CD.pPincode",
      "CD.Mobile_No",
      "CD.Mobile_No_Country_Code"
    )
    .modify((queryBuilder) => {
      if (args.serviceProviderId) {
        queryBuilder.where("EJ.Service_Provider_Id", args.serviceProviderId);
      }
    })
    .leftJoin(
      ehrTables.empPersonalInfo + " as EPI",
      "EPI.Employee_Id",
      "EJ.Employee_Id"
    )
    .leftJoin(
      ehrTables.contactDetails + " as CD",
      "CD.Employee_Id",
      "EPI.Employee_Id"
    )
    .innerJoin(
      ehrTables.salaryPayslip + " as SP",
      "SP.Employee_Id",
      "EJ.Employee_Id"
    )
    .where("SP.Salary_Month", args.payRollMonth)
    .innerJoin(
      ehrTables.salaryDeduction + " as SD",
      "SP.Payslip_Id",
      "SD.Payslip_Id"
    )
    .innerJoin(
      ehrTables.monthlyForm16Snapshot + " as MFS",
      "MFS.Payslip_Id",
      "SP.Payslip_Id"
    );
}

const fieldLengths = {
  sequenceNumber:5,
  taxableSalaryCount:15,
  deductionSalaryCount:15,
  taxpayerIdentificationNumber:13,
  name:320,
  pApartment_Name:100,
  pStreet_Name:60,
  pCity :100,
  pState:100,
  pCountry:100,
  pPincode:5,
  Blank:27
};
