// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError } = require('apollo-server-lambda');
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const { formId } = require('../common/appconstants');
const moment = require('moment-timezone');

// resolver definition
const resolvers = {
    Query: {
        // function to get FBP or NPS declaration settings
        getFBPDeclarationSettings: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside getFBPDeclarationSettings function');
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                const typeOfDeclaration = args.typeOfDeclaration || 'FBP';

                const result = await getFBPDeclarationSettingsLogic(organizationDbConnection, loggedInEmpId, typeOfDeclaration);

                return {
                    errorCode: '',
                    message: 'Declaration settings retrieved successfully.',
                    success: true,
                    lockStatus: result
                };
            }
            catch (mainCatchError) {
                console.log('Error in getFBPDeclarationSettings function main catch block', mainCatchError);

                const errResult = commonLib.func.getError(mainCatchError, 'PFF0018');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) organizationDbConnection.destroy();
            }
        },

        getAllowanceWithRegime: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside getAllowanceWithRegime function');
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100';
                }

                const allowanceWithRegime = await getAllowanceWithRegimeLogic(organizationDbConnection);

                return {
                    errorCode: '',
                    message: 'Allowance with regime retrieved successfully.',
                    allowanceWithRegime: JSON.stringify(allowanceWithRegime)
                };
            }
            catch (mainCatchError) {
                console.log('Error in getAllowanceWithRegime function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0027');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) organizationDbConnection.destroy();
            }
        },

        // function to get perquisites based on formId
        getPerquisites: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside getPerquisites function');
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Validate formId
                if (!args.formId || ![381, 382, 383,407].includes(args.formId)) {
                    throw 'IVE0658';
                }

                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );

                if (Object.keys(checkRights).length === 0) {
                    throw '_DB0100';
                }

                // Get perquisites based on formId
                const perquisites = await getPerquisitesByFormId(organizationDbConnection, args.formId);

                return {
                    errorCode: '',
                    message: 'Perquisites retrieved successfully.',
                    success: true,
                    perquisites: JSON.stringify(perquisites)
                };
            }
            catch (mainCatchError) {
                console.log('Error in getPerquisites function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0027');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        },

        // function to get benefit forms based on formId
        getBenefitForms: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside getBenefitForms function');
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Validate formId
                if (!args.formId || ![381, 382, 383,407].includes(args.formId)) {
                    throw 'IVE0658';
                }
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );

                if (Object.keys(checkRights).length === 0) {
                    throw '_DB0100';
                }

                // Get benefit forms (skip Adhoc Allowance logic as requested)
                const benefitForms = await getBenefitFormsByFormId(organizationDbConnection, args.formId, args.all);

                return {
                    errorCode: '',
                    message: 'Benefit forms retrieved successfully.',
                    success: true,
                    benefitForms: JSON.stringify(benefitForms)
                };
            }
            catch (mainCatchError) {
                console.log('Error in getBenefitForms function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0027');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        },

        // function to list allowance types
        listAllowanceType: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside listAllowanceType function');
                const loggedInEmpId = context.logInEmpId;
                const { formId: requestedFormId } = args; // Get formId from arguments

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // check access right based on employeeid
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                 args.formId // formId for allowances

                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100';
                }

                // get allowance types data
                const allowanceTypesData = await getAllowanceTypes(organizationDbConnection, requestedFormId);

                // return response
                return {
                    errorCode: '',
                    message: 'Allowance types listed successfully.',
                    allowanceTypes: JSON.stringify(allowanceTypesData)
                };
            }
            catch (mainCatchError) {
                console.log('Error in listAllowanceType function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0025');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        },
        getEmployeeEffectiveDate: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside getEmployeeEffectiveDate function');
                organizationDbConnection = knex(context.connection.OrganizationDb);
                // Check access rights for the provided formId
                const loggedInEmpId = context.logInEmpId;
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );
                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100'; // Access denied
                }
                // Execute all queries in parallel for better performance
                const [effectiveDateResult, overtimeDetails] = await Promise.all([
                    // Get date of join based on salary type and operation
                    getDateOfJoinForEffectiveDate(organizationDbConnection, args.employeeId, 'Monthly', args.operation, context.orgCode),
                    // Get grade-wise overtime details
                    getGradeWiseOvertimeDetailsForEffectiveDate(organizationDbConnection, args.employeeId)
                ]);
                return {
                    errorCode: '',
                    message: 'Employee effective date details retrieved successfully',
                    success: true,
                    effectiveDate: effectiveDateResult?.effectiveDate || null,
                    maxPayslipMonth: effectiveDateResult?.maxPayslipMonth || null,
                    overtimeAllocation: overtimeDetails?.Overtime_Allocation || null,
                    wageIndex: overtimeDetails?.Overtime_Wage_Index || null,
                    overtimeAmount: overtimeDetails?.OvertimeFixedAmount || null
                };
            } catch (mainCatchError) {
                console.log('Error in getEmployeeEffectiveDate function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0027');
                throw new ApolloError(errResult.message, errResult.code);
            } finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

// function to get allowance types
async function getAllowanceTypes(organizationDbConnection, requestedFormId) {
    try {
        return await organizationDbConnection.transaction(async (trx) => {
            let allowanceTypeFilter = {};

            if (requestedFormId === 381) { // Earnings
                allowanceTypeFilter = {
                    'AT.Allowance_Mode': 'Non Bonus',
                    'AT.Is_Claim_From_Reimbursement': 'No'
                };
            } else if (requestedFormId === 382) { // Reimbursement
                allowanceTypeFilter = {
                    'AT.Allowance_Mode': 'Non Bonus',
                    'AT.Is_Claim_From_Reimbursement': 'Yes'
                };
            } else if (requestedFormId === 383) { // Bonus
                allowanceTypeFilter = {
                    'AT.Allowance_Mode': 'Bonus',
                    'AT.Is_Claim_From_Reimbursement': 'No'
                };
            }
            // Optimized: Get basic allowance type data with employee names
            let allowanceTypesQuery = organizationDbConnection(ehrTables.allowanceType + ' as AT')
                .select(
                    'AT.Allowance_Type_Id',
                    'AT.Allowance_Name',
                    'AT.Name_In_Payslip',
                    'AT.Calculation_Type',
                    'AT.Allowance_Percentage',
                    'AT.Allowance_Amount',
                    'AT.Custom_Formula',
                    'AT.FBP_Max_Declaration_Amount',
                    'AT.Restrict_Employee_FBP_Override',
                    'AT.Tax_Inclusion',
                    'AT.Formula_Based',
                    'AT.Reimbursement_Type',
                    'AT.Unclaimed_LTA',
                    'AT.Allowance_Mode',
                    'AT.Period',
                    'AT.Is_Basic_Pay',
                    'AT.AllowanceType_Status',
                    'AT.Description',
                    'AT.Is_Flexi_Benefit_Plan',
                    'AT.Is_Claim_From_Reimbursement',
                    'AT.Perquisites_Id',
                    'AT.Allowance_As_Is_Payment',
                    'AT.Salary_Component_Id',
                    'SC.Component_Code as Salary_Component_Code',
                    'SC.Component_Name as Salary_Component_Name',
                    'PP.Perquisites_Name',
                    'AT.Workflow_Id',
                    'WF.Workflow_Name',
                    'AT.Consider_For_EPF_Contribution',
                    organizationDbConnection.raw("DATE_FORMAT(AT.Added_On, '%Y-%m-%d %H:%i:%s') as Added_On"),
                    organizationDbConnection.raw("DATE_FORMAT(AT.Updated_On, '%Y-%m-%d %H:%i:%s') as Updated_On"),
                    'AT.Added_By',
                    'AT.Updated_By',
                    // Employee name concatenations
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI_Added.Emp_First_Name, EPI_Added.Emp_Middle_Name, EPI_Added.Emp_Last_Name) as Added_By_Name"),
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI_Updated.Emp_First_Name, EPI_Updated.Emp_Middle_Name, EPI_Updated.Emp_Last_Name) as Updated_By_Name"),
                    // Transform Unclaimed_LTA for display
                    organizationDbConnection.raw(`CASE
                        WHEN AT.Unclaimed_LTA = "Carry Forward Unclaimed LTA"
                        THEN "Carry forward unclaimed LTA to the next financial year"
                        ELSE "Encash unclaimed LTA at the end of each financial year"
                    END as UnclaimedLTA`)
                )
                // Essential joins for basic data and employee names
                .leftJoin(ehrTables.perquisites + ' as PP', 'PP.Perquisites_Id', 'AT.Perquisites_Id')
                .leftJoin(ehrTables.workflows + ' as WF', 'WF.Workflow_Id', 'AT.Workflow_Id')
                .leftJoin('salary_components as SC', 'SC.Component_Id', 'AT.Salary_Component_Id')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI_Added', 'EPI_Added.Employee_Id', 'AT.Added_By')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI_Updated', 'EPI_Updated.Employee_Id', 'AT.Updated_By')
                .orderBy('AT.Allowance_Name', 'asc')
                .transacting(trx);

            // Apply formId-based filtering if specified
            if (Object.keys(allowanceTypeFilter).length > 0) {
                Object.keys(allowanceTypeFilter).forEach(key => {
                    allowanceTypesQuery = allowanceTypesQuery.where(key, allowanceTypeFilter[key]);
                });
            }

            const allowanceTypes = await allowanceTypesQuery;

            // Get all required data in separate optimized queries to avoid complex JOINs
            const allowanceTypeIds = allowanceTypes.map(at => at.Allowance_Type_Id);

            // Get benefit associations in a single query
            let benefitAssociations = {};
            if (allowanceTypeIds.length > 0) {
                // Special Form IDs that need Form_Name from ehr_forms table (same as getBenefitFormsByFormId)
                const specialFormIds = [280, 281, 110, 90, 126, 52, 58];

                const benefits = await organizationDbConnection('allowance_type_benefit_association as ABA')
                    .select('ABA.Allowance_Type_Id', 'BF.Form_Id', 'BF.Display_Form_Name', 'EF.Form_Name as EHR_Form_Name')
                    .innerJoin(ehrTables.benefitForms + ' as BF', 'BF.Form_Id', 'ABA.Form_Id')
                    .leftJoin(ehrTables.ehrForms + ' as EF', 'EF.Form_Id', 'BF.Form_Id')
                    .whereIn('ABA.Allowance_Type_Id', allowanceTypeIds)
                    .transacting(trx);

                benefits.forEach(benefit => {
                    if (!benefitAssociations[benefit.Allowance_Type_Id]) {
                        benefitAssociations[benefit.Allowance_Type_Id] = {
                            names: [],
                            ids: []
                        };
                    }
                    // Use EHR_Form_Name for special form IDs, otherwise use Display_Form_Name
                    const formName = specialFormIds.includes(benefit.Form_Id) && benefit.EHR_Form_Name
                        ? benefit.EHR_Form_Name
                        : benefit.Display_Form_Name;

                    benefitAssociations[benefit.Allowance_Type_Id].names.push(formName);
                    benefitAssociations[benefit.Allowance_Type_Id].ids.push(benefit.Form_Id);
                });
            }

            // Get salary template associations in a single query
            let templateAssociations = [];
            if (allowanceTypeIds.length > 0) {
                templateAssociations = await organizationDbConnection(ehrTables.salaryTemplate + ' as ST')
                    .select('ST.Template_Id', 'ST.Template_Name', 'TAC.Allowance_Type_Id')
                    .innerJoin(ehrTables.templateAllowanceComponents + ' as TAC', 'TAC.Template_Id', 'ST.Template_Id')
                    .where('ST.Template_Status', 'Active')
                    .whereIn('TAC.Allowance_Type_Id', allowanceTypeIds)
                    .transacting(trx);
            }

            // Create a map of allowance type ID to associated templates
            const templateMap = {};
            templateAssociations.forEach(association => {
                if (!templateMap[association.Allowance_Type_Id]) {
                    templateMap[association.Allowance_Type_Id] = [];
                }
                templateMap[association.Allowance_Type_Id].push({
                    Template_Id: association.Template_Id,
                    Template_Name: association.Template_Name
                });
            });

            // Process the results and add all the pre-fetched data
            allowanceTypes.forEach(allowanceType => {
                // Add benefit associations from pre-fetched data
                const benefits = benefitAssociations[allowanceType.Allowance_Type_Id];
                allowanceType.Benefits_Name = benefits ? benefits.names : [];
                allowanceType.Benefits_Association = benefits ? benefits.ids : [];

                // Add salary template information from the pre-built map
                allowanceType.Associated_Templates = templateMap[allowanceType.Allowance_Type_Id] || [];
            });

            return allowanceTypes;
        });
    } catch (error) {
        console.log('Error in getAllowanceTypes function:', error);
        throw error;
    }
}

// Get perquisites based on formId (converted from PHP getPerquisites function)
async function getPerquisitesByFormId(dbConnection, formId) {
    try {
        let allowanceDetails = [];

        // Get organization details for assessment year
        const orgDetails = await dbConnection(ehrTables.orgDetails).first();

            // Get perquisite tracker details
            const perquisiteTrackerDetails = await dbConnection(ehrTables.perquisiteTracker + ' as PT')
                .select('PTA.Perquisite_Id')
                .innerJoin(ehrTables.perquisiteTrackerAmt + ' as PTA', 'PTA.Perquisite_Tracker_Id', 'PT.Perquisite_Tracker_Id')
                .where('PT.Assessment_Year', orgDetails.Assessment_Year)
                .groupBy('PTA.Perquisite_Id')
                .pluck('PTA.Perquisite_Id');

            // Get expense type details
            const expenseTypeDetails = await dbConnection(ehrTables.expenseTypes + ' as ET')
                .select('Perquisites_Id')
                .whereNotNull('Perquisites_Id')
                .groupBy('ET.Perquisites_Id')
                .pluck('Perquisites_Id');

            allowanceDetails = [...perquisiteTrackerDetails, ...expenseTypeDetails];

        // Get perquisites details excluding already used ones
        let perquisitesQuery = dbConnection(ehrTables.perquisites + ' as P')
            .select('P.Perquisites_Id', 'P.Perquisites_Name')
            .orderBy('P.Perquisites_Name', 'ASC');

        if (allowanceDetails.length > 0) {
            perquisitesQuery = perquisitesQuery.whereNotIn('P.Perquisites_Id', allowanceDetails);
        }

        const perquisitesResult = await perquisitesQuery;

        // Convert to key-value pairs like PHP fetchPairs
        const perquisitesPairs = {};
        perquisitesResult.forEach(item => {
            perquisitesPairs[item.Perquisites_Id] = item.Perquisites_Name;
        });

        return perquisitesPairs;

    } catch (error) {
        console.log('Error in getPerquisitesByFormId:', error);
        throw error;
    }
}

async function getGradeWiseOvertimeDetailsForEffectiveDate(organizationDbConnection, employeeId) {
  try {
    const overtimeDetails = await organizationDbConnection(ehrTables.empJob + ' as J')
      .select([
        'EG.Overtime_Allocation',
        'EG.Overtime_Wage_Index',
        'EG.OvertimeFixedAmount'
      ])
      .innerJoin(ehrTables.empPersonalInfo + ' as P', 'J.Employee_Id', 'P.Employee_Id')
      .innerJoin(ehrTables.empGrade + ' as EG', 'EG.Grade_Id', 'J.Grade_Id')
      .where('J.Employee_Id', employeeId)
      .where('P.Form_Status', 1)
      .where('J.Emp_Status', 'Active')
      .first();
    return overtimeDetails;
  } catch (error) {
    console.error('Error in getGradeWiseOvertimeDetailsForEffectiveDate:', error);
    throw error;
  }
}
// Get benefit forms based on formId (converted from PHP getBenefitForms function)
async function getBenefitFormsByFormId(dbConnection, formId, all = false) {
    try {
        // Skip 'Adhoc Allowance' logic as requested, implement only the else part
        // Note: formId parameter is kept for API consistency but not used in current logic
        const onlyAdhocFormIds = [90]; // Professional Tax

        // Special Form IDs that need Form_Name from ehr_forms table
        const specialFormIds = [280, 281, 110, 90, 126, 52, 58];

        let query = dbConnection(ehrTables.benefitForms + ' as BF')
            .select(
                'BF.Form_Id',
                'BF.Display_Form_Name',
                'EF.Form_Name as EHR_Form_Name',
                'BF.Display_Order',
                'BF.Visiblity'
            )
            .leftJoin(ehrTables.ehrForms + ' as EF', 'EF.Form_Id', 'BF.Form_Id')
            .orderBy('BF.Display_Order', 'ASC')
            .modify(function (queryBuilder) {
               if(formId===407){
                queryBuilder.whereIn('BF.Form_Id', [126,52,90,58,326,327]);
               }
               else{
                queryBuilder.whereNotIn('BF.Form_Id', onlyAdhocFormIds);
               }
            });

        // Apply visibility filter only if 'all' flag is not true
        if (!all) {
            query = query.where('BF.Visiblity', 'Yes');
        }

        const benefitFormsResult = await query;

        // Convert to array of objects with id and name keys
        const benefitFormsPairs = benefitFormsResult.map(item => {
            // Use EHR_Form_Name for special form IDs, otherwise use Display_Form_Name
            const formName = specialFormIds.includes(item.Form_Id) && item.EHR_Form_Name
                ? item.EHR_Form_Name
                : item.Display_Form_Name;

            return {
                id: item.Form_Id,
                name: formName
            };
        });

        return JSON.stringify(benefitFormsPairs);

    } catch (error) {
        console.log('Error in getBenefitFormsByFormId:', error);
        throw error;
    }
}

/**
 * Get employee's date of join based on salary type and operation
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} employeeId - Employee ID
 * @param {string} salaryType - Salary type (Monthly/hourly)
 * @param {string} operation - Operation type (Edit/Add)
 * @param {string} orgCode - Organization code
 * @returns {Object} - Object containing effectiveDate and maxPayslipMonth
 */
async function getDateOfJoinForEffectiveDate(organizationDbConnection, employeeId, salaryType, operation, orgCode) {
  try {
    let maxPayslipMonth = null;

    if (operation.toLowerCase() === 'edit') {
      // Get the last payslip month using common library function
      maxPayslipMonth = await commonLib.func.maxPayslipMonth(organizationDbConnection, employeeId, salaryType);
      if (maxPayslipMonth) {
        const maxPayslipDateInArray = maxPayslipMonth.split('-');
        const year = maxPayslipDateInArray[0];   // Year is at index 0
        let month = maxPayslipDateInArray[1];    // Month is at index 1

        // Handle invalid month (00) - convert to valid month
        if (month === '00') {
          month = '01'; // Default to January if month is 00
        }
        month = parseInt(month);
        if (month < 1 || month > 12) {
          month = 1;
        }

        const lastSalaryDay = await commonLib.func.getSalaryDay(
          orgCode,
          organizationDbConnection,
          month.toString(),
          year
        );
        if (lastSalaryDay && lastSalaryDay.Last_SalaryDate && lastSalaryDay.Last_SalaryDate !== 'Invalid date') {
          const nextDay = moment(lastSalaryDay.Last_SalaryDate);
          if (nextDay.isValid()) {
            return {
              effectiveDate: nextDay.add(1, 'day').format('YYYY-MM-DD'),
              maxPayslipMonth: maxPayslipMonth
            };
          } else {
            console.log('Invalid Last_SalaryDate, falling back to salary details');
          }
        } else {
          console.log('No valid lastSalaryDay found, falling back to salary details');
        }
      }
      const salaryQuery = organizationDbConnection(ehrTables.salary + ' as S')
        .select('S.Effective_Date')
        .where('S.Employee_Id', employeeId)
        .first();

      const salaryDetails = await salaryQuery;

      if (salaryDetails && salaryDetails.Effective_Date) {
        return {
          effectiveDate: moment(salaryDetails.Effective_Date).format('YYYY-MM-DD'),
          maxPayslipMonth: maxPayslipMonth
        };
      }
    }

    const salaryQuery = organizationDbConnection(ehrTables.salary + ' as S')
        .select('S.Effective_Date')
        .where('S.Employee_Id', employeeId)
        .first();

      const salaryDetails = await salaryQuery;

      if (salaryDetails && salaryDetails.Effective_Date) {
        return {
          effectiveDate: moment(salaryDetails.Effective_Date).format('YYYY-MM-DD'),
          maxPayslipMonth: maxPayslipMonth
        };
      }
    // For Add operation or fallback, get date of join
    const dateOfJoin = await organizationDbConnection(ehrTables.empJob + ' as J')
      .select('J.Date_Of_Join')
      .innerJoin(ehrTables.empPersonalInfo + ' as P', 'J.Employee_Id', 'P.Employee_Id')
      .where('J.Employee_Id', employeeId)
      .where('P.Form_Status', 1)
      .where('J.Emp_Status', 'Active')
      .first();

    return {
      effectiveDate: dateOfJoin ? moment(dateOfJoin.Date_Of_Join).format('YYYY-MM-DD') : null,
      maxPayslipMonth: maxPayslipMonth
    };

  } catch (error) {
    console.error('Error in getDateOfJoinForEffectiveDate:', error);
    throw error;
  }
}

/**
 * Get FBP declaration settings logic
 * @param {Object} organizationDbConnection - Database connection
 * @param {string} logEmpId - Logged in employee ID
 * @param {string} typeOfDeclaration - Type of declaration ('NPS' or 'FBP')
 * @returns {Promise<string>} - Returns 'lock' or 'unlock' status
 */
async function getFBPDeclarationSettingsLogic(organizationDbConnection, logEmpId, typeOfDeclaration) {
    try {
        const tableName = typeOfDeclaration === 'NPS' ? 'nps_declaration_settings' : 'fbp_declaration_settings';

        // Get FBP tax declaration settings
        const fbpTaxDeclarationSettings = await organizationDbConnection(tableName)
            .select('Lock_Date', 'New_Joiners_Grace_Days')
            .first();

            console.log('fbpTaxDeclarationSettings:', fbpTaxDeclarationSettings);

        if (!fbpTaxDeclarationSettings) {
            return 'unlock';
        }

        // Get employee date of join
        const employeeDetails = await organizationDbConnection(ehrTables.empJob + ' as EJ')
            .select('EJ.Date_Of_Join')
            .leftJoin(ehrTables.empResignation + ' as R', function() {
                this.on('EJ.Employee_Id', '=', 'R.Employee_Id')
                    .onIn('R.Approval_Status', organizationDbConnection.raw('(?,?)', ['Applied', 'Approved']))
            })
            .where('EJ.Employee_Id', logEmpId)
            .first();

        if (!employeeDetails || !employeeDetails.Date_Of_Join) {
            return 'unlock';
        }

        // Calculate FBP lock date based on date of join + grace days
        const dateOfJoin = moment(employeeDetails.Date_Of_Join);
        const graceDays = parseInt(fbpTaxDeclarationSettings.New_Joiners_Grace_Days) || 0;
        const fbpLockDateOfJoin = dateOfJoin.clone().add(graceDays, 'days');

        // Format dates for comparison (YYYY-MM-DD)
        const fbpLockDateOfJoinFormatted = fbpLockDateOfJoin.format('YYYY-MM-DD');
        const lockDateFormatted = moment(fbpTaxDeclarationSettings.Lock_Date).format('YYYY-MM-DD');
        const currentDate = moment().format('YYYY-MM-DD');

        // Determine final lock date
        let finalLockDate = lockDateFormatted;
        if (moment(fbpLockDateOfJoinFormatted).isAfter(moment(lockDateFormatted))) {
            finalLockDate = fbpLockDateOfJoinFormatted;
        }

        // Check if current date is greater than or equal to lock date
        if (moment(currentDate).isSameOrBefore(moment(finalLockDate))) {
            return {
                status: 'unlock',
                lockDate: finalLockDate
            };
            // return 'unlock';
        } else {
            return {
                status: 'lock',
                lockDate: finalLockDate
            };
            // return 'lock';
        }

    } catch (error) {
        console.log('Error in getFBPDeclarationSettingsLogic:', error);
        throw error;
    }
}

async function getAllowanceWithRegimeLogic(organizationDbConnection) {
    try {
        const allowanceWithRegime = await organizationDbConnection(ehrTables.sectionInvestmentCategory)
            .select('Allowance_Type_Id', 'Tax_Regime')
            .where('Tax_Status', 'Active')

        return allowanceWithRegime;

    } catch (error) {
        console.log('Error in getAllowanceWithRegimeLogic:', error);
        throw error;
    }
}

module.exports.resolvers = resolvers;
module.exports.getDateOfJoinForEffectiveDate = getDateOfJoinForEffectiveDate;
