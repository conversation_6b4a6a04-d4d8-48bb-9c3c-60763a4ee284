
const commonLib = require("@cksiva09/hrapp-corelib").CommonLib;
const { ehrTables } = require("../common/tablealias");
async function getMainBranchLocation(
    organizationDbConnection,
    orgCode,
    appManagerDbConnection,
    args
  ) {
    let locationId = null,
      organizationName = "";
  
    try {
      var filedForce = await commonLib.func.getOrgDetails(
        orgCode,
        organizationDbConnection,
        0,
        1
      );
      if (filedForce && args.serviceProviderId) {
        let serviceProvider = await organizationDbConnection("emp_job")
          .select(
            "service_provider.Service_Provider_Name",
            "service_provider.Location_Id"
          )
          .leftJoin(
            "service_provider",
            "service_provider.Service_Provider_Id",
            "emp_job.Service_Provider_Id"
          )
          .where("emp_job.Service_Provider_Id", args.serviceProviderId)
          .first();
  
        organizationName = serviceProvider?.Service_Provider_Name || "";
        locationId = serviceProvider?.Location_Id || null;
      } else if (!locationId || filedForce) {
        if (appManagerDbConnection) {
          let orgDetails = await appManagerDbConnection("hrapp_registeruser")
            .select("Org_Name")
            .where("Org_Code", orgCode)
            .first();
          organizationName = orgDetails.Org_Name || "";
        } else {
          let orgDetails = organizationDbConnection(ehrTables.orgDetails)
            .select("Org_Name")
            .where("Org_Code", orgCode)
            .first();
          organizationName = orgDetails.Org_Name || "";
        }
      }
  
      let location = await organizationDbConnection("location")
        .select(
          "location.Street1",
          "location.Street2",
          "location.Pincode",
          "state.State_Name",
          "country.Country_Name",
          "city.City_Name",
          "location.Phone"
        )
        .leftJoin("country", "location.Country_Code", "country.Country_Code")
        .leftJoin("state", "location.State_Id", "state.State_Id")
        .leftJoin("city", "location.City_Id", "city.City_Id")
        .where((qb) => {
          if (locationId) {
            qb.where("location.Location_Id", locationId);
          } else {
            qb.where("location.Location_Type", "MainBranch");
          }
        });
      return { location: location, organizationName: organizationName };
    } catch (error) {
      console.log("Error in getMainBranchLocation", error);
      throw error;
    }
  }
  async function getTaxDetails(organizationDbConnection, args) {
    return organizationDbConnection(ehrTables.taxConfiguration)
      .select("Service_Provider_Id", "Org_Type", "TAN")
      .modify((queryBuilder) => {
        if (args.serviceProviderId) {
          queryBuilder.where("Service_Provider_Id", args.serviceProviderId);
        } else {
          queryBuilder.limit(1);
        }
      });
  }
  const getMonthSequence = (year) => {
    let months = [];
    let month = 1; // January is 1 
    // Iterate until December of the following year
    while (month <= 12) {
      months.push(`${month},${year}`);
        month++;
    }
    return months;
  };
  async function getCompanyDetails(organizationDbConnection) {
    return organizationDbConnection(ehrTables.orgDetails)
      .select("Org_Name")
      .limit(1);
  }
  const formatFieldBasedPadding = (value, length) => {
    try {
        // Check for null, undefined, or empty values
        if (value == null || value === '') {
          if(value === null || value === undefined){
             return '0'.repeat(length); // Return zeros for empty or null values
          }
          else{
            return ' '.repeat(length);
          }
           
        }

        // If the value is a non-numeric string, add an extra space and pad with spaces on the right
        if (typeof value === 'string' && isNaN(value)) {
            return (` ${value}`).padEnd(length, ' ');
        }

        // If the value is a number (or numeric string), process as a number
        if (!isNaN(value) && typeof value !== 'boolean') {
            // Convert value to a string and remove the decimal point
            const formattedValue = String(value).replace('.', '');

            // Right-align and pad with zeros to the left
            const paddedValue = formattedValue.padStart(length, '0');

            // Truncate if the number exceeds the field length
            return paddedValue.length <= length ? paddedValue : paddedValue.slice(0, length);
        }

        // Default case: pad other values with spaces on the right
        return String(value).padEnd(length, ' ');
    } catch (e) {
        console.log('Error in formatFieldBasedPadding', e);
        throw e;
    }
};
  module.exports={
    getMainBranchLocation,
    getTaxDetails,
    getMonthSequence,
    getCompanyDetails,
    formatFieldBasedPadding
  }