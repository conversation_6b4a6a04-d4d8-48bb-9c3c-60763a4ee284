// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// Organization database connection
const knex = require('knex');
// require common constant files
const constants = require('../common/appconstants');
// require table alias
const {ehrTables} = require('../common/tablealias');

// variable declarations
let errResult = {};
let organizationDbConnection = '';
let checkRights=false;

// resolver definition
const resolvers = {
    Mutation: {
        // function to update template status
        updateTemplateStatus: async (parent, args, context, info) => {
            try{
                console.log('Inside updateTemplateStatus function');
                let loggedInEmpId=context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                
                // Check update access right based on employeeid
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loggedInEmpId,constants.formName.salaryTemplate,constants.roles.roleUpdate);
                
                if (checkRights === true) {
                    // calculate timezone based on loggedInEmpId
                    let employeeTimeZone= await commonLib.func.getEmployeeTimeZone(loggedInEmpId, organizationDbConnection, 1);

                    return(
                        organizationDbConnection
                        .transaction(function(trx){
                            return(
                                // update the template status and timestamp in salary_template table
                                organizationDbConnection(ehrTables.salaryTemplate)
                                .update({
                                    Template_Status : args.templateStatus,
                                    Updated_On    : employeeTimeZone,
                                    Updated_By    : loggedInEmpId
                                })
                                .where('Template_Id',args.templateId)
                                .transacting(trx)
                                .then(async(updateStatus) =>{
                                    // form system log parameters
                                    systemLogParams = {
                                        action : constants.systemLogs.roleUpdate,
                                        userIp : context.userIp,
                                        employeeId: loggedInEmpId,
                                        formName : constants.formName.salaryTemplate,
                                        trackingColumn: (args.templateStatus.toLowerCase()==='active')?'Status - Activate the template':'Status - Inactivate the template',
                                        organizationDbConnection: organizationDbConnection,
                                        uniqueId : args.templateId
                                    }
                                    // call function to update system log activities
                                    await commonLib.func.createSystemLogActivities(systemLogParams);
                                    return { errorCode : '', message : 'Template status updated successfully.' };
                                })
                            )
                        })
                        .then(function (result) {
                            return result;
                        })
                        //catch db-connectivity errors
                        .catch(function (catchError) {
                            console.log('Error in updateTemplateStatus function .catch() block', catchError);
                            errResult = commonLib.func.getError(catchError, 'PST0109');
                            throw new ApolloError(errResult.message,errResult.code);
                        })
                        /**close db connection */
                        .finally(() => {
                            organizationDbConnection.destroy();
                        })                    
                    )
                }
                else if (checkRights === false) {
                    throw '_DB0102';
                } else {
                    // throw error
                    throw (checkRights);
                }
            } catch (updateStatusMainCatch){
                console.log('Error in updateTemplateStatus function main catch block',updateStatusMainCatch);
                // destroy database connection
                (organizationDbConnection)?organizationDbConnection.destroy():'';
                errResult = commonLib.func.getError(updateStatusMainCatch, 'PST0016');
                throw new ApolloError(errResult.message,errResult.code);
            }
        }
    }
}

exports.resolvers = resolvers;