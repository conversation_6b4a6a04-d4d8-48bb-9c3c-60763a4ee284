//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const { ehrTables } = require('../../common/tablealias');

//List the work schedule details in the work schedule form
module.exports.getR5ContributionReport = async (parent, args, context, info) => {

    let organizationDbConnection;
    try{
        console.log("Inside getR5ContributionReport() function ",args, args.assessmentYear)
        organizationDbConnection= knex(context.connection.OrganizationDb);
       
        const [providentFund, orgDetails, taxDetails, companyLocation, paymentDetails] = await Promise.all([
            
                organizationDbConnection(ehrTables.providentFund+' as PF')
                .select('PF.EPF_Number as EPF_Number').limit(1),
        
                organizationDbConnection(ehrTables.orgDetails).select('Org_Name').limit(1),

                organizationDbConnection(ehrTables.taxConfiguration).select('TAN').limit(1),

                organizationDbConnection(ehrTables.location+' as L')
                    .select('L.Street1', 'L.Street2', 'city.City_Name', 'state.State_Name', 'country.Country_Name',
                    'L.Pincode','L.Phone')
                    .leftJoin('country', 'L.Country_Code','country.Country_Code')
                    .leftJoin('state', 'L.State_Id','state.State_Id')
                    .leftJoin('city', 'L.City_Id', 'city.City_Id')
                    .where('L.Location_Type','MainBranch'),

                organizationDbConnection(ehrTables.empPfPayment)
                    .select('Salary_Month as salaryMonth')
                    .sum('Org_ShareAmount as orgShareAmount')
                    .sum('Emp_ShareAmount as empShareAmount')
                    .where('Salary_Month','like', `%${args.assessmentYear}%`)
                    .groupBy('Salary_Month'),

            ]);

            let reportDetails = {
                maillingAddress: {
                    street1:  companyLocation[0]?.Street1,
                    street2: companyLocation[0]?.Street2,
                    cityName: companyLocation[0]?.City_Name,
                    stateName: companyLocation[0]?.State_Name,
                    countryName: companyLocation[0]?.Country_Name,
                    pincode: companyLocation[0]?.Pincode,
                    phone: companyLocation[0]?.Phone
                },
                employerTinNo: taxDetails[0]?.TAN,
                employerName: orgDetails[0]?.Org_Name,
                paymentDetails : paymentDetails,
                orgShareAmountTotal :  paymentDetails.length ? paymentDetails.reduce((accumulator, currentValue) => accumulator + currentValue.orgShareAmount, 0) : 0,
                employerShareTotal : paymentDetails.length ? paymentDetails.reduce((accumulator, currentValue) => accumulator + currentValue.empShareAmount, 0) : 0,
                dateReceived: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                errorCode: "", 
                message: "Social Security Scheme R5 Contribution Report has been fetched successfully.",
            }

            organizationDbConnection ? organizationDbConnection.destroy() : null;
            
            return reportDetails;

    } catch(err){
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.error('Error in getR5ContributionReport function main catch block.', err);
        let errResult = commonLib.func.getError(err, 'PR0123');
        throw new ApolloError(errResult.message, errResult.code)
    }
}