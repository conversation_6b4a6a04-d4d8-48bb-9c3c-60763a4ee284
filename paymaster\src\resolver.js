// require resolver files
const listSalaryTemplateDetails = require('./resolvers/listSalaryTemplateDetails');
const deleteSalaryTemplate = require('./resolvers/deleteSalaryTemplate');
const addUpdateSalaryDetails = require('./resolvers/addUpdateSalaryDetails');
const listSalaryComponents = require('./resolvers/listSalaryComponents');
const listAllowanceType = require('./resolvers/listAllowanceType');
const updateSalaryConfiguration = require('./resolvers/updateSalaryConfiguration');
//need to handle with existing list modules with some changes
const getRetiralComponents = require('./resolvers/getRetiralComponents');
const getSalaryDetails = require('./resolvers/getSalaryDetails');
const updateTemplateStatus = require('./resolvers/updateTemplateStatus');
const retrieveSalaryConfiguration = require('./resolvers/retrieveSalaryConfiguration');
const getEffectiveDate = require('./resolvers/getEffectiveDate');
const deleteSalaryRecord = require('./resolvers/deleteSalaryRecord');
const addUpdateAllowanceType = require('./resolvers/addUpdateAllowanceType');
const grossConfiguration = require('./resolvers/grossConfiguration');

// Adhoc Allowance Types Resolvers
const listAdhocAllowanceTypes = require('./resolvers/listAdhocAllowanceTypes');
const addUpdateAdhocAllowanceType = require('./resolvers/addUpdateAdhocAllowanceType');

// Formula System Resolvers
const listFormulaComponents = require('./resolvers/listFormulaComponents');
const listFormulaFunctions = require('./resolvers/listFormulaFunctions');
const listFormulaOperators = require('./resolvers/listFormulaOperators');
const validateSalaryFormula = require('./resolvers/validateSalaryFormula');
const addFormulaComponent = require('./resolvers/addFormulaComponent');

// One Time Earnings Resolvers
const listOneTimeEarnings = require('./resolvers/listOneTimeEarnings');
const listOneTimeEarningsByMonth = require('./resolvers/listOneTimeEarningsByMonth');
const calculateOneTimeEarningAmount = require('./resolvers/calculateOneTimeEarningAmount');
const addUpdateOneTimeEarning = require('./resolvers/addUpdateOneTimeEarning');
const getDefaultPayoutMonth = require('./resolvers/getDefaultPayoutMonth');

// Candidate One Time Earnings Resolvers
const listCandidateOneTimeEarnings = require('./resolvers/listCandidateOneTimeEarnings');
const addUpdateCandidateOneTimeEarning = require('./resolvers/addUpdateCandidateOneTimeEarning');

// Candidate Salary Resolvers
const addUpdateCandidateSalaryDetails = require('./resolvers/candidateSalary/addUpdateCandidateSalaryDetails');
const listCandidateSalaryDetails = require('./resolvers/candidateSalary/listCandidateSalaryDetails');
const deleteCandidateSalaryDetails = require('./resolvers/candidateSalary/deleteCandidateSalaryDetails');

// Salary Revision Bulk Operations (Step Function)
const triggerCancelSalaryRevisions = require('./stepFunction/triggerCancelSalaryRevisions');

// Payroll Reconciliation Resolver
const getPayrollReconciliation = require('./resolvers/getPayrollReconciliation');

// Define resolver
const resolvers = {
    Query: Object.assign({},
        listSalaryTemplateDetails.resolvers.Query,
        getRetiralComponents.resolvers.Query,
        listSalaryComponents.resolvers.Query,
        listAllowanceType.resolvers.Query,
        getSalaryDetails.resolvers.Query,
        retrieveSalaryConfiguration.resolvers.Query,
        listCandidateSalaryDetails.resolvers.Query,
        listFormulaComponents.resolvers.Query,
        listFormulaFunctions.resolvers.Query,
        listFormulaOperators.resolvers.Query,
        validateSalaryFormula.resolvers.Query,
        grossConfiguration.resolvers.Query,
        listAdhocAllowanceTypes.resolvers.Query,
        listOneTimeEarnings.resolvers.Query,
        listOneTimeEarningsByMonth.resolvers.Query,
        calculateOneTimeEarningAmount.resolvers.Query,
        getDefaultPayoutMonth.resolvers.Query,
        listCandidateOneTimeEarnings.resolvers.Query,
        getPayrollReconciliation.resolvers.Query,
        { getEffectiveDate: getEffectiveDate.getEffectiveDate }
    ),
    Mutation: Object.assign({},
        deleteSalaryTemplate.resolvers.Mutation,
        updateTemplateStatus.resolvers.Mutation,
        updateSalaryConfiguration.resolvers.Mutation,
        addUpdateSalaryDetails.resolvers.Mutation,
        deleteSalaryRecord.resolvers.Mutation,
        addUpdateAllowanceType.resolvers.Mutation,
        grossConfiguration.resolvers.Mutation,
        addUpdateCandidateSalaryDetails.resolvers.Mutation,
        deleteCandidateSalaryDetails.resolvers.Mutation,
        addFormulaComponent.resolvers.Mutation,
        addUpdateAdhocAllowanceType.resolvers.Mutation,
        addUpdateOneTimeEarning.resolvers.Mutation,
        addUpdateCandidateOneTimeEarning.resolvers.Mutation,
        { triggerCancelSalaryRevisions: triggerCancelSalaryRevisions.triggerCancelSalaryRevisions }
    )
}
exports.resolvers = resolvers;