
//Require common library to access common function
const commonLib = require("@cksiva09/hrapp-corelib").CommonLib;
const knex = require("knex");
const { ApolloError } = require("apollo-server-lambda");
const { ehrTables } = require("../../common/tablealias");
let moment = require('moment-timezone');

module.exports.getPND1MonthlyReport = async (parent, args, context, info) => {

    let organizationDbConnection, errorMessage ='';
    console.log("Inside getPND1MonthlyReport() function");
    try {

        organizationDbConnection = knex(context.connection.OrganizationDb);

        const [tdsPaymentDetails, taxConfiguration, salaryPayslip, location] = await Promise.all([

            organizationDbConnection('tds_payment as TP').select('TPT.Payment_Date')
            .innerJoin('tds_payment_tracker as TPT', 'TPT.Payment_Id', 'TP.Payment_Id')
            .where('TP.Salary_Month', args.payRollMonth).first(),

            organizationDbConnection('tax_configuration as TC')
            .select('TC.TAN', 'TC.PAN',  
                organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name,EPI.Emp_Last_Name) as EmployeeName"),
                'D.Designation_Name as Position',
            )
            .leftJoin('emp_personal_info as EPI', 'EPI.Employee_Id', 'TC.Form16_Signatory')
            .leftJoin('emp_job as EJ', 'EJ.Employee_Id', 'EPI.Employee_Id')
            .leftJoin('designation as D', 'D.Designation_Id', 'EJ.Designation_Id')
            .modify(queryBuilder => {
                if(args.serviceProviderId){
                    queryBuilder.where('TC.Service_Provider_Id', args.serviceProviderId)
                }
            }).first(),

            organizationDbConnection('salary_payslip as SP')
            .select('MFS.Annual_Gross_Salary as paidAmount', 'SD.Deduction_Amount as amountOfTaxWithHeld', 
                'EPI.Aadhaar_Card_Number as empPersonalIdentificationNo', 'EPI.PAN as empTaxIdentificationNo', 'EPI.Salutation as surname',
                organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name,EPI.Emp_Last_Name) as employeeName"),
                organizationDbConnection.raw("\'1\' as conditions")
            )
            .innerJoin('emp_job as EJ', 'EJ.Employee_Id', 'SP.Employee_Id')
            .innerJoin('emp_personal_info as EPI', 'EPI.Employee_Id', 'EJ.Employee_Id')
            .innerJoin('monthly_form16_snapshot as MFS', 'MFS.Payslip_Id', 'SP.Payslip_Id')
            .leftJoin('salary_deduction as SD', function () {
                this.on('SD.Payslip_Id', 'SP.Payslip_Id')
                this.on('SD.Deduction_Name','=', organizationDbConnection.raw('?', ['Tax']))
            }).where('SP.Salary_Month', args.payRollMonth),

            organizationDbConnection(ehrTables.location).select('Branch_No as branchNo')
            .where('Location_Type', 'MainBranch').first()

        ]);

       
        if (!salaryPayslip || !salaryPayslip.length) {
            errorMessage = "Payslip details for this month are missing. Please generate them before proceeding.";
            throw 'PFF0018';
        }
        if (!tdsPaymentDetails) {
            errorMessage = "TDS payment details are unavailable. Please generate them before proceeding.";
            throw 'PFF0018';
        }
       
        if (!taxConfiguration) {
            errorMessage = "Tax details for this month are missing. Please generate them before proceeding.";
            throw 'PFF0018';
        }

        let totalPaidAmount=0, totalAmountOfTaxWithHeld=0;
        if(salaryPayslip && salaryPayslip.length){
            totalPaidAmount = salaryPayslip.reduce((sum, payment) => sum + payment.paidAmount, 0);
            totalAmountOfTaxWithHeld = salaryPayslip.reduce((sum, payment) => sum + payment.amountOfTaxWithHeld, 0);
        }
        
        let response = {
            personalIdentificationNo:  taxConfiguration?.TAN || '',
            taxpayerIdentificationNo: taxConfiguration?.PAN || '',
            typeofIncome1: 'Yes',
            typeofIncome2: 'No',
            typeofIncome3: 'No',
            typeofIncome4: 'No',
            typeofIncome5: 'No',
            branchNo: location?.branchNo || '',
            paymentDate: tdsPaymentDetails?.Payment_Date || null,
            employeeDetails: salaryPayslip && salaryPayslip.length ? salaryPayslip : [],
            position: taxConfiguration?.Position || '',
            payerResponsibleName: taxConfiguration?.EmployeeName || '',
            fillingDate: moment().utc().format('YYYY-MM-DD'),
            totalPaidAmount: totalPaidAmount,
            totalAmountOfTaxWithHeld: totalAmountOfTaxWithHeld
        }

        return {
            errorCode: "",
            message: "PND1 Monthly Report has been retrieved successfully.",
            data: response,
        };

    } catch (error) {
        console.error("Error in getPND1MonthlyReport() function main catch block", error);
        if(error == "PFF0018"){
            throw new ApolloError(errorMessage, error);
          }
        let errResult = commonLib.func.getError(error, "PFF0017");
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }

}
