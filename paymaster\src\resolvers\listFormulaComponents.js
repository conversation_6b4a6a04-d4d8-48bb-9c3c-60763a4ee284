// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError } = require('apollo-server-lambda');
const knex = require('knex');

// resolver definition
const resolvers = {
    Query: {
        // function to list formula components for formula building
        listFormulaComponents: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Validate formId parameter
                if (!args.formId) {
                    throw 'IVE0001';
                }

                // Check access rights
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );

                if (Object.keys(checkRights).length === 0) {
                    throw '_DB0100';
                }

                // get salary components for formula building
                const components = await getFormulaComponents(organizationDbConnection, args.employeeId, args.candidateId, args.componentTypeFilter);

                return {
                    errorCode: '',
                    message: 'Formula components retrieved successfully.',
                    success: true,
                    data: components
                };

            } catch (mainCatchError) {
                console.log('Error in listFormulaComponents function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0030');
                throw new ApolloError(errResult.message, errResult.code);
            } finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

/**
 * Get salary components for formula building
 * @param {Object} organizationDbConnection - Database connection object
 * @param {Number} employeeId - Optional employee ID to filter components
 * @param {Number} candidateId - Optional candidate ID to filter components
 * @param {String} componentTypeFilter - Optional component type filter (e.g., 'LEAVE')
 * @returns {Promise<Object>} - Promise resolving to components grouped by componentType
 */
async function getFormulaComponents(organizationDbConnection, employeeId = null, candidateId = null, componentTypeFilter = null) {
    try {
        // Determine which table to use based on employeeId or candidateId
        const isCandidate = candidateId ? true : false;
        const personId = candidateId || employeeId;
        const allowanceTable = isCandidate ? 'candidate_salary_allowance' : 'employee_salary_allowance';
        const grossTable = isCandidate ? 'candidate_gross_components' : 'salary_gross_components';
        const personIdColumn = isCandidate ? 'Candidate_Id' : 'Employee_Id';

        // Get salary components with optional employee/candidate filtering
        const salaryComponents = await organizationDbConnection('salary_components as SC')
            .leftJoin('allowance_type as AT', 'AT.Salary_Component_Id', 'SC.Component_Id')
            .leftJoin(`${allowanceTable} as SA`, 'AT.Allowance_Type_Id', 'SA.Allowance_Type_Id')
            .leftJoin('gross_configuration as GC', 'GC.Salary_Component_Id', 'SC.Component_Id')
            .leftJoin(`${grossTable} as SG`, 'SG.Gross_Id', 'GC.Gross_Id')
            .distinct(
                'SC.Component_Id',
                'SC.Component_Code',
                'SC.Component_Name',
                'SC.Component_Type',
                'SC.Group_Details',
                'SC.Notes',
                'SC.Components_Visible_For'
            )
            .modify((queryBuilder) => {
                // If componentTypeFilter is provided, filter by specific component type only
                if (componentTypeFilter) {
                    queryBuilder.where('SC.Component_Type', '=', componentTypeFilter.toUpperCase());
                } else if (personId) {
                    // When personId is provided, exclude BONUS, RETIRAL
                    queryBuilder.whereNotIn('SC.Component_Type', ['BONUS', 'RETIRAL']);

                    // Filter by employee/candidate
                    queryBuilder.where(function() {
                        // For EARNING type: must exist for this employee/candidate
                        this.where(function() {
                            this.where('SC.Component_Type', '=', 'EARNING')
                                .where(`SA.${personIdColumn}`, '=', personId);
                        })
                        .orWhere(function() {
                            this.where('SC.Component_Type', '=', 'GROSS')
                                .where(`SG.${personIdColumn}`, '=', personId);
                        })
                        .orWhere(function() {
                            // CTC is a universal component available for all employees/candidates
                            this.where('SC.Component_Type', '=', 'CTC');
                        });
                    });
                }
                // When personId is not provided, include all component types (including BONUS, RETIRAL, CTC)
            })
            .orderBy([
                { column: 'SC.Component_Type', order: 'asc' },
                { column: 'SC.Component_Name', order: 'asc' }
            ]);

        // Group components by componentType
        const groupedComponents = {};

        salaryComponents.forEach(component => {
            const componentType = component.Component_Type.toLowerCase();

            if (!groupedComponents[componentType]) {
                groupedComponents[componentType] = [];
            }

            groupedComponents[componentType].push({
                componentId: component.Component_Id,
                componentCode: component.Component_Code,
                componentName: component.Component_Name,
                componentType: component.Component_Type,
                description: `${component.Component_Type} - ${component.Component_Name}`,
                groupDetails: component.Group_Details,
                notes: component.Notes,
                componentsVisibleFor: component.Components_Visible_For
            });
        });

        // If componentTypeFilter is provided, return only the filtered results
        if (componentTypeFilter) {
            const filterKey = componentTypeFilter.toLowerCase();
            const filteredResult = {};
            filteredResult[filterKey] = groupedComponents[filterKey] || [];
            return filteredResult;
        }

        return groupedComponents;

    } catch (error) {
        console.error('Error in getFormulaComponents:', error);
        throw error;
    }
}

exports.resolvers = resolvers;
