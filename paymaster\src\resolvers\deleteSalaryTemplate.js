// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// Organization database connection
const knex = require('knex');
// require common constant files
const constants = require('../common/appconstants');
// require table alias
const {ehrTables} = require('../common/tablealias');

// variable declarations
let errResult = {};
let organizationDbConnection = '';
let checkRights=false;

// resolver definition
const resolvers = {
    Mutation: {
        // function to delete salary template
        deleteSalaryTemplate: async (parent, args, context, info) => {
            try{
                console.log('Inside deleteSalaryTemplate function');
                // variable declarations
                let loggedInEmpId=context.logInEmpId;
                let templateId=args.templateId;
                let systemLogParams={};

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Get delete access right based on employeeid
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loggedInEmpId,constants.formName.salaryTemplate,constants.roles.roleDelete);
                if (checkRights === true) {
                    return(
                        organizationDbConnection
                        .transaction(function(trx){
                            return(
                                // check whether the template id mapped to any employee or not
                                organizationDbConnection(ehrTables.employeeSalaryDetails)
                                .where('Template_Id',templateId)
                                .transacting(trx)
                                .then((getDetails) => {
                                    // check whether data exist or not
                                    if(getDetails.length>0)
                                    {
                                        // if template is mapped to to employee we should not allow template to be deleted
                                        throw 'PST0101';
                                    }
                                    else{
                                        return(
                                            // delete the template in salary_template table
                                            organizationDbConnection(ehrTables.salaryTemplate)
                                            .del()
                                            .where('Template_Id',templateId)
                                            .transacting(trx)
                                            .then((deleteTemplate) => {
                                                if(deleteTemplate)
                                                {
                                                    return(
                                                        // delete the template in template_allowance_components table
                                                        organizationDbConnection(ehrTables.templateAllowanceComponents)
                                                        .del()
                                                        .where('Template_Id',templateId)
                                                        .transacting(trx)
                                                        .then((deleteAllowances) => {
                                                            // delete the template in template_retrial_components table
                                                            return(
                                                                organizationDbConnection(ehrTables.templateRetiralComponents)
                                                                .del()
                                                                .where('Template_Id',templateId)
                                                                .transacting(trx)
                                                                .then(async(deleteRetrials) => {
                                                                    // include system log
                                                                    systemLogParams = {
                                                                        action: constants.systemLogs.roleDelete,
                                                                        userIp: context.userIp,
                                                                        employeeId: loggedInEmpId,
                                                                        formName: constants.formName.salaryTemplate,
                                                                        trackingColumn: '',
                                                                        organizationDbConnection: organizationDbConnection,
                                                                        uniqueId: templateId
                                                                    }
                                                                    // call function createSystemLogActivities() to update system log activities
                                                                    await commonLib.func.createSystemLogActivities(systemLogParams);
                                                                    // return success response to UI
                                                                    return { errorCode: '', message: 'Salary template deleted successfully' };                                                       
                                                                })
                                                            );
                                                        })
                                                    );
                                                }
                                                else{
                                                    // return template doesnot exists
                                                    throw 'PST0003';
                                                }
                                            })
                                        );
                                    }
                                })
                            );
                        })
                        .then(function (result) {
                            return result;
                        })
                        //catch db-connectivity errors
                        .catch(function (catchError) {
                            console.log('Error in deleteSalaryTemplate function .catch() block', catchError);
                            errResult = commonLib.func.getError(catchError, 'PST0002');
                            throw new ApolloError(errResult.message,errResult.code)
                        })
                        /**close db connection */
                        .finally(() => {
                            organizationDbConnection.destroy();
                        })                        
                    );  
                }
                else if (checkRights === false) {
                    throw '_DB0103';
                } else {
                    // throw error
                    throw (checkRights);
                }
            } catch (deleteSalaryTemplateMainCatch){
                console.log('Error in deleteSalaryTemplate function main catch block',deleteSalaryTemplateMainCatch);
                // destroy database connection
                (organizationDbConnection)?organizationDbConnection.destroy():'';
                errResult = commonLib.func.getError(deleteSalaryTemplateMainCatch, 'PST0002');
                throw new ApolloError(errResult.message,errResult.code)
            }
        }
    }
};

exports.resolvers = resolvers;
