// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');

// resolver definition
const resolvers = {
    Query: {
        // function to list candidate one time earnings
        listCandidateOneTimeEarnings: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside listCandidateOneTimeEarnings function');
                const loggedInEmpId = context.logInEmpId;
                const { formId, candidateId, approvalStatus } = args;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // check access right
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    formId
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100';
                }

                // get candidate one time earnings data
                const candidateOneTimeEarningsData = await getCandidateOneTimeEarnings(
                    organizationDbConnection,
                    candidateId,
                    approvalStatus
                );

                // return response
                return {
                    errorCode: '',
                    message: 'Candidate one time earnings listed successfully.',
                    success: true,
                    candidateOneTimeEarnings: JSON.stringify(candidateOneTimeEarningsData)
                };
            }
            catch (mainCatchError) {
                console.log('Error in listCandidateOneTimeEarnings function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0046');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        },

        // function to get single candidate one time earning
        getCandidateOneTimeEarning: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside getCandidateOneTimeEarning function');
                const loggedInEmpId = context.logInEmpId;
                const { candidateOneTimeEarningId, formId } = args;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // check access right
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    formId
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100';
                }

                // get single candidate one time earning
                const candidateOneTimeEarning = await getSingleCandidateOneTimeEarning(
                    organizationDbConnection,
                    candidateOneTimeEarningId
                );

                if (!candidateOneTimeEarning) {
                    throw 'IVE0927'; // Candidate one time earning not found
                }

                // return response
                return {
                    errorCode: '',
                    message: 'Candidate one time earning retrieved successfully.',
                    success: true,
                    candidateOneTimeEarning: JSON.stringify(candidateOneTimeEarning)
                };
            }
            catch (mainCatchError) {
                console.log('Error in getCandidateOneTimeEarning function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0046');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

/**
 * Get candidate one time earnings with filters
 */
async function getCandidateOneTimeEarnings(organizationDbConnection, candidateId, approvalStatus) {
    try {
        let query = organizationDbConnection(`${ehrTables.candidateOneTimeEarnings} as COTE`)
            .select(
                // Main table fields
                'COTE.Candidate_One_Time_Earning_Id',
                'COTE.Candidate_Id',
                'COTE.One_Time_Earning_Type_Id',
                'COTE.Payout_Month',
                'COTE.Payout_Period',
                'COTE.Clawback_Period',
                'COTE.Amount',
                'COTE.Custom_Formula',
                'COTE.Commitment_Period_Months',
                'COTE.Commitment_Start_Month',
                'COTE.Commitment_End_Month',
                'COTE.Clawback_Amount',
                'COTE.Clawback_Formula',
                'COTE.Approval_Status',
                'COTE.Process_Instance_Id',
                'COTE.Added_On',
                'COTE.Added_By',
                'COTE.Updated_On',
                'COTE.Updated_By',
                'COTE.Approved_On',
                'COTE.Approved_By',

                // Candidate name
                organizationDbConnection.raw("CONCAT_WS(' ', CPI.Emp_First_Name, CPI.Emp_Middle_Name, CPI.Emp_Last_Name) as CandidateName"),

                // Type config fields (retrieved from onetime_earning_types)
                'AAT.Title',
                'AAT.Description',
                'AAT.Calculation_Type',
                'AAT.Override_Custom_Formula',
                'AAT.Commitment_Period',
                'AAT.Clawback_Type',
                'AAT.Default_Payout_Month_From',
                'AAT.Default_Payout_Duration_Months',
                'AAT.Auto_Approval',
                'AAT.Tax_Inclusion',
                
                // Added/Updated/Approved by names
                organizationDbConnection.raw("CONCAT_WS(' ', EPI1.Emp_First_Name, EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as AddedByName"),
                organizationDbConnection.raw("CONCAT_WS(' ', EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as UpdatedByName"),
                organizationDbConnection.raw("CONCAT_WS(' ', EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as ApprovedByName")
            )
            .join(ehrTables.onetimeEarningTypes + ' as AAT', 'AAT.One_Time_Earning_Type_Id', 'COTE.One_Time_Earning_Type_Id')
            .leftJoin('candidate_personal_info as CPI', 'CPI.Candidate_Id', 'COTE.Candidate_Id')
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI1', 'EPI1.Employee_Id', 'COTE.Added_By')
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI2', 'EPI2.Employee_Id', 'COTE.Updated_By')
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI3', 'EPI3.Employee_Id', 'COTE.Approved_By');

        // Apply filters
        if (candidateId) {
            query.where('COTE.Candidate_Id', candidateId);
        }

        if (approvalStatus) {
            query.where('COTE.Approval_Status', approvalStatus);
        }

        // Order by most recent first
        query.orderBy('COTE.Added_On', 'desc');

        const results = await query;

        return results.map(row => ({
            candidateOneTimeEarningId: row.Candidate_One_Time_Earning_Id,
            candidateId: row.Candidate_Id,
            candidateName: row.CandidateName,
            oneTimeEarningTypeId: row.One_Time_Earning_Type_Id,
            adhocAllowanceTypeTitle: row.Title,
            adhocAllowanceTypeDescription: row.Description,
            payoutMonth: row.Payout_Month,
            payoutPeriod: row.Payout_Period,
            clawbackPeriod: row.Clawback_Period,
            amount: row.Amount,
            customFormula: row.Custom_Formula,
            commitmentPeriodMonths: row.Commitment_Period_Months,
            clawbackAmount: row.Clawback_Amount,
            clawbackFormula: row.Clawback_Formula,
            approvalStatus: row.Approval_Status,
            processInstanceId: row.Process_Instance_Id,
            addedOn: row.Added_On,
            addedBy: row.Added_By,
            addedByName: row.AddedByName,
            updatedOn: row.Updated_On,
            updatedBy: row.Updated_By,
            updatedByName: row.UpdatedByName,
            approvedOn: row.Approved_On,
            approvedBy: row.Approved_By,
            approvedByName: row.ApprovedByName,
            // Type configuration fields
            calculationType: row.Calculation_Type,
            overrideCustomFormula: row.Override_Custom_Formula,
            commitmentPeriod: row.Commitment_Period,
            clawbackType: row.Clawback_Type,
            defaultPayoutMonthFrom: row.Default_Payout_Month_From,
            defaultPayoutDurationMonths: row.Default_Payout_Duration_Months,
            autoApproval: row.Auto_Approval,
            taxInclusion: row.Tax_Inclusion
        }));
    } catch (error) {
        console.log('Error in getCandidateOneTimeEarnings', error);
        throw error;
    }
}

/**
 * Get single candidate one time earning by ID
 */
async function getSingleCandidateOneTimeEarning(organizationDbConnection, candidateOneTimeEarningId) {
    try {
        const query = organizationDbConnection(`${ehrTables.candidateOneTimeEarnings} as COTE`)
            .select(
                // Main table fields
                'COTE.Candidate_One_Time_Earning_Id',
                'COTE.Candidate_Id',
                'COTE.One_Time_Earning_Type_Id',
                'COTE.Payout_Month',
                'COTE.Payout_Period',
                'COTE.Clawback_Period',
                'COTE.Amount',
                'COTE.Custom_Formula',
                'COTE.Commitment_Period_Months',
                'COTE.Commitment_Start_Month',
                'COTE.Commitment_End_Month',
                'COTE.Clawback_Amount',
                'COTE.Clawback_Formula',
                'COTE.Approval_Status',
                'COTE.Process_Instance_Id',
                'COTE.Added_On',
                'COTE.Added_By',
                'COTE.Updated_On',
                'COTE.Updated_By',
                'COTE.Approved_On',
                'COTE.Approved_By',

                // Candidate name
                organizationDbConnection.raw("CONCAT_WS(' ', CPI.Emp_First_Name, CPI.Emp_Middle_Name, CPI.Emp_Last_Name) as CandidateName"),

                // Type config fields
                'AAT.Title',
                'AAT.Description',
                'AAT.Calculation_Type',
                'AAT.Override_Custom_Formula',
                'AAT.Commitment_Period',
                'AAT.Clawback_Type',
                'AAT.Default_Payout_Month_From',
                'AAT.Default_Payout_Duration_Months',
                'AAT.Auto_Approval',
                'AAT.Tax_Inclusion',
                
                // Added/Updated/Approved by names
                organizationDbConnection.raw("CONCAT_WS(' ', EPI1.Emp_First_Name, EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as AddedByName"),
                organizationDbConnection.raw("CONCAT_WS(' ', EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as UpdatedByName"),
                organizationDbConnection.raw("CONCAT_WS(' ', EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as ApprovedByName")
            )
            .join(ehrTables.onetimeEarningTypes + ' as AAT', 'AAT.One_Time_Earning_Type_Id', 'COTE.One_Time_Earning_Type_Id')
            .leftJoin('candidate_personal_info as CPI', 'CPI.Candidate_Id', 'COTE.Candidate_Id')
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI1', 'EPI1.Employee_Id', 'COTE.Added_By')
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI2', 'EPI2.Employee_Id', 'COTE.Updated_By')
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI3', 'EPI3.Employee_Id', 'COTE.Approved_By')
            .where('COTE.Candidate_One_Time_Earning_Id', candidateOneTimeEarningId)
            .first();

        const row = await query;

        if (!row) {
            return null;
        }

        return {
            candidateOneTimeEarningId: row.Candidate_One_Time_Earning_Id,
            candidateId: row.Candidate_Id,
            candidateName: row.CandidateName,
            oneTimeEarningTypeId: row.One_Time_Earning_Type_Id,
            adhocAllowanceTypeTitle: row.Title,
            adhocAllowanceTypeDescription: row.Description,
            payoutMonth: row.Payout_Month,
            payoutPeriod: row.Payout_Period,
            clawbackPeriod: row.Clawback_Period,
            amount: row.Amount,
            customFormula: row.Custom_Formula,
            commitmentPeriodMonths: row.Commitment_Period_Months,
            clawbackAmount: row.Clawback_Amount,
            clawbackFormula: row.Clawback_Formula,
            approvalStatus: row.Approval_Status,
            processInstanceId: row.Process_Instance_Id,
            addedOn: row.Added_On,
            addedBy: row.Added_By,
            addedByName: row.AddedByName,
            updatedOn: row.Updated_On,
            updatedBy: row.Updated_By,
            updatedByName: row.UpdatedByName,
            approvedOn: row.Approved_On,
            approvedBy: row.Approved_By,
            approvedByName: row.ApprovedByName,
            // Type configuration fields
            calculationType: row.Calculation_Type,
            overrideCustomFormula: row.Override_Custom_Formula,
            commitmentPeriod: row.Commitment_Period,
            clawbackType: row.Clawback_Type,
            defaultPayoutMonthFrom: row.Default_Payout_Month_From,
            defaultPayoutDurationMonths: row.Default_Payout_Duration_Months,
            autoApproval: row.Auto_Approval,
            taxInclusion: row.Tax_Inclusion
        };
    } catch (error) {
        console.log('Error in getSingleCandidateOneTimeEarning', error);
        throw error;
    }
}

module.exports = { resolvers };

