//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const { ehrTables } = require('../../common/tablealias');

//members contribution remittance form (MCRF) report
module.exports.getMemberContributionReport = async (parent, args, context, info) => {

    let organizationDbConnection;
    try{
        console.log("Inside getMemberContributionReport() function ")
        organizationDbConnection= knex(context.connection.OrganizationDb);

        const [employeeDetails, companyDetails, companyLocation] = await Promise.all([ 
            
        
            organizationDbConnection(ehrTables.empEtfPayment + ' as pay')
            .select('epi.Emp_First_Name as firstName', 'epi.Emp_Middle_Name as middleName', 'epi.Emp_Last_Name as lastName', 'epi.PRAN_No as rtnNumber',
                'pay.Org_ShareAmount as employerShare', 'pay.Emp_ShareAmount as personalShare',
                organizationDbConnection.raw('CASE WHEN pay.Org_ShareAmount IS NOT NULL THEN SUM(pay.Org_ShareAmount) ELSE 0 END as employerShareTotal'),
                organizationDbConnection.raw('CASE WHEN pay.Emp_ShareAmount IS NOT NULL THEN SUM(pay.Emp_ShareAmount) ELSE 0 END as personalShareTotal')
            ) 
            .innerJoin(ehrTables.empPersonalInfo+' as epi', function() {
                this.on('pay.Employee_Id', '=', 'epi.Employee_Id')
                    .andOn('pay.Salary_Month', '=',  organizationDbConnection.raw('?', [args.payRollMonth]) );
            }),
               
                organizationDbConnection(ehrTables.orgDetails).select('Org_Name').limit(1),

                organizationDbConnection(ehrTables.location)
                .select('location.Phone','location.Street1', 'location.Street2', 'city.City_Name', 'state.State_Name', 'country.Country_Name', 'location.Pincode')
                .leftJoin(ehrTables.country, 'location.Country_Code','country.Country_Code')
                .leftJoin(ehrTables.state, 'location.State_Id','state.State_Id')
                .leftJoin(ehrTables.city, 'location.City_Id', 'city.City_Id')
                .where('location.Location_Type','MainBranch')
            ]);


            let employeeDetail = {
                maillingAddress: {
                    street1:  companyLocation[0]?.Street1,
                    street2: companyLocation[0]?.Street2,
                    cityName: companyLocation[0]?.City_Name,
                    stateName: companyLocation[0]?.State_Name,
                    countryName: companyLocation[0]?.Country_Name,
                    pincode: companyLocation[0]?.Pincode
                },
                mobileNo: companyLocation[0]?.Phone,
                employerName: companyDetails[0]?.Org_Name,
                employeeDetails : employeeDetails,
                employerShareTotal: employeeDetails[0]?.employerShareTotal,
                personalShareTotal: employeeDetails[0]?.personalShareTotal,
                dateReceived: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                errorCode: "", 
                message: "Member Contribution Report has been fetched successfully.",
            }

            organizationDbConnection ? organizationDbConnection.destroy() : null;

            return employeeDetail;

    } catch(err){
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.error('Error in getMemberContributionReport function main catch block.', err);
        let errResult = commonLib.func.getError(err, 'PFF0013');
        throw new ApolloError(errResult.message, errResult.code)
    }
}