// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const moment = require('moment');
const { ehrTables } = require('../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const commonFunctions = require('../common/salaryTemplateCommonFunctions');
const { Organizations } = require('aws-sdk');
const { formId } = require('../common/appconstants');
const { getFiscalStartDate, getFiscalMonthYear } = require('../common/commonfunctions');
const { getSalaryDateRange } = require('../common/salaryDateFunctions');

/**
 * Main resolver function to get salary template details
 * @param {Object} parent - Parent object
 * @param {Object} args - Arguments passed to the resolver
 * @param {Object} context - Context object containing user info
 * @param {Object} info - GraphQL info object
 * @returns {Object} - Response with salary template details
 */
const listSalaryTemplateDetails = async (parent, args, context, info) => {
  let organizationDbConnection;

  try {
    console.log('Inside listSalaryTemplateDetails function',context);
    let responseData = [];
    let loginEmployeeId = null;
    let orgCode = null;
    if(process.env.endPoint === 'external'){
      orgCode = context.Org_Code;
    }else{
      orgCode = context.orgCode;
      loginEmployeeId = context.logInEmpId;
    }
    const userFormId = args.formId || 206;
    const isViewMode = args.isViewMode || false;

    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Initialize checkRights variable
    let checkRights = {};

    // Skip access check for external endpoint
    if (process.env.endPoint !== 'external') {
      const accessFormId = args.accessFormId || userFormId;
      checkRights = await commonLib.func.checkEmployeeAccessRights(
        organizationDbConnection,
        loginEmployeeId,
        null,
        '',
        'UI',
        false,
        accessFormId
      );
      if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
        throw '_DB0100';
      }
    } else {
      // For external endpoint, set default checkRights to allow access
      checkRights = {
        Role_View: 1,
        Employee_Role: 'admin' // Default to admin role for external access
      };
    }

    // Initialize employee access control variables for forms other than 206
    let isAdmin = 0;
    let isManager = 0;
    let employeeIdsArray = [];

    // Get organization details for assessment year and manager access control
    const orgDetails = await organizationDbConnection(ehrTables.orgDetails)
      .select('Assessment_Year', 'Restrict_Financial_Access_For_Manager')
      .where('Org_Code', orgCode)
      .first();

    const restrictManagerFinanceAccess = orgDetails?.Restrict_Financial_Access_For_Manager === 1;

    if (userFormId !== 206) {
      // Apply the same employeeIdsArray logic as listSalaryPayslip.js
      if (checkRights.Employee_Role && checkRights.Employee_Role.toLowerCase() === 'admin') {
        isAdmin = 1;
      } else {
        // Check service provider admin
        const serviceProviderAdmin = await commonLib.func.checkEmployeeAccessRights(
          organizationDbConnection,
          loginEmployeeId,
          '',
          'Role_Update',
          '',
          false,
          formId.serviceProviderAdmin
        );

        if (serviceProviderAdmin) {
          const serviceProviderEmployeeIds = await commonLib.func.getServiceProviderEmpIdsForFieldForce(
            organizationDbConnection,
            loginEmployeeId,
            context.orgCode
          );
          employeeIdsArray = employeeIdsArray.concat(serviceProviderEmployeeIds);
        } else if (checkRights.Is_Manager === 1) {
          isManager = 1;
          const managerAccessDetails = await commonLib.func.getManagerHierarchy(
            organizationDbConnection,
            loginEmployeeId,
            0,
            null
          );
          // When Restrict_Financial_Access_For_Manager = 1, manager IS restricted (no financial access)
          // When Restrict_Financial_Access_For_Manager ≠ 1, manager has access to reportees
          if (!restrictManagerFinanceAccess) {
            employeeIdsArray = employeeIdsArray.concat(managerAccessDetails);
          } else {
            // Manager is restricted - use sentinel value to ensure fail-closed security
            employeeIdsArray = [-1];
          }
        } else {
          employeeIdsArray.push(loginEmployeeId);
        }
      }
    }

    const [currencySymbolDetails, roundOffDetails, fiscalMonthArrayData] = await Promise.all([
      organizationDbConnection(ehrTables.payrollGeneralSettings)
        .select('Payroll_Currency')
        .first(),
      getRoundOffSettings(organizationDbConnection),
      getFiscalMonthYear(organizationDbConnection, orgCode, orgDetails?.Assessment_Year)
    ]);

    const currencySymbol = currencySymbolDetails?.Payroll_Currency || '₹';

    const tableConfig = getTableConfig(userFormId,args);
    const mainTableData = await getMainTableData(organizationDbConnection, tableConfig, args, { isAdmin, isManager, employeeIdsArray, loginEmployeeId, restrictManagerFinanceAccess });

    // Get all record IDs upfront
    const recordIds = mainTableData.map(data => data[tableConfig.primaryKey]);

    // Get all allowance components in one query
    const [
      allowanceComponentsByRecord,
      retiralsComponentsByRecord,
      grossComponentsByRecord
    ] = await Promise.all([
      getAllowanceComponentsByRecord(organizationDbConnection, recordIds, tableConfig),
      getRetiralsComponentsByRecord(organizationDbConnection, recordIds, tableConfig),
      getGrossComponentsByRecord(organizationDbConnection, recordIds, tableConfig)
    ]);

    // Get revision payslip components for salary revision records only
    let revisionPayslipComponentsByRecord = {};
    let retiralsPayslipComponentsByRecord = {};
    
    if (tableConfig.mainTable === ehrTables.salaryRevisionDetails && args.isViewMode === true) {
      revisionPayslipComponentsByRecord = await getRevisionPayslipComponentsByRecord(organizationDbConnection, recordIds, tableConfig) || {};
      retiralsPayslipComponentsByRecord = await getRetiralsPayslipByRecord(organizationDbConnection, recordIds, tableConfig) || {};
    }
    
    // Build response data
    const organizationDetails =  await commonLib.func.getOrgDetails(orgCode, organizationDbConnection, 0);
    const fiscalStartDate = await getFiscalStartDate(orgCode, organizationDbConnection);

    responseData = await Promise.all(mainTableData.map(async (data) => {
      const recordId = data[tableConfig.primaryKey];
      let recordData = {
        ...formatMainTableData(data, tableConfig),
      };

      // Process maxPayslipMonth and get lastSalaryDay if available
      if (data.maxPayslipMonth) {
        try {
          // Handle the Salary_Month format which is typically "MM,YYYY"
          let month, year;
          if (typeof data.maxPayslipMonth === 'string') {
            // Parse the "MM,YYYY" format
            const parts = data.maxPayslipMonth.split(',');
            if (parts.length === 2) {
              month = parseInt(parts[0], 10);
              year = parseInt(parts[1], 10);

              // Validate month and year
              if (month >= 1 && month <= 12 && year > 1900 && year < 3000) {
                const lastSalaryDay = await commonLib.func.getSalaryDay(
                  orgCode,
                  organizationDbConnection,
                  month.toString(),
                  year,
                  null,
                  organizationDetails
                );
                recordData.lastSalaryDay = lastSalaryDay;
                recordData.maxPayslipMonth = data.maxPayslipMonth;

                // Compare fiscalStartDate with maxPayslipMonth to determine effective_month_to_consider
                if (fiscalStartDate) {
                  const fiscalMoment = moment(fiscalStartDate, 'YYYY-MM-DD');
                  const maxPayslipMoment = moment(`${year}-${month.toString().padStart(2, '0')}-01`, 'YYYY-MM-DD');

                  const earliestMoment = fiscalMoment.isBefore(maxPayslipMoment) ? fiscalMoment : maxPayslipMoment;
                  let earliesTwithDateOfJoin = data.Date_Of_Join ?
                    (earliestMoment.isBefore(moment(data.Date_Of_Join, 'YYYY-MM-DD')) ? moment(data.Date_Of_Join, 'YYYY-MM-DD') : earliestMoment) :
                    earliestMoment;
                

                  // Apply mid-joinee logic if Date_Of_Join exists
                  if (data.Date_Of_Join) {
                    try {
                      // Get organization details for getSalaryDateRange (scoped by orgCode)
                      const orgDetails = await getOrganizationDetails(organizationDbConnection, orgCode);

                      // Convert Date_Of_Join to moment and extract month/year
                      const dateOfJoinMoment = moment(data.Date_Of_Join, 'YYYY-MM-DD');
                      const joinMonth = parseInt(dateOfJoinMoment.format('M'));
                      const joinYear = parseInt(dateOfJoinMoment.format('YYYY'));

                      // Get salary date range for this month considering date of join
                      const salaryDateRange = await getSalaryDateRange(
                        joinMonth,
                        joinYear,
                        data.Date_Of_Join, // punchInDate is dateOfJoin
                        0, // formId
                        orgCode,
                        organizationDbConnection,
                        orgDetails
                      );

                      // Ensure salaryDateRange and Last_SalaryDate exist
                      if (salaryDateRange && salaryDateRange.Last_SalaryDate) {
                        const lastSalaryDate = moment(salaryDateRange.Last_SalaryDate);
                        const employeeJoinedMonth = lastSalaryDate.format('M,YYYY');
                        const currentMonthYear = earliesTwithDateOfJoin.format('M,YYYY');

                        // If (Existing Result Month == Employee Joined Month) → Existing Result Month + 1 Month
                        if (currentMonthYear === employeeJoinedMonth) {
                          // Add 1 month to the current month
                          const nextMonth = moment(`${joinMonth}-${joinYear}`, 'M-YYYY').add(1, 'month');
                          earliesTwithDateOfJoin = nextMonth;
                        }
                      }
                    } catch (error) {
                      console.error('Error in mid-joinee logic:', error);
                      // Continue with original logic if error occurs
                    }
                  }

                  recordData.effective_month_to_consider = earliesTwithDateOfJoin.format('M,YY');
                }
              } else {
                recordData.lastSalaryDay = null;
              }
            } else {
              recordData.lastSalaryDay = null;
            }
          } else {
            recordData.lastSalaryDay = null;
          }
        } catch (error) {
          recordData.lastSalaryDay = null;
        }
      } else {
        recordData.lastSalaryDay = null;

        // If no maxPayslipMonth but fiscalStartDate exists, use fiscalStartDate
        if (fiscalStartDate) {
          const fiscalMoment = moment(fiscalStartDate, 'YYYY-MM-DD');
          recordData.effective_month_to_consider = fiscalMoment.format('M,YY');
        }
      }

      if (isViewMode) {
        recordData.allowances = {
          allowanceArray: allowanceComponentsByRecord[recordId]?.Allowance || [],
          fixedAllowanceArray: allowanceComponentsByRecord[recordId]?.Fixed_Allowance || [],
          bonusArray: allowanceComponentsByRecord[recordId]?.Bonus || [],
          flexiBenefitPlanArray: allowanceComponentsByRecord[recordId]?.Flexible_Benefit_Plan || [],
          reimbursementArray: allowanceComponentsByRecord[recordId]?.Reimbursement || [],
          basicPayArray: allowanceComponentsByRecord[recordId]?.Basic_Pay || []
        };
        recordData.retirals = retiralsComponentsByRecord[recordId] || [];
        recordData.grossComponents = grossComponentsByRecord[recordId] || [];

        if (tableConfig.mainTable === ehrTables.salaryRevisionDetails && args.isViewMode === true) {
          recordData.revisionPayslipComponents = revisionPayslipComponentsByRecord?.[recordId]?.revisionPayslipComponents || [];
          recordData.retiralsPayslipComponents = retiralsPayslipComponentsByRecord?.[recordId]?.retiralsPayslipComponents || [];
        }
      }

      return recordData;
    }));

    // Get PF settings separately
    const pfSettings = await organizationDbConnection(ehrTables.providentFundSettings)
      .select('Admin_Charge', 'Admin_Charge_Max_Amount', 'EDLI_Charge', 'EDLI_Charge_Max_Amount')
      .first();

    // Get PF configuration separately
    const pfConfig = await organizationDbConnection(ehrTables.providentFund)
      .select('Admin_Charge_Part_Of_CTC', 'Edli_Charge_Part_Of_CTC')
      .first();

    return {
      errorCode: "",
      message: `${tableConfig.entityName} details retrieved successfully`,
      currencySymbol: currencySymbol,
      templateDetails: JSON.stringify(responseData),
      roundOffSettings: JSON.stringify(roundOffDetails),
      pfSettings: JSON.stringify({
        ...(pfSettings || {}),
        ...(pfConfig || {})
      }),
      fiscalMonthArray: JSON.stringify(fiscalMonthArrayData || [])
    };
  } catch (error) {
    console.log('Error in listSalaryTemplateDetails() function main catch block', error);

    // Handle error response
    const errResult = commonLib.func.getError(error, 'PFF0018');
    throw new ApolloError(errResult.message, errResult.code);
  } finally {
    // Clean up database connections
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
};

async function getRoundOffSettings(organizationDbConnection) {
    try {
        const roundOffSettings = await organizationDbConnection(ehrTables.payrollRoundOffSettings)
            .select('Round_Off_Settings_Id', 'Multiples_Of', 'Round_Off_For', 'Form_Id')
        return roundOffSettings || [];
    } catch (error) {
        console.error('Error in getRoundOffSettings:', error);
        throw error;
    }
}

/**
 * Get table configuration based on form ID
 * @param {number} formId - Form ID to determine table configuration
 * @returns {Object} - Configuration object with table details
 */
function getTableConfig(formId,args) {
  switch (parseInt(formId)) {
    case 206:
      return {
        entityName: 'Salary template',
        mainTable: ehrTables.salaryTemplate,
        allowanceTable: ehrTables.templateAllowanceComponents,
        retiralsTable: ehrTables.templateRetiralComponents,
        grossTable: ehrTables.templateGrossComponents,
        primaryKey: 'Template_Id',
        foreignKey: 'Template_Id',
        statusField: 'Template_Status',
        defaultOrderBy: 'Template_Status'
      };
    case 207:
    case 346:
      return {
        entityName: 'Salary details',
        mainTable: args.includeHistoricalRecords ? 'employee_salary_history' : ehrTables.employeeSalaryDetails,
        allowanceTable: args.includeHistoricalRecords ? 'employee_allowance_history' : ehrTables.employeeSalaryAllowance,
        retiralsTable: args.includeHistoricalRecords ? 'employee_retirals_history' : ehrTables.employeeSalaryRetirals,
        grossTable: args.includeHistoricalRecords ? ehrTables.salaryHistoryGrossComponents : ehrTables.salaryGrossComponents,
        primaryKey: args.includeHistoricalRecords ? 'Salary_History_Id' : 'Employee_Id',
        foreignKey: args.includeHistoricalRecords ? 'Salary_History_Id' : 'Employee_Id',
        statusField: null,
        defaultOrderBy: args.includeHistoricalRecords ? 'Salary_History_Id' : 'Employee_Id',
        isHistorical: args.includeHistoricalRecords || false
      };
    case 360: // Salary Revision
      return {
        entityName: 'Salary revision',
        mainTable: ehrTables.salaryRevisionDetails,
        allowanceTable: ehrTables.salaryRevisionAllowance,
        retiralsTable: ehrTables.salaryRevisionRetirals,
        grossTable: ehrTables.revisionGrossComponents,
        primaryKey: 'Revision_Id',
        foreignKey: 'Revision_Id',
        statusField: null,
        defaultOrderBy: 'Employee_Id'
      };
    default:
      throw 'PFF0019';
  }
}

/**
 * Get main table data with filtering and ordering
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} tableConfig - Table configuration
 * @param {Object} args - Query arguments
 * @param {Object} accessControl - Access control parameters (isAdmin, isManager, employeeIdsArray, loginEmployeeId)
 * @returns {Promise<Array>} - Array of records from main table
 */
async function getMainTableData(organizationDbConnection, tableConfig, args, accessControl = {}) {
  let query;
  const isViewMode = args.isViewMode || false;
  const isDropdown = args.isDropdown || false;

  // 1. Determine table alias upfront
  const isSalaryTable = [ehrTables.employeeSalaryDetails, ehrTables.salaryRevisionDetails, 'employee_salary_history'].includes(tableConfig.mainTable);
  const mainTableAlias = isSalaryTable ?
    (tableConfig.mainTable === ehrTables.employeeSalaryDetails ? 'ESD' :
     tableConfig.mainTable === 'employee_salary_history' ? 'ESH' : 'RSD') :
    tableConfig.mainTable;
  if (isSalaryTable) {
    query = organizationDbConnection(`${tableConfig.mainTable} as ${mainTableAlias}`)
      .select(
        `${mainTableAlias}.*`,
        'ST.Template_Name',
        'EJ.Emp_Status',
        'EJ.Date_Of_Join',
        organizationDbConnection.raw("CONCAT_WS(' ', EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as EmployeeName"),
        organizationDbConnection.raw("(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EPI3.Employee_Id END) as userDefinedEmpId"),
        organizationDbConnection.raw(`(
          SELECT ESL.Amount
          FROM ?? as ESL
          INNER JOIN ?? as AT ON ESL.Allowance_Type_Id = AT.Allowance_Type_Id
          LEFT JOIN salary_components as SC ON AT.Salary_Component_Id = SC.Component_Id
          WHERE ESL.?? = ??.??
          AND SC.Component_Code = ?
          limit 1
        ) as Basic_Pay`, [
          tableConfig.allowanceTable,
          ehrTables.allowanceType,
          tableConfig.foreignKey,
          mainTableAlias,
          tableConfig.primaryKey,
          'basic_salary_amount'
        ]),
        organizationDbConnection.raw(`(
          SELECT SP.Salary_Month
          FROM ?? as SP
          WHERE SP.Employee_Id = ??.Employee_Id
          ORDER BY STR_TO_DATE(SP.Salary_Month,'%m,%Y') DESC
          LIMIT 1
        ) as maxPayslipMonth`, [
          ehrTables.salaryPayslip,
          mainTableAlias
        ]),
        'ER.Resignation_Date'
      )

      .leftJoin(`${ehrTables.salaryTemplate} as ST`, `${mainTableAlias}.Template_Id`, 'ST.Template_Id')
      .leftJoin(`${ehrTables.empPersonalInfo} as EPI3`, `${mainTableAlias}.Employee_Id`, 'EPI3.Employee_Id')
      .leftJoin(`${ehrTables.empJob} as EJ`, 'EJ.Employee_Id', 'EPI3.Employee_Id')
      .leftJoin(`${ehrTables.empResignation} as ER`, function() {
        this.on(`${mainTableAlias}.Employee_Id`, '=', 'ER.Employee_Id')
            .andOn(organizationDbConnection.raw('(ER.Approval_Status = ? OR ER.Approval_Status = ?)', ['Approved', 'Applied']));
      })
      
    if (tableConfig.mainTable === ehrTables.employeeSalaryDetails && isViewMode) {
      query.select([
        'ESC.Eligible_For_Overtime',
        'ESC.Overtime_Allocation',
        'ESC.Overtime_Wage_Index',
        'ESC.Overtime_Slab',
        'ESC.Overtime_Wage',
        'ESC.Eligible_For_Pf',
        'ESC.Eligible_For_Pension',
        'ESC.Exempt_EDLI',
        'ESC.UAN',
        'ESC.Pf_PolicyNo',
        'ESC.Eligible_For_Vpf',
        'ESC.Vpf_Type',
        'ESC.Vpf_Employee_Share',
        'ESC.Vpf_Employee_Share_Amount',
        'ESC.Eligible_For_ESI',
        'ESC.ESI_Number',
        'ESC.ESI_Contribution_End_Date',
        'ESC.Eligible_For_Insurance',
        'ESC.Eligible_For_Nps',
        'ESC.Nps_Number',
        'ESC.Eligible_For_Gratuity',
        'ESC.Eligible_For_PT',
        'ESC.Eligible_For_Contractor_Tds',
        'ESC.Tax_Section_Id',
        'ESC.Eligible_For_Teacher_Provident_Fund',
        'ESC.TPF_Type',
        'ESC.TPF_Employee_Share_Amount',
        'ESC.TPF_Employee_Share',
        'ESC.TPF_Number',
        'ESC.Eligible_For_Contribution_Pension_Scheme',
        'ESC.CPS_Type',
        'ESC.CPS_Employee_Share_Amount',
        'ESC.CPS_Employer_Share_Amount',
        'ESC.CPS_Employee_Share',
        'ESC.CPS_Employer_Share',
        'ESC.CPS_Number',
        'ESC.Eligible_For_Special_Provident_Fund',
        'ESC.SPF_Employee_Share',
        'ESC.SPF_End_Month',
        'ESC.SPF_Number',
        'ESC.Salary_Calculation_Scheme'
      ])
      .leftJoin(`${ehrTables.employeeSalaryConfiguration} as ESC`, `${mainTableAlias}.Employee_Id`, 'ESC.Employee_Id');
    }

    // Connect with salary revision if formId = 207 and get count of applied revisions
    if(args.formId === 207){
      query = query.select(
        organizationDbConnection.raw(`(
          SELECT COUNT(SRD.Revision_Id)
          FROM salary_revision_details SRD
          WHERE SRD.Employee_Id = ??.Employee_Id
          AND SRD.Revision_Status = ?
        ) as Revision_Count`, [mainTableAlias, 'Applied'])
      );
    }
    // For historical records, don't add salary configuration
    if (tableConfig.mainTable === 'employee_salary_history' && isViewMode) {
      // Historical records don't need salary configuration join
    }
  } else {
    query = organizationDbConnection(tableConfig.mainTable).select('*');
  }


  // 3. Common joins for AddedBy/UpdatedBy (applies to ALL forms)
  query
    .leftJoin(`${ehrTables.empPersonalInfo} as EPI`, 'EPI.Employee_Id', `${mainTableAlias}.Added_By`)
    .leftJoin(`${ehrTables.empPersonalInfo} as EPI2`, 'EPI2.Employee_Id', `${mainTableAlias}.Updated_By`)
    .select([
      organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as AddedByName"),
      organizationDbConnection.raw("CONCAT_WS(' ', EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as UpdatedByName"),
    ]);
  // 4. Conditional filtering
  if (args.templateId) {
    query.where(`${mainTableAlias}.${tableConfig.primaryKey}`, args.templateId);
  }
  if((args.id && tableConfig.mainTable === ehrTables.salaryRevisionDetails) || (args.id && tableConfig.mainTable === ehrTables.employeeSalaryHistory)){
    query.where(`${mainTableAlias}.${tableConfig.primaryKey}`, args.id);
  }
  if (args.employeeId) {
    query.where(`${mainTableAlias}.Employee_Id`, args.employeeId);
  } else {
    if (tableConfig.mainTable === 'employee_salary_history') {
      // No additional filtering for historical records - show all history
    }

    if (tableConfig.mainTable === ehrTables.salaryTemplate && isDropdown) {
      query.where(`${mainTableAlias}.Template_Status`, 'Active');
    } else if (tableConfig.statusField) {
      query.where(`${mainTableAlias}.${tableConfig.statusField}`, 'Active');
    }
   if (args.formId !== 206 && args.id == null) {
      const { isAdmin, isManager, employeeIdsArray, loginEmployeeId, restrictManagerFinanceAccess } = accessControl;

      if (!isAdmin) {
        if (isManager) {
          if (restrictManagerFinanceAccess) {
            // Manager is restricted: sees only self
            query.where(`${mainTableAlias}.Employee_Id`, loginEmployeeId);
          } else {
            // Manager has access: sees team (exclude self)
            query.whereNotIn(`${mainTableAlias}.Employee_Id`, [loginEmployeeId]);
            // Fail-closed security: if employeeIdsArray is empty, use sentinel value
            if (employeeIdsArray.length === 0) {
              query.where(`${mainTableAlias}.Employee_Id`, -1);
            } else {
              query.whereIn(`${mainTableAlias}.Employee_Id`, employeeIdsArray);
            }
          }
        } else {
          // Regular employee: sees only self
          query.where(`${mainTableAlias}.Employee_Id`, loginEmployeeId);
        }
      }
      // For admins, no additional filtering (show all records)
    }
  }

  // 5. Ordering
  if (tableConfig.defaultOrderBy) {
    query.orderBy(`${mainTableAlias}.${tableConfig.defaultOrderBy}`);
  }

  const result = await query;

  console.log('Main table data:', result);
  return result.length > 0 ? result : [];
}

/**
 * Format main table data based on table type
 * @param {Object} data - Raw data from database
 * @param {Object} tableConfig - Table configuration
 * @returns {Object} - Formatted data object
 */
function formatMainTableData(data, tableConfig) {
  switch (tableConfig.mainTable) {
    case ehrTables.salaryTemplate:
      return {
        'Template_Id': data.Template_Id,
        'Template_Name': data.Template_Name,
        'External_Template_Id': data.External_Template_Id,
        'Annual_CTC': data.Annual_Ctc,
        'Annual_Gross_Salary': data.Annual_Gross_Salary,
        'Monthly_Gross_Salary': data.Monthly_Gross_Salary,
        'Template_Status': data.Template_Status,
        'Description': data.Description,
        'Added_On': data.Added_On,
        'Updated_On': data.Updated_On,
        'AddedByName': data.AddedByName,
        'UpdatedByName': data.UpdatedByName
      };
    case ehrTables.employeeSalaryDetails:
      // For salary form, include all fields from the query
      const formattedData = {
        'Employee_Id': data.Employee_Id,
        'Template_Id': data.Template_Id,
        'Template_Name': data.Template_Name,
        'Basic_Pay': data.Basic_Pay,
        'Effective_From': data.Effective_From,
        'Annual_CTC': data.Annual_Ctc,
        'Annual_Gross_Salary': data.Annual_Gross_Salary,
        'Monthly_Gross_Salary': data.Monthly_Gross_Salary,
        'Salary_Effective_Month': data.Salary_Effective_Month,
        'Employee_Name': data.EmployeeName,
        'Emp_Status' : data.Emp_Status,
        'User_Defined_EmpId': data.userDefinedEmpId,
        'Resignation_Date': data.Resignation_Date,
        'Added_On': data.Added_On,
        'Updated_On': data.Updated_On,
        'AddedByName': data.AddedByName,
        'UpdatedByName': data.UpdatedByName
      };

      // Add configuration fields if they exist (when isViewMode is true)
      if (data.Eligible_For_Overtime !== undefined) {
        Object.assign(formattedData, {
          'Eligible_For_Overtime': data.Eligible_For_Overtime,
          'Overtime_Allocation': data.Overtime_Allocation,
          'Overtime_Wage_Index': data.Overtime_Wage_Index,
          'Overtime_Slab': data.Overtime_Slab,
          'Overtime_Wage': data.Overtime_Wage,
          'Eligible_For_Pf': data.Eligible_For_Pf,
          'Eligible_For_Pension': data.Eligible_For_Pension,
          'Exempt_EDLI': data.Exempt_EDLI,
          'UAN': data.UAN,
          'Pf_PolicyNo': data.Pf_PolicyNo,
          'Eligible_For_ESI': data.Eligible_For_ESI,
          'ESI_Number': data.ESI_Number,
          'ESI_Contribution_End_Date': data.ESI_Contribution_End_Date,
          'Reason_Id': data.Reason_Id,
          'Eligible_For_Vpf': data.Eligible_For_Vpf,
          'Vpf_Type': data.Vpf_Type,
          'Vpf_Employee_Share': data.Vpf_Employee_Share,
          'Vpf_Employee_Share_Amount': data.Vpf_Employee_Share_Amount,
          'Eligible_For_Insurance': data.Eligible_For_Insurance,
          'Eligible_For_Nps': data.Eligible_For_Nps,
          'Nps_Number': data.Nps_Number,
          'Eligible_For_Gratuity': data.Eligible_For_Gratuity,
          'Eligible_For_PT': data.Eligible_For_PT,
          'Bond_Recovery_Applicable': data.Bond_Recovery_Applicable,
          'Minimum_Months_To_Be_Served': data.Minimum_Months_To_Be_Served,
          'Bond_Value': data.Bond_Value,
          'Eligible_For_Contractor_Tds': data.Eligible_For_Contractor_Tds,
          'Tax_Section_Id': data.Tax_Section_Id,
          'Eligible_For_Teacher_Provident_Fund': data.Eligible_For_Teacher_Provident_Fund,
          'TPF_Type': data.TPF_Type,
          'TPF_Employee_Share_Amount': data.TPF_Employee_Share_Amount,
          'TPF_Employee_Share': data.TPF_Employee_Share,
          'TPF_Number': data.TPF_Number,
          'Eligible_For_Contribution_Pension_Scheme': data.Eligible_For_Contribution_Pension_Scheme,
          'CPS_Type': data.CPS_Type,
          'CPS_Employee_Share_Amount': data.CPS_Employee_Share_Amount,
          'CPS_Employer_Share_Amount': data.CPS_Employer_Share_Amount,
          'CPS_Employee_Share': data.CPS_Employee_Share,
          'CPS_Employer_Share': data.CPS_Employer_Share,
          'CPS_Number': data.CPS_Number,
          'Eligible_For_Special_Provident_Fund': data.Eligible_For_Special_Provident_Fund,
          'SPF_Employee_Share': data.SPF_Employee_Share,
          'SPF_End_Month': data.SPF_End_Month,
          'SPF_Number': data.SPF_Number,
          'Salary_Calculation_Scheme': data.Salary_Calculation_Scheme,
          'PTKP_Id': data.PTKP_Id,
          'Tax_Object_Id': data.Tax_Object_Id,
          'Daily_Employee': data.Daily_Employee,
          'Get_Facilities': data.Get_Facilities,
          'SKB_Number': data.SKB_Number,
          'DTP_Number': data.DTP_Number

        });
      }

      // Add revision count if formId = 207 and revision count exists
      if (data.Revision_Count !== undefined) {
        Object.assign(formattedData, {
          'Revision_Count': data.Revision_Count
        });
      }

      return formattedData;
    case 'employee_salary_history':
      // For salary history, format similar to salary details but with history-specific fields
      return {
        'Salary_History_Id': data.Salary_History_Id,
        'Employee_Id': data.Employee_Id,
        'Template_Id': data.Template_Id,
        'Template_Name': data.Template_Name,
        'Basic_Pay': data.Basic_Pay,
        'Effective_From': data.Effective_From,
        'Effective_To': data.Effective_To,
        'Annual_CTC': data.Annual_Ctc,
        'Annual_Gross_Salary': data.Annual_Gross_Salary,
        'Monthly_Gross_Salary': data.Monthly_Gross_Salary,
        'Salary_Effective_Month': data.Salary_Effective_Month,
        'Employee_Name': data.EmployeeName,
        'Emp_Status': data.Emp_Status,
        'User_Defined_EmpId': data.userDefinedEmpId,
        'Added_On': data.Added_On,
        'Updated_On': data.Updated_On,
        'AddedByName': data.AddedByName,
        'UpdatedByName': data.UpdatedByName
      };
    case ehrTables.salaryRevisionDetails:
      return {
        'Revision_Id': data.Revision_Id,
        'Employee_Id': data.Employee_Id,
        'Template_Id': data.Template_Id,
        'Template_Name': data.Template_Name,
        'Basic_Pay': data.Basic_Pay,
        'Effective_From': data.Effective_From,
        'Payout_Month': data.Payout_Month,
        'Salary_Effective_To': data.Salary_Effective_To,
        'Revise_Ctc_By_Percentage': data.Revise_Ctc_By_Percentage,
        'Annual_CTC': data.Annual_Ctc,
        'Employee_Name': data.EmployeeName,
        'Emp_Status': data.Emp_Status,
        'User_Defined_EmpId': data.userDefinedEmpId,
        'Annual_Gross_Salary': data.Annual_Gross_Salary,
        'Monthly_Gross_Salary': data.Monthly_Gross_Salary,
        'revisionType': data.Revision_Type,
        'revisionStatus': data.Revision_Status,
        'Salary_Effective_Month': data.Salary_Effective_Month,
        'previousCtc': data.Previous_Ctc,
        'Added_On': data.Added_On,
        'Updated_On': data.Updated_On,
        'AddedByName': data.AddedByName,
        'UpdatedByName': data.UpdatedByName
      };
    default:
      return data;
  }
}

/**
 * Get allowance components for multiple records in one query
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} recordIds - Array of record IDs
 * @param {Object} tableConfig - Table configuration
 * @returns {Promise<Object>} - Object mapping record IDs to their allowance components
 */


async function getAllowanceComponentsByRecord(organizationDbConnection, recordIds, tableConfig) {
  try {
    const tableAlias = tableConfig.mainTable === ehrTables.salaryTemplate ? 'TAC' :
                      tableConfig.mainTable === 'employee_salary_history' ? 'EAH' : 'ESA';
    const allowanceTypeDetails = await organizationDbConnection
      .select(
        `${tableAlias}.*`,
        'AT.Allowance_Amount as Org_Amount',
        'AT.*',
        'SC.Component_Code',
        organizationDbConnection.raw('GROUP_CONCAT(BA.Form_Id) as Form_Id'),
        organizationDbConnection.raw('COALESCE(??.FBP_Max_Declaration, AT.FBP_Max_Declaration_Amount) as FBP_Max_Declaration', [tableAlias]),
        organizationDbConnection.raw('GROUP_CONCAT(A2.Allowance_Type_Id) as Allowance_Ids'),
      )
      .from(`${tableConfig.allowanceTable} as ${tableAlias}`)
      .leftJoin(`${ehrTables.allowanceType} as AT`, `AT.Allowance_Type_Id`, `${tableAlias}.Allowance_Type_Id`)
      .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
      .leftJoin('allowance_type_benefit_association as BA',
        'AT.Allowance_Type_Id', 'BA.Allowance_Type_Id')
      .leftJoin(ehrTables.allowanceType + ' as A2', 'A2.Allowance_Type_Id', 'BA.Allowance_Type_Id')
      .leftJoin(ehrTables.ehrForms + ' as EF', 'EF.Form_Id', 'BA.Form_Id')
      .groupBy(`${tableAlias}.${tableConfig.foreignKey}`, `${tableAlias}.Allowance_Type_Id`)
      .whereIn(`${tableAlias}.${tableConfig.foreignKey}`, recordIds);

    // Group allowance components by record ID
    const componentsByRecord = {};
    for (let allowanceData of allowanceTypeDetails) {
      const recordId = allowanceData[tableConfig.foreignKey];
      if (!componentsByRecord[recordId]) {
        componentsByRecord[recordId] = {
          Allowance: [],
          Fixed_Allowance: [],
          Bonus: [],
          Flexible_Benefit_Plan: [],
          Reimbursement: [],
          Basic_Pay: []
        };
      }

      const response = await commonFunctions.benefitAssociation(allowanceData);

      const mappedData = mapAllowanceData(response);

      if (allowanceData['Component_Code']?.toLowerCase() === 'fixed_allowance_amount') {
        componentsByRecord[recordId].Fixed_Allowance.push(mappedData);
      } else if (
        allowanceData['Is_Claim_From_Reimbursement'] && allowanceData['Is_Claim_From_Reimbursement'].toLowerCase() === 'yes'
      ) {
        componentsByRecord[recordId].Reimbursement.push(mappedData);
      }
      else if (
        allowanceData['Is_Flexi_Benefit_Plan'] && allowanceData['Is_Flexi_Benefit_Plan'].toLowerCase() === 'yes'
      ) {
        componentsByRecord[recordId].Flexible_Benefit_Plan.push(mappedData);
      }
      else if (allowanceData['Allowance_Mode'] && allowanceData['Allowance_Mode'].toLowerCase() === 'bonus') {
        componentsByRecord[recordId].Bonus.push(mappedData);
      }
      else if (allowanceData['Allowance_Mode'] && allowanceData['Allowance_Mode'].toLowerCase() === 'non bonus' &&
               allowanceData['Component_Code']?.toLowerCase() !== 'basic_salary_amount') {
        componentsByRecord[recordId].Allowance.push(mappedData);
      }
      else if (allowanceData['Component_Code']?.toLowerCase() === 'basic_salary_amount'){
        componentsByRecord[recordId].Basic_Pay.push(mappedData);
      }
    }


    return componentsByRecord;
  } catch (error) {
    console.log('Error in getAllowanceComponentsByRecord catch block', error);
    throw error;
  }
}

/**
 * Get revision payslip components for multiple records
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} recordIds - Array of record IDs
 * @param {Object} tableConfig - Table configuration
 * @returns {Promise<Object>} - Object mapping record IDs to their revision payslip components
 */
async function getRevisionPayslipComponentsByRecord(organizationDbConnection, recordIds, tableConfig) {
  try {
    const payslipComponentsByRecord = {};

    const revisionPayslipDetails = await organizationDbConnection
      .select(
        'RPD.Revision_Id',
        'AT.Allowance_Type_Id as Allowance_Id',
        organizationDbConnection.raw("CONCAT_WS(' ', 'Arrear', AT.Allowance_Name) as Allowance_Name"),
        'RPD.Component_Id',
        'RPD.Component_Amount',
        'RPD.Current_Component_Amount',
        'RPD.Previous_Component_Amount'
      )
      .from(`${ehrTables.revisionPayslipDetails} as RPD`)
      .leftJoin(`${ehrTables.allowanceType} as AT`, 'AT.Allowance_Type_Id', 'RPD.Component_Id')
      .whereIn('RPD.Revision_Id', recordIds)
      .orderBy([
        { column: 'AT.Allowance_Sequence', order: 'desc' },
        { column: 'AT.Allowance_Name', order: 'asc' }
      ]);

    // Group components by record ID
    for (let allowanceData of revisionPayslipDetails) {
      const recordId = allowanceData.Revision_Id;
      if (!payslipComponentsByRecord[recordId]) {
        payslipComponentsByRecord[recordId] = {
          revisionPayslipComponents: []
        };
      }
      const mappedData = mapRevisionPayslipData(allowanceData);
      payslipComponentsByRecord[recordId].revisionPayslipComponents.push(mappedData);
    }

    return payslipComponentsByRecord;
  } catch (error) {
    console.log('Error in revisionPayslipComponentsByRecord catch block', error);
    throw error;
  }
}

/**
 * Get retirals payslip components for multiple records
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} recordIds - Array of record IDs
 * @param {Object} tableConfig - Table configuration
 * @returns {Promise<Object>} - Object mapping record IDs to their retirals payslip components
 */

  async function getRetiralsPayslipByRecord(organizationDbConnection, recordIds, tableConfig) {
      try {
        const retiralsComponentsByRecord = {};
        const retiralsComponents = await organizationDbConnection
          .select(
            'PRD.*',
            'IC.Insurance_Name AS Insurance_Name',
            'IC.Insurance_Type AS Insurance_Type',
            'IC.Override_Insurance_Contribution_At_Employee_Level',
            'IC.Payment_Frequency',
            organizationDbConnection.raw('CASE WHEN PRD.Form_Id = 126 THEN PGS.Slab_Wise_NPS WHEN PRD.Form_Id = 52 THEN PGS.Slab_Wise_PF ELSE IC.Slab_Wise_Insurance END as Slab_Wise'),
            'EF.Form_Name AS Retirals_Name',
            'PFP.Override_PF_Contribution_Rate_At_Employee_Level',
            'NR.Enable_Employee_To_Declare_Employer_Share'
          )
          .from('payslip_retirals_revision_details as PRD')
          .leftJoin(`${ehrTables.insuranceConfiguration} as IC`, 'IC.InsuranceType_Id', 'PRD.Retirals_Id')
          .leftJoin(`${ehrTables.ehrForms} as EF`, 'EF.Form_Id', 'PRD.Form_Id')
          .leftJoin(`${ehrTables.payrollGeneralSettings} as PGS`, organizationDbConnection.raw('1=1'))
          .leftJoin(`${ehrTables.providentFund} as PFP`, organizationDbConnection.raw('1=1'))
          .leftJoin(`${ehrTables.npsRules} as NR`, organizationDbConnection.raw('1=1'))
          .whereIn(`PRD.${tableConfig.foreignKey}`, recordIds)
          for(let retiralsData of retiralsComponents){
            const recordId = retiralsData[tableConfig.foreignKey];
            if (!retiralsComponentsByRecord[recordId]) {
              retiralsComponentsByRecord[recordId] = {
                retiralsPayslipComponents: []
              };
            }
            const mappedData = mapRetiralsData(retiralsData);
            if(!retiralsComponentsByRecord[recordId]){
              retiralsComponentsByRecord[recordId] = [];
            }
            retiralsComponentsByRecord[recordId].retiralsPayslipComponents.push(mappedData);
          }
          return retiralsComponentsByRecord;
      } catch (error) {
        console.log('Error in getRetiralsComponentsByRecord catch block', error);
        throw error;
      }
    }
/**
 * Map allowance data to consistent format
 * @param {Object} response - Raw allowance data
 * @returns {Object} - Formatted allowance data
 */
function mapAllowanceData(response) {
  return {
    'Allowance_Id': response['Allowance_Id'],
    'Allowance_Name': response['Allowance_Name'],
    'Name_In_Payslip': response['Name_In_Payslip'],
    'Allowance_Type': response['Allowance_Type'],
    'Allowance_Type_Id': response['Allowance_Type_Id'],
    'Is_Basic_Pay': response['Is_Basic_Pay'], // Keep for backward compatibility
    'Formula_Based': response['Formula_Based'], // Keep for backward compatibility
    'Component_Code': response['Component_Code'],
    'Allowance_Ids': response['Allowance_Ids'],
    'Period': response['Period'],
    'Amount': response['Amount'],
    'AllowanceWages': response['Allowance_Wages'],
    'Percentage': response['Percentage'],
    'orgAmount': response['Org_Amount'],
    'Form_Id': response['Form_Id'],
    'Form_Name': response['Form_Name'],
    'pfMapped': response['pfMapped'],
    'variableInsuranceMapped': response['variableInsuranceMapped'],
    'fixedInsuranceMapped': response['fixedInsuranceMapped'],
    'gratuityMapped': response['gratuityMapped'],
    'npsMapped': response['npsMapped'],
    'FBP_Max_Declaration': response['FBP_Max_Declaration'],
    'Description': response['Description'],
    'Is_Flexi_Benefit_Plan': response['Is_Flexi_Benefit_Plan'],
    'Custom_Formula': response['Custom_Formula'],
    'Restrict_Employee_FBP_Override': response['Restrict_Employee_FBP_Override']
  };
}

function mapRevisionPayslipData(response){
  return {
    'Allowance_Id': response['Allowance_Id'],
    'Allowance_Name': response['Allowance_Name'],
    'Amount': response['Component_Amount'],
    'Current_Component_Amount': response['Current_Component_Amount'],
    'Previous_Component_Amount': response['Previous_Component_Amount']
  };
}
function mapRetiralsData(response){
  return {
    'Form_Id': response['Form_Id'],
    'Retirals_Id': response['Retirals_Id'],
    'Current_Employee_Share_Amount': response['Current_Employee_Share_Amount'],
    'Previous_Employee_Share_Amount': response['Previous_Employee_Share_Amount'],
    'Employee_Share_Amount': response['Employee_Share_Amount'],
    'Current_Employer_Share_Amount': response['Current_Employer_Share_Amount'],
    'Previous_Employer_Share_Amount': response['Previous_Employer_Share_Amount'],
    'Employer_Share_Amount': response['Employer_Share_Amount'],
    'Insurance_Name': response['Insurance_Name'],
    'Insurance_Type': response['Insurance_Type'],
    'Retirals_Name': response['Retirals_Name'],
    'Payment_Frequency': response['Payment_Frequency'],
    'Override_Insurance_Contribution_At_Employee_Level': response['Override_Insurance_Contribution_At_Employee_Level']
  };
}

/**
 * Map gross data to consistent format
 * @param {Object} response - Raw gross data
 * @returns {Object} - Formatted gross data
 */
function mapGrossData(response) {
  return {
    'Gross_Id': response['Gross_Id'],
    'Gross_Name': response['Gross_Name'],
    'Display_Name': response['Display_Name'],
    'Calculation_Type': response['Calculation_Type'],
    'Custom_Formula': response['Custom_Formula'],
    'Amount': response['Amount'],
    'Gross_Description': response['Gross_Description'],
    'Salary_Component_Id': response['Salary_Component_Id'],
    'Salary_Component_Name': response['Salary_Component_Name'],
    'Salary_Component_Code': response['Salary_Component_Code'],
    'Salary_Component_Type': response['Salary_Component_Type']
  };
}

/**
 * Get retirals components for multiple records in one query
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} recordIds - Array of record IDs
 * @param {Object} tableConfig - Table configuration
 * @returns {Promise<Object>} - Object mapping record IDs to their retirals components
 */
async function getRetiralsComponentsByRecord(organizationDbConnection, recordIds, tableConfig) {
  try {
    const retiralsComponents = await organizationDbConnection
      .select(
        'TRC.*',
        'IC.Insurance_Name AS Insurance_Name',
        'IC.Insurance_Type AS Insurance_Type',
        'IC.Override_Insurance_Contribution_At_Employee_Level',
        'IC.Payment_Frequency',
        'EF.Form_Name as Retirals_Name',
        organizationDbConnection.raw('CASE WHEN TRC.Form_Id = 126 THEN PGS.Slab_Wise_NPS WHEN TRC.Form_Id = 52 THEN PGS.Slab_Wise_PF ELSE IC.Slab_Wise_Insurance END as Slab_Wise'),
        organizationDbConnection.raw('GROUP_CONCAT(A2.Allowance_Type_Id) as Allowance_Ids'),
        'PFP.Override_PF_Contribution_Rate_At_Employee_Level',
        'NR.Enable_Employee_To_Declare_Employer_Share'
      )
      .from(`${tableConfig.retiralsTable} as TRC`)
      .leftJoin('allowance_type_benefit_association as BA',
        'TRC.Form_Id', 'BA.Form_Id')
        .leftJoin(ehrTables.allowanceType + ' as A2', 'A2.Allowance_Type_Id', 'BA.Allowance_Type_Id')
      .innerJoin(`${ehrTables.ehrForms} as EF`, 'EF.Form_Id', 'TRC.Form_Id')
      .leftJoin(`${ehrTables.insuranceConfiguration} as IC`, 'IC.InsuranceType_Id', 'TRC.Retirals_Id')
      .leftJoin(`${ehrTables.payrollGeneralSettings} as PGS`, organizationDbConnection.raw('1=1'))
      .leftJoin(`${ehrTables.providentFund} as PFP`, organizationDbConnection.raw('1=1'))
      .leftJoin(`${ehrTables.npsRules} as NR`, organizationDbConnection.raw('1=1'))
      .whereIn(`TRC.${tableConfig.foreignKey}`, recordIds)
      .groupBy(`TRC.${tableConfig.foreignKey}`,`TRC.Form_Id`,`TRC.Retirals_Id`);

    // Group retirals by record ID and add PF settings
    const retiralsByRecord = {};
    for (let retiralsData of retiralsComponents) {
      const recordId = retiralsData[tableConfig.foreignKey];
      if (!retiralsByRecord[recordId]) {
        retiralsByRecord[recordId] = [];
      }
      retiralsByRecord[recordId].push(retiralsData);
    }

    return retiralsByRecord;
  } catch (error) {
    console.log('Error in getRetiralsComponentsByRecord catch block', error);
    throw error;
  }
}

/**
 * Get gross components for multiple records in one query
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} recordIds - Array of record IDs
 * @param {Object} tableConfig - Table configuration
 * @returns {Promise<Object>} - Object mapping record IDs to their gross components
 */
async function getGrossComponentsByRecord(organizationDbConnection, recordIds, tableConfig) {
  try {
    const grossComponents = await organizationDbConnection
      .select(
        'TGC.*',
        'GC.Gross_Name',
        'GC.Display_Name',
        'GC.Calculation_Type',
        'GC.Custom_Formula',
        'GC.Description as Gross_Description',
        'SC.Component_Name as Salary_Component_Name',
        'SC.Component_Code as Salary_Component_Code',
        'SC.Component_Type as Salary_Component_Type'
      )
      .from(`${tableConfig.grossTable} as TGC`)
      .innerJoin(`${ehrTables.grossConfiguration} as GC`, 'GC.Gross_Id', 'TGC.Gross_Id')
      .leftJoin(`${ehrTables.salaryComponents} as SC`, 'SC.Component_Id', 'GC.Salary_Component_Id')
      .whereIn(`TGC.${tableConfig.foreignKey}`, recordIds)
      .where('GC.Status', 'Active')
      .orderBy('GC.Gross_Name', 'asc');

    // Group gross components by record ID
    const grossByRecord = {};
    for (let grossData of grossComponents) {
      const recordId = grossData[tableConfig.foreignKey];
      if (!grossByRecord[recordId]) {
        grossByRecord[recordId] = [];
      }

      const mappedData = mapGrossData(grossData);
      grossByRecord[recordId].push(mappedData);
    }

    return grossByRecord;
  } catch (error) {
    console.log('Error in getGrossComponentsByRecord catch block', error);
    throw error;
  }
}
/**
 * Get organization details for salary date calculations
 * @param {Object} organizationDbConnection - Database connection
 * @param {string} orgCode - Organization code to scope the query
 * @returns {Promise<Object>} - Organization details
 */
async function getOrganizationDetails(organizationDbConnection, orgCode) {
  try {
    // Validate orgCode is provided to prevent cross-org data leakage
    if (!orgCode) {
      console.error('Error: orgCode is required for getOrganizationDetails');
      return {};
    }

    const orgDetails = await organizationDbConnection(ehrTables.orgDetails)
      .select('Payment_Day', 'Cutoff_Day', 'Consider_Cutoff_Days_For_Attendance_And_Timeoff',
             'Paycycle', 'Paycycle_End_Day', 'Bimonthly_Pay_Cycle_Mid_Day', 'Payroll_Period')
      .where('Org_Code', orgCode)
      .first();

    return orgDetails || {};
  } catch (error) {
    console.error('Error fetching organization details:', error);
    return {};
  }
}

// Export resolvers
const resolvers = {
  Query: {
    listSalaryTemplateDetails
  }
};

exports.resolvers = resolvers;