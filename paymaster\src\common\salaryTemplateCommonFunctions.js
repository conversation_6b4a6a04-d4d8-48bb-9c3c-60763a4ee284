// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common constant files
const constants = require('../common/appconstants');
// require table alias
const {ehrTables} = require('../common/tablealias');

// declarations
let defaultFormDetails=[{formId:'',formName:''}];

// function to get benefit association
module.exports = {
    benefitAssociation : (key) => {
        console.log('benefitAssociation - Input key:', JSON.stringify({
            Allowance_Type_Id: key.Allowance_Type_Id,
            Amount: key.Amount,
            Allowance_Type: key.Allowance_Type,
            Percentage: key.Percentage,
            Form_Id: key.Form_Id
        }));
        
        // initially assign it to 0
        key.pfMapped=0;
        key.variableInsuranceMapped=0;
        key.gratuityMapped=0;
        key.npsMapped=0;
        
        try{
            // check whether formId exist in this function
            if(key.Form_Id)
            {
                let formIds=key.Form_Id;
                // split the formIds
                let splitFormId=formIds.toString().split(',');
                if(splitFormId.length)
                {
                    for(let i=0;i<splitFormId.length;i++){
                        // check whether the formid is same as pf form then return it as pfMapped
                        if(parseInt(splitFormId[i])===constants.formId.pfId){
                            key.pfMapped=1;
                        }
                        // check whether the formid is same as variable insurance form then return it as variableInsuranceMapped
                        if(parseInt(splitFormId[i])===constants.formId.variableInsuranceId){
                            key.variableInsuranceMapped=1;
                        }
                        // check whether the formid is same as gratuity form then return it as gratuityMapped
                        if(parseInt(splitFormId[i])===constants.formId.gratuityId){
                            key.gratuityMapped=1;
                        }
                        // check whether the formid is same as nps form then return it as npsMapped
                        if(parseInt(splitFormId[i])===constants.formId.npsId){
                            key.npsMapped=1;
                        }
                        else{
                            console.log('Some other form id is mapped',parseInt(splitFormId[i]));
                        }
                    }
                }
            }
            
            console.log('benefitAssociation - Output key:', JSON.stringify({
                Allowance_Type_Id: key.Allowance_Type_Id,
                Amount: key.Amount,
                Allowance_Type: key.Allowance_Type,
                Percentage: key.Percentage,
                pfMapped: key.pfMapped,
                variableInsuranceMapped: key.variableInsuranceMapped
            }));
            
            return key;   
        }
        catch(error){
            console.log("Error in benefitAssociation function catch block",error);
            return key;   
        }
    },
    // function to form fixed insurance array
    fixedInsuranceArray : (organizationDbConnection,appmanagerDbConnection,source,key) => {
        try{
            // variable declarations
            let mainQuery;
            let fixedInsuranceArrayDetails=[];
            let fixedInsuranceJson={};

            let subQuery=organizationDbConnection
            .select('IT.Insurance_Name','FI.InsuranceType_Id as Insurance_Type_Id','UD.Unit_Tag as Period')
            .from(ehrTables.fixedInsurance + ' as FI')
            .innerJoin(ehrTables.insuranceType + ' as IT', 'FI.InsuranceType_Id', 'IT.InsuranceType_Id')
            .innerJoin(ehrTables.unitData + ' as UD', 'UD.Target_Value', 'FI.PaymentMode_Id')
            .innerJoin(ehrTables.insurancetypeGrade + ' as ITG', 'FI.InsuranceType_Id', 'ITG.InsuranceType_Id')
            .where('IT.InsuranceType_Status','Active')
            .where('UD.Target_Unit', 'LIKE', 'Month')
            .where('FI.Coverage',0)

            // make query based on source form
            if(source.toLowerCase()==='salary template'){
                mainQuery=subQuery
            }
            else{
                mainQuery= subQuery
                    .where('FI.Insurance_Id',key.Retirals_Id)
            }
            return(
                mainQuery
                .then(async(fixedInsuranceDetails) =>{
                    // check data exist or not
                    if(fixedInsuranceDetails.length>0){
                        // get form details based on formId
                        let formDetails=await commonLib.func.getFormNameBasedOnId(appmanagerDbConnection,constants.formId.fixedInsuranceId);
                        fixedInsuranceData=fixedInsuranceDetails[0];
                        fixedInsuranceData.Retirals_Id=key.Retirals_Id;
                        fixedInsuranceData.Retirals_Type=key.Retirals_Type;
                        fixedInsuranceData.Retiral_Wages=key.Retiral_Wages;
                        fixedInsuranceData.Employer_Share_Percentage=key.Employer_Share_Percentage;
                        fixedInsuranceData.Employer_Share_Amount=key.Employer_Share_Amount;
                        fixedInsuranceData.PF_Employer_Contribution=key.PF_Employer_Contribution;
                        fixedInsuranceData.Employer_Statutory_Limit=key.Employer_Statutory_Limit;
                        fixedInsuranceData.Eligible_For_EPS=key.Eligible_For_EPS;
                        fixedInsuranceData.Admin_Charge=key.Admin_Charge;
                        fixedInsuranceData.EDLI_Charge=key.EDLI_Charge;
                        // if request from salary details form then include employee values 
                        if(source.toLowerCase()==='salary details'){
                            fixedInsuranceData.Employee_Share_Percentage=key.Employee_Share_Percentage;
                            fixedInsuranceData.Employee_Share_Amount=key.Employee_Share_Amount;
                            fixedInsuranceData.PF_Employee_Contribution=key.PF_Employee_Contribution;
                            fixedInsuranceData.Employee_Statutory_Limit=key.Employee_Statutory_Limit;
                        }
                        // push fixed insurance into array
                        fixedInsuranceArrayDetails.push(fixedInsuranceData);
                        fixedInsuranceJson['fixedInsuranceArray']=fixedInsuranceArrayDetails;
                        fixedInsuranceJson['formDetails']=formDetails;
                        return fixedInsuranceJson;
                    }
                    else{
                        return {};
                    }
                })
                //catch db-connectivity errors
                .catch(function (catchError) {
                    console.log('Error in fixedInsuranceArray function .catch() block', catchError);
                    return {};
                })            
            )
        }
        catch(error){
            console.log('Error in fixedInsuranceArray function main catch block', error);
            return {};
        }
    },
    // function to form variable insurance array
    variableInsuranceArray : (organizationDbConnection,appmanagerDbConnection,source,key) => {
        try{
            // variable declarations
            let mainQuery;
            let variableInsuranceJson={};
            let esiJson={};
            let esiArrayDetails=[];
            let insuranceArray=[];

            let subQuery=organizationDbConnection
            .select('IT.Insurance_Name','IT.Employee_State_Insurance','VI.InsuranceType_Id as Insurance_Type_Id','UD.Unit_Tag as Period')
            .from(ehrTables.variableInsurance + ' as VI')
            .innerJoin(ehrTables.insuranceType + ' as IT', 'VI.InsuranceType_Id', 'IT.InsuranceType_Id')
            .innerJoin(ehrTables.unitData + ' as UD', 'UD.Target_Value', 'VI.PaymentMode_Id')
            .innerJoin(ehrTables.insurancetypeGrade + ' as ITG', 'VI.InsuranceType_Id', 'ITG.InsuranceType_Id')
            .where('IT.InsuranceType_Status','Active')
            .where('UD.Target_Unit', 'LIKE', 'Month')
            .where('VI.Coverage',0)

            // formation of query based on source form
            if(source.toLowerCase()==='salary template'){
                mainQuery=subQuery
            }
            else{
                mainQuery= subQuery
                .where('VI.Insurance_Id',key.Retirals_Id)
            }
            return(
                mainQuery
                .then(async(variableInsuranceDetails) =>{
                    // check data exist or not
                    if(variableInsuranceDetails.length>0){
                        // get form details based on formId
                        let formDetails=await commonLib.func.getFormNameBasedOnId(appmanagerDbConnection,constants.formId.variableInsuranceId);
                        variableInsuranceData=variableInsuranceDetails[0];
                        variableInsuranceData.Retirals_Id=key.Retirals_Id;
                        variableInsuranceData.Retirals_Type=key.Retirals_Type;
                        variableInsuranceData.Retiral_Wages=key.Retiral_Wages;
                        variableInsuranceData.Employer_Share_Percentage=key.Employer_Share_Percentage;
                        variableInsuranceData.Employer_Share_Amount=key.Employer_Share_Amount;
                        variableInsuranceData.PF_Employer_Contribution=key.PF_Employer_Contribution;
                        variableInsuranceData.Employer_Statutory_Limit=key.Employer_Statutory_Limit;
                        variableInsuranceData.Eligible_For_EPS=key.Eligible_For_EPS;
                        variableInsuranceData.Admin_Charge=key.Admin_Charge;
                        variableInsuranceData.EDLI_Charge=key.EDLI_Charge;

                        // if request from salary details form then include employee values 
                        if(source.toLowerCase()==='salary details'){
                            variableInsuranceData.Employee_Share_Percentage=key.Employee_Share_Percentage;
                            variableInsuranceData.Employee_Share_Amount=key.Employee_Share_Amount;
                            variableInsuranceData.PF_Employee_Contribution=key.PF_Employee_Contribution;
                            variableInsuranceData.Employee_Statutory_Limit=key.Employee_Statutory_Limit;
                        }

                        // if Employee_State_Insurance is 1 then return it as esi details else variable insurance
                        if(variableInsuranceData.Employee_State_Insurance){
                            esiArrayDetails.push(variableInsuranceData);
                        }
                        else{
                            insuranceArray.push(variableInsuranceData);
                        }
                        // formation of response
                        variableInsuranceJson['variableInsuranceArray']=insuranceArray;
                        variableInsuranceJson['formDetails']=formDetails;
                        esiJson['esiArray']=esiArrayDetails;
                        esiJson['formDetails']=defaultFormDetails;
                        return (Object.keys(esiJson).length)?esiJson:variableInsuranceJson;
                    }
                    else{
                        return {};
                    }
                })
                //catch db-connectivity errors
                .catch(function (catchError) {
                    console.log('Error in variableInsuranceArray function .catch() block', catchError);
                    return {};
                })            
            )
        }
        catch(error){
            console.log('Error in variableInsuranceArray function main catch block', error);
            return {};
        }
    },
    // function to form fixedHealth Insurance array
    fixedHealthInsuranceArray : (organizationDbConnection,appmanagerDbConnection,source,key) => {
        try{
            // variable declarations
            let mainQuery;
            let fixedHealthInsuranceArrayDetails=[];
            let fixedHealthInsuranceJson={};

            let subQuery=organizationDbConnection
            .select('FHIT.Title as Insurance_Name','FHIT.Period','FHI.Insurance_Type_Id')
            .from(ehrTables.fixedHealthInsurance + ' as FHI')
            .innerJoin(ehrTables.fixedHealthInsuranceType + ' as FHIT', 'FHIT.Insurance_Type_Id', 'FHI.Insurance_Type_Id')
            .where('FHI.Insurance_Status','Active')
            .where('FHIT.InsuranceType_Status','Active')
            .where('FHI.Coverage',0)

            // formation of query based on source form
            if(source.toLowerCase()==='salary template'){
                mainQuery=subQuery
            }
            else{
                mainQuery= subQuery
                    .where('FHI.Insurance_Id',key.Retirals_Id)
            }
            return(
                mainQuery
                .then(async(fixedHealthInsuranceDetails) =>{
                    // check data exist or not
                    if(fixedHealthInsuranceDetails.length>0){
                        // get form details based on formId
                        let formDetails=await commonLib.func.getFormNameBasedOnId(appmanagerDbConnection,constants.formId.fixedHealthInsuranceId);
                        fixedHealthInsuranceData=fixedHealthInsuranceDetails[0];
                        fixedHealthInsuranceData.Retirals_Id=key.Retirals_Id;
                        fixedHealthInsuranceData.Retirals_Type=key.Retirals_Type;
                        fixedHealthInsuranceData.Retiral_Wages=key.Retiral_Wages;
                        fixedHealthInsuranceData.Employer_Share_Percentage=key.Employer_Share_Percentage;
                        fixedHealthInsuranceData.Employer_Share_Amount=key.Employer_Share_Amount;
                        fixedHealthInsuranceData.PF_Employer_Contribution=key.PF_Employer_Contribution;
                        fixedHealthInsuranceData.Employer_Statutory_Limit=key.Employer_Statutory_Limit;
                        fixedHealthInsuranceData.Eligible_For_EPS=key.Eligible_For_EPS;
                        fixedHealthInsuranceData.Admin_Charge=key.Admin_Charge;
                        fixedHealthInsuranceData.EDLI_Charge=key.EDLI_Charge;
                        // if request from salary details form then include employee values 
                        if(source.toLowerCase()==='salary details'){
                            fixedHealthInsuranceData.Employee_Statutory_Limit=key.Employee_Statutory_Limit;
                            fixedHealthInsuranceData.PF_Employee_Contribution=key.PF_Employee_Contribution;
                            fixedHealthInsuranceData.Employee_Share_Percentage=key.Employee_Share_Percentage;
                            fixedHealthInsuranceData.Employee_Share_Amount=key.Employee_Share_Amount;
                        }
                        // form response
                        fixedHealthInsuranceArrayDetails.push(fixedHealthInsuranceData);
                        fixedHealthInsuranceJson['fixedHealthInsuranceArray']=fixedHealthInsuranceArrayDetails;
                        fixedHealthInsuranceJson['formDetails']=formDetails;
                        return fixedHealthInsuranceJson;
                    }
                    else{
                        return {};
                    }
                })
                //catch db-connectivity errors
                .catch(function (catchError) {
                    console.log('Error in fixedHealthInsuranceArray function .catch() block', catchError);
                    return {};
                })            
            )
        }
        catch(error){
            console.log('Error in fixedHealthInsuranceArray function main catch block', error);
            return {};
        }
    },
    // function to form pf array
    pfArray : (organizationDbConnection,appmanagerDbConnection,source,key) => {
        try{
            // variable declarations
            let pfJson={};
            let pfArrayDetails=[];    
            let mainQuery;

            let subQuery=organizationDbConnection
            .select('Admin_Charge_Max_Amount','Allow_Epf_Excess_Contribution','Allow_Eps_Excess_Contribution',
            'Admin_Charge_Part_Of_Ctc','Edli_Charge_Part_Of_Ctc','EDLI_Charge_Max_Amount')
            .from(ehrTables.orgPf)
            .where('Employee_Share','>',0)
            .where('Company_Share','>',0)
            .andWhere('Statutory_Salary_Limit','>',0)
            .where('Salary_Type','MON')

            if(source.toLowerCase()==='salary template'){
                mainQuery=subQuery
            }
            else{
                mainQuery= subQuery
                .where('PfOrg_Id',key.Retirals_Id)
            }

            return(
                mainQuery
                .then(async(pfDetails) =>{
                    // check data exist or not
                    if(pfDetails.length>0){
                        // get form details based on formId
                        let formDetails=await commonLib.func.getFormNameBasedOnId(appmanagerDbConnection,constants.formId.pfId);
                        let retiralsName=(formDetails.length)?formDetails[0].formName:'';
                        pfDetails={
                            'Insurance_Name'            : retiralsName,
                            'Retirals_Id'               : key.Retirals_Id,
                            'Retirals_Type'             : key.Retirals_Type,
                            'Retiral_Wages'             : key.Retiral_Wages,
                            'Employer_Share_Percentage' : key.Employer_Share_Percentage,
                            'Employer_Share_Amount'     : key.Employer_Share_Amount,
                            'PF_Employer_Contribution'  : key.PF_Employer_Contribution,
                            'Employer_Statutory_Limit'  : key.Employer_Statutory_Limit,
                            'Eligible_For_EPS'          : key.Eligible_For_EPS,
                            'Admin_Charge_Max_Amount'   :pfDetails[0].Admin_Charge_Max_Amount,
                            'EDLI_Charge_Max_Amount'    :pfDetails[0].EDLI_Charge_Max_Amount,
                            'Admin_Charge_Part_Of_Ctc'  :pfDetails[0].Admin_Charge_Part_Of_Ctc,
                            'Edli_Charge_Part_Of_Ctc'   :pfDetails[0].Edli_Charge_Part_Of_Ctc,
                            'Allow_Epf_Excess_Contribution':pfDetails[0].Allow_Epf_Excess_Contribution,
                            'Allow_Epf_Excess_Contribution':pfDetails[0].Allow_Epf_Excess_Contribution,
                            'Admin_Charge' : key.Admin_Charge,
                            'EDLI_Charge' : key.EDLI_Charge,
                            'Period' : 'Monthly'
                        }
                        // if request from salary details form then include employee values 
                        if(source.toLowerCase()==='salary details'){
                            pfDetails.Employee_Share_Percentage=key.Employee_Share_Percentage;
                            pfDetails.Employee_Share_Amount=key.Employee_Share_Amount;
                            pfDetails.PF_Employee_Contribution=key.PF_Employee_Contribution;
                            pfDetails.Employee_Statutory_Limit=key.Employee_Statutory_Limit;
                        }
                        pfArrayDetails.push(pfDetails);
                        pfJson['pfArray']=pfArrayDetails;
                        pfJson['formDetails']=formDetails;
                        return pfJson;
                    }
                    else{
                        return {};
                    }
                })
                //catch db-connectivity errors
                .catch(function (catchError) {
                    console.log('Error in pfArray function .catch() block', catchError);
                    return {};
                })            
            )
        }
        catch(error){
            console.log('Error in pfArray function main catch block', error);
            return {};
        }
    },
    // formation of nps array
    npsArray : async (appmanagerDbConnection,key,source) => {
        try{
            let npsDetails,npsJson={};
            let npsArrayDetails=[];
            // get form details based on formId
            let formDetails=await commonLib.func.getFormNameBasedOnId(appmanagerDbConnection,constants.formId.npsId);
            let retiralsName=(formDetails.length)?formDetails[0].formName:'';

            npsDetails={
                'Insurance_Name' : retiralsName,
                'Retirals_Id': key.Retirals_Id,
                'Retirals_Type' : key.Retirals_Type,
                'Retiral_Wages': key.Retiral_Wages,
                'Employer_Share_Percentage' : key.Employer_Share_Percentage,
                'Employer_Share_Amount' : key.Employer_Share_Amount,
                'PF_Employer_Contribution' : key.PF_Employer_Contribution,
                'Employer_Statutory_Limit' : key.Employer_Statutory_Limit,
                'Eligible_For_EPS' : key.Eligible_For_EPS,
                'Admin_Charge' : key.Admin_Charge,
                'EDLI_Charge' : key.EDLI_Charge,
                'Period' : 'Monthly'
            }
            // if request from salary details form then include employee values 
            if(source.toLowerCase()==='salary details'){
                npsDetails.Employee_Share_Percentage=key.Employee_Share_Percentage;
                npsDetails.Employee_Share_Amount=key.Employee_Share_Amount;
                npsDetails.PF_Employee_Contribution=key.PF_Employee_Contribution;
                npsDetails.Employee_Statutory_Limit=key.Employee_Statutory_Limit;
            }
            npsArrayDetails.push(npsDetails);
            npsJson['npsArray']=npsArrayDetails;
            npsJson['formDetails']=formDetails
            return npsJson;
        }
        catch(error){
            console.log('Error in npsArray function catch block', error);
            return {};
        }
    },
    // formation of gratuity array
    gratuityArray : async (appmanagerDbConnection,key,source) => {
        try{
            let gratuityDetails,gratuityJson={};
            let gratuityArrayDetails=[];

            // get form details based on formId
            let formDetails=await commonLib.func.getFormNameBasedOnId(appmanagerDbConnection,constants.formId.gratuityId);
            let retiralsName=(formDetails.length)?formDetails[0].formName:'';

            gratuityDetails={
                'Insurance_Name' : retiralsName,
                'Retirals_Id': key.Retirals_Id,
                'Retirals_Type' : key.Retirals_Type,
                'Retiral_Wages': key.Retiral_Wages,
                'Employer_Share_Percentage' : key.Employer_Share_Percentage,
                'Employer_Share_Amount' : key.Employer_Share_Amount,
                'PF_Employer_Contribution' : key.PF_Employer_Contribution,
                'Employer_Statutory_Limit' : key.Employer_Statutory_Limit,
                'Eligible_For_EPS' : key.Eligible_For_EPS,
                'Admin_Charge' : key.Admin_Charge,
                'EDLI_Charge' : key.EDLI_Charge,
                'Period' : 'Annually'
            }
            // if request from salary details form then include employee values 
            if(source.toLowerCase()==='salary details'){
                gratuityDetails.Employee_Share_Percentage=key.Employee_Share_Percentage;
                gratuityDetails.Employee_Share_Amount=key.Employee_Share_Amount;
                gratuityDetails.PF_Employee_Contribution=key.PF_Employee_Contribution;
                gratuityDetails.Employee_Statutory_Limit=key.Employee_Statutory_Limit;
            }            
            gratuityArrayDetails.push(gratuityDetails);
            gratuityJson['gratuityArray']=gratuityArrayDetails;
            gratuityJson['formDetails']=formDetails
            return gratuityJson;
        }
        catch(error){
            console.log('Error in gratuityArray function catch block', error);
            return {};
        }
    }
};
