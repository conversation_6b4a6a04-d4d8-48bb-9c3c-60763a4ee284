const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../common/tablealias');
const { formId } = require('../common/appconstants');
const { getRoundOffValue, getRoundOffSettings, getFiscalMonthYear } = require('../common/commonfunctions');

let organizationDbConnection;
let roundOffSettings;

module.exports.calculatePayslipRetiralDetails = async (parent, args, context, info) => {
    console.log('Inside calculatePayslipRetiralDetails function');

    try {
        organizationDbConnection = context.orgdb ? context.orgdb : knex(context.connection.OrganizationDb);

        // NEW INPUT STRUCTURE: Extract employeeIds, salaryMonth, formId
        const employeeIds = args.employeeIds;
        const salaryMonth = args.salaryMonth;
        const retiralFormId = args.formId;
        let orgCode = context.orgCode;

        // Validate inputs
        if (!employeeIds || !Array.isArray(employeeIds) || employeeIds.length === 0) {
            throw new ApolloError('employeeIds array is required and cannot be empty', 'INVALID_INPUT');
        }
        if (!salaryMonth) {
            throw new ApolloError('salaryMonth is required', 'INVALID_INPUT');
        }
        if (!retiralFormId) {
            throw new ApolloError('formId is required', 'INVALID_INPUT');
        }

        // If orgCode is not in context, get it from org_details table
        if (!orgCode) {
            const orgDetailsRow = await organizationDbConnection(ehrTables.orgDetails)
                .select('Org_Code')
                .first();

            orgCode = orgDetailsRow?.Org_Code;
            if (!orgCode) {
                throw new ApolloError('Organization code not found', 'ORG_CODE_NOT_FOUND');
            }
        }

        const [month, year] = salaryMonth.split(',');

        // Get salary dates for the month using corelib function
        const { Salary_Date, Last_SalaryDate } = await commonLib.func.getSalaryDay(
            orgCode,
            organizationDbConnection,
            month,
            year
        );
        // Fetch all required data in parallel
        const [unpaidLeaveDaysMap, workScheduleData, employeeRetiralDetails, orgDetails, roundOffSetting] = await Promise.all([
            commonLib.shiftAndTimeManagement.getUnpaidLeaveDaysForEmployees(
                organizationDbConnection,
                employeeIds,
                Salary_Date,
                Last_SalaryDate,
                salaryMonth
            ),
            commonLib.shiftAndTimeManagement.prepareAttendanceInputs(
                organizationDbConnection,
                employeeIds,
                Salary_Date,
                Last_SalaryDate,
                true
            ),
            getEmployeeRetiralDetails(organizationDbConnection, employeeIds, retiralFormId),
            getOrganizationDetails(organizationDbConnection, orgCode),
            getRoundOffSettings(organizationDbConnection)
        ]);

        const employeeWorkScheduleShiftDetails = {
            employeeDetails: workScheduleData.employeeDetails,
            allWorkScheduleDetails: workScheduleData.allWorkScheduleDetails,
            allEmployeeShiftDetails: workScheduleData.allEmployeeShiftDetails,
            allHolidayDetails: workScheduleData.allHolidayDetails
        };

        roundOffSettings = roundOffSetting;

        // Calculate remaining fiscal months for forecasting
        const remainingMonths = await calculateRemainingFiscalMonths(organizationDbConnection, orgCode, orgDetails, salaryMonth);

        // Get previous retiral deductions for all employees in batch (outside loop)
        const previousRetiralsDeductionsMap = await getPreviousMonthsRetiralsDeductions(
            organizationDbConnection,
            employeeIds,
            orgDetails,
            orgCode,
            retiralFormId,
            Last_SalaryDate
        );

        // Get NPS-specific data in parallel if needed
        let npsInvestmentCategory = null;
        let lastDeclarationId = 0;

        if (retiralFormId === formId.npsId) {
            [npsInvestmentCategory, lastDeclarationId] = await Promise.all([
                getNPSInvestmentCategory(organizationDbConnection, orgDetails.Assessment_Year, retiralFormId),
                getLastDeclarationId(organizationDbConnection)
            ]);

            if (!npsInvestmentCategory) {
                throw new ApolloError('NPS Investment Category not found for Form_Id=126', 'CONFIGURATION_ERROR');
            }
        }

        // Process each employee
        const results = [];
        for (const employeeId of employeeIds) {
            try {
                // Find employee's retiral configuration
                const employeeRetiral = employeeRetiralDetails.find(ret => ret.Employee_Id === employeeId);

                if (!employeeRetiral) {
                    results.push({
                        employeeId: employeeId,
                        success: false,
                        errorMessage: 'Retiral configuration not found for employee',
                        npsDetails: null
                    });
                    continue;
                }

                // Get retiral wages from employee_salary_retirals table
                const employerRetiralWages = parseFloat(employeeRetiral?.Employer_Retiral_Wages || 0);
                const employeeRetiralWages = parseFloat(employeeRetiral?.Employee_Retiral_Wages || 0);
                const unpaidLeaveDays = unpaidLeaveDaysMap.get(employeeId) || 0;
                let actualWorkingDaysForTheEmployee = await commonLib.shiftAndTimeManagement.getBusinessWorkingDays(
                    Salary_Date,
                    Last_SalaryDate,
                    employeeId,
                    1, // totalWorkingDays
                    null, // leaveCalculationDays
                    null, // formName
                    null, // compOffCalculationMethod
                   employeeWorkScheduleShiftDetails
                );

                actualWorkingDaysForTheEmployee = actualWorkingDaysForTheEmployee?.days || 0;

                let workedDaysForTheEmployee = await commonLib.shiftAndTimeManagement.getBusinessWorkingDays(
                    Salary_Date,
                    Last_SalaryDate,
                    employeeId,
                    null, // totalWorkingDays
                    null, // leaveCalculationDays
                    null, // formName
                    null, // compOffCalculationMethod
                    employeeWorkScheduleShiftDetails
                );

                workedDaysForTheEmployee = workedDaysForTheEmployee?.days || 0;
                let adjustedEmployerRetiralWages = employerRetiralWages;
                let adjustedEmployeeRetiralWages = employeeRetiralWages;
                let unpaidLeaveDeduction = 0;
                const workedDaysMinusUnpaidLeaveDays = workedDaysForTheEmployee - unpaidLeaveDays;

                // Prepare retirals object
                const retiralsConfig = {
                    Retirals_Id: employeeRetiral.Retirals_Id,
                    Form_Id: retiralFormId,
                    Employee_Share_Percentage: employeeRetiral.Employee_Share_Percentage,
                    Employer_Share_Percentage: employeeRetiral.Employer_Share_Percentage,
                    Employee_Share_Amount: employeeRetiral.Employee_Share_Amount,
                    Employer_Share_Amount: employeeRetiral.Employer_Share_Amount,
                    Retirals_Type: employeeRetiral.Retirals_Type
                };

                if (workedDaysMinusUnpaidLeaveDays !== actualWorkingDaysForTheEmployee) {
                    // Calculate unpaid leave deduction based on retiral type
                    const isFixedRetiral = employeeRetiral.Retirals_Type?.toLowerCase() === 'fixed';

                    if (isFixedRetiral) {
                        // For fixed retirals: Adjust both employee and employer share amounts
                        const perDayEmployerRetiralWages = employerRetiralWages / actualWorkingDaysForTheEmployee;
                        adjustedEmployerRetiralWages = perDayEmployerRetiralWages * workedDaysMinusUnpaidLeaveDays;
                        const employeeShareAmountFixed = employeeRetiral.Employee_Share_Amount || 0;
                        const perDayEmployeeWages = employeeShareAmountFixed / actualWorkingDaysForTheEmployee;
                        const adjustedEmployeeAmount = perDayEmployeeWages * workedDaysMinusUnpaidLeaveDays;
                        retiralsConfig.Employee_Share_Amount = adjustedEmployeeAmount;

                        const employerShareAmountFixed = employeeRetiral.Employer_Share_Amount || 0;
                        const perDayEmployerWages = employerShareAmountFixed / actualWorkingDaysForTheEmployee;

                        const adjustedEmployerAmount = perDayEmployerWages * workedDaysMinusUnpaidLeaveDays;
                        retiralsConfig.Employer_Share_Amount = adjustedEmployerAmount;

                        // Calculate deduction for employer share
                        unpaidLeaveDeduction = employerRetiralWages-adjustedEmployerRetiralWages ;
                    } else {
                        // For percentage-based retirals: Adjust the retiral wages and share amounts proportionally
                        // Adjust employer retiral wages
                        const perDayEmployerRetiralWages = employerRetiralWages / actualWorkingDaysForTheEmployee;
                        adjustedEmployerRetiralWages = perDayEmployerRetiralWages * workedDaysMinusUnpaidLeaveDays;

                        // Adjust employee retiral wages
                        const perDayEmployeeRetiralWages = employeeRetiralWages / actualWorkingDaysForTheEmployee;
                        adjustedEmployeeRetiralWages = perDayEmployeeRetiralWages * workedDaysMinusUnpaidLeaveDays;

                        // Calculate adjusted employer share amount based on adjusted employer wages
                        const employerShareAmount = employeeRetiral.Employer_Share_Amount || 0;
                        const perDayEmployerShare = employerShareAmount / actualWorkingDaysForTheEmployee;
                        const adjustedEmployerAmount = perDayEmployerShare * workedDaysMinusUnpaidLeaveDays;
                        retiralsConfig.Employer_Share_Amount = adjustedEmployerAmount;

                        // Calculate adjusted employee share amount based on adjusted employee wages
                        const employeeShareAmount = employeeRetiral.Employee_Share_Amount || 0;
                        const perDayEmployeeShare = employeeShareAmount / actualWorkingDaysForTheEmployee;
                        const adjustedEmployeeAmountCalc = perDayEmployeeShare * workedDaysMinusUnpaidLeaveDays;
                        retiralsConfig.Employee_Share_Amount = adjustedEmployeeAmountCalc;

                        // Calculate deduction for employer share
                        unpaidLeaveDeduction = employerRetiralWages - adjustedEmployerRetiralWages;
                    }
                }

                // Apply round-off to employee and employer share amounts
                // Use retiralFormId if provided, otherwise fallback to formId.npsId (126)
                const roundOffFormId = retiralFormId || formId.npsId;
                const roundedEmployeeShareAmount = getRoundOffValue(roundOffFormId, retiralsConfig.Employee_Share_Amount, roundOffSettings);
                const roundedEmployerShareAmount = getRoundOffValue(roundOffFormId, retiralsConfig.Employer_Share_Amount, roundOffSettings);

                // Use employee_salary_retirals data directly (already adjusted if unpaid leaves exist)
                const retiralCalculationResult = {
                    Employee_Share_Amount: roundedEmployeeShareAmount,
                    Employer_Share_Amount: roundedEmployerShareAmount,
                    Employee_Share_Percentage: retiralsConfig.Employee_Share_Percentage,
                    Employer_Share_Percentage: retiralsConfig.Employer_Share_Percentage,
                    Retiral_Type: retiralsConfig.Retirals_Type,
                    Retirals_Id: retiralsConfig.Retirals_Id
                };

                // Get previous months retiral deductions from pre-fetched map
                const previousMonthsEmployerShare = previousRetiralsDeductionsMap.get(employeeId) || 0;

                // Calculate future months employer share by multiplying current month's employer share
                const nextMonthsEmployerShare = getRoundOffValue(roundOffFormId, retiralCalculationResult.Employer_Share_Amount * remainingMonths, roundOffSettings);

                // Calculate total amount for tax declaration (only for NPS)
                let totalNPSAmount = 0;
                let taxDeclarationData = null;
                if (retiralFormId === formId.npsId) {
                    totalNPSAmount = getRoundOffValue(roundOffFormId, previousMonthsEmployerShare + retiralCalculationResult.Employer_Share_Amount + nextMonthsEmployerShare, roundOffSettings);
                    unpaidLeaveDeduction = getRoundOffValue(roundOffFormId, unpaidLeaveDeduction, roundOffSettings);
                    // Prepare tax declaration data for response (don't insert)
                    lastDeclarationId = lastDeclarationId + 1;
                    const currentDateTime = new Date();

                    taxDeclarationData = {
                        Declaration_Id: lastDeclarationId,
                        Employee_Id: employeeId,
                        Investment_Cat_Id: npsInvestmentCategory.Investment_Cat_Id,
                        Age_Group_Id: null,
                        Amount_Declared: totalNPSAmount,
                        Amount_Approved: totalNPSAmount,
                        Assessment_Year: orgDetails.Assessment_Year,
                        Approver_Id: 1,
                        Submission_Date: currentDateTime,
                        Added_By: context.employeeId || 1,
                        Added_On: currentDateTime,
                        Approval_Status: 'Approved',
                        Lock_Flag: 0,
                        Approved_By: 1,
                        Approved_On: currentDateTime
                    };
                }

                results.push({
                    employeeId: employeeId,
                    success: true,
                    errorMessage: null,
                    npsDetails: {
                        originalNpsWages: employerRetiralWages,
                        unpaidLeaveDays: unpaidLeaveDays,
                        unpaidLeaveDeduction: unpaidLeaveDeduction,
                        adjustedNpsWages: adjustedEmployerRetiralWages,
                        employeeShareAmount: retiralCalculationResult.Employee_Share_Amount,
                        employerShareAmount: retiralCalculationResult.Employer_Share_Amount,
                        employeeSharePercentage: retiralCalculationResult.Employee_Share_Percentage,
                        employerSharePercentage: retiralCalculationResult.Employer_Share_Percentage,
                        retiralType: retiralCalculationResult.Retiral_Type,
                        retiralsId: retiralCalculationResult.Retirals_Id,
                        previousMonthsEmployerShare: previousMonthsEmployerShare,
                        nextMonthsEmployerShare: nextMonthsEmployerShare,
                        totalNPSAmount: totalNPSAmount,
                        declarationId: retiralFormId === formId.npsId ? lastDeclarationId : null,
                        taxDeclaration: taxDeclarationData
                    }
                });

            } catch (empError) {
                console.error(`Error processing employee ${employeeId}:`, empError);
                results.push({
                    employeeId: employeeId,
                    success: false,
                    errorMessage: empError.message || 'Error calculating retiral for employee',
                    npsDetails: null
                });
            }
        }

        return {
            errorCode: '',
            message: 'Payslip retiral details calculation completed successfully',
            results: JSON.stringify(results)
        };

    } catch (error) {
        console.error('Error in calculatePayslipRetiralDetails function main catch block:', error);
        const errResult = commonLib.func.getError(error, 'PFF0018');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
};

/**
 * Get organization details including fiscal year information
 */
async function getOrganizationDetails(organizationDbConnection, orgCode) {
    try {
        const query = organizationDbConnection(ehrTables.orgDetails)
            .select('Fiscal_StartMonth', 'Assessment_Year', 'Org_Code');

        if (orgCode) {
            query.where('Org_Code', orgCode);
        }

        const orgDetails = await query.first();
        return orgDetails || {};
    } catch (error) {
        console.error('Error in getOrganizationDetails:', error);
        throw error;
    }
}

/**
 * Calculate remaining months from given salary month to fiscal year end
 */
async function calculateRemainingFiscalMonths(organizationDbConnection, orgCode, orgDetails, salaryMonth) {
    try {
        if (!orgDetails?.Fiscal_StartMonth || !orgDetails?.Assessment_Year) {
            return 0;
        }

        // Get fiscal month array for current assessment year
        const effectiveOrgCode = orgCode || orgDetails.Org_Code;
        const fiscalMonthArray = await getFiscalMonthYear(
            organizationDbConnection,
            effectiveOrgCode,
            orgDetails.Assessment_Year
        );

        if (!fiscalMonthArray?.length) {
            return 0;
        }
        // Find index of salary month in fiscal array
        let currentIndex = fiscalMonthArray.findIndex(monthStr => monthStr === salaryMonth);

        if (currentIndex === -1) {
            return 0;
        }
        currentIndex++;
        // Calculate remaining months (excluding current month)
        return fiscalMonthArray.length - currentIndex;
    } catch (error) {
        console.error('Error in calculateRemainingFiscalMonths:', error);
        return 0;
    }
}

/**
 * Get previous retiral deductions for all employees in batch (current fiscal year up to but not including salary month)
 * Returns a Map with employeeId as key and total retiral deduction as value
 * For NPS: Gets from emp_etf_payment.Org_ShareAmount
 * For others: Gets from salary_deduction.Deduction_Amount
 */
async function getPreviousMonthsRetiralsDeductions(organizationDbConnection, employeeIds, orgDetails, orgCode, retiralFormId, currentSalaryDate) {
    try {
        const resultMap = new Map();
        employeeIds.forEach(empId => resultMap.set(empId, 0));

        if (!orgDetails?.Fiscal_StartMonth || !orgDetails?.Assessment_Year) {
            return resultMap;
        }

        // Get fiscal month array for current assessment year
        const effectiveOrgCode = orgCode || orgDetails.Org_Code;
        const fiscalMonthArray = await getFiscalMonthYear(
            organizationDbConnection,
            effectiveOrgCode,
            orgDetails.Assessment_Year
        );

        if (!fiscalMonthArray?.length) {
            return resultMap;
        }

        // Get first month from fiscal array as start date
        const [fiscalMonth, fiscalYear] = fiscalMonthArray[0].split(',');

        // Get Salary_Date for fiscal year start month using getSalaryDay
        const { Salary_Date: fiscalStartDate } = await commonLib.func.getSalaryDay(
            effectiveOrgCode,
            organizationDbConnection,
            fiscalMonth,
            fiscalYear
        );

        // For NPS: Get from emp_etf_payment and emp_tds_history tables
        if (retiralFormId === formId.npsId) {
            const [npsPayments, tdsHistory] = await Promise.all([
                // Get from emp_etf_payment table (uses Salary_Month column - convert to date for comparison)
                organizationDbConnection(ehrTables.empEtfPayment + ' as EP')
                    .select('EP.Employee_Id',
                            organizationDbConnection.raw('SUM(EP.Org_ShareAmount) as Total_Org_Share'))
                    .whereIn('EP.Employee_Id', employeeIds)
                    .whereRaw('STR_TO_DATE(EP.Salary_Month, "%m,%Y") >= ?', [fiscalStartDate])
                    .whereRaw('STR_TO_DATE(EP.Salary_Month, "%m,%Y") < ?', [currentSalaryDate])
                    .groupBy('EP.Employee_Id'),

                // Get from emp_tds_history table (uses From_Date and To_Date columns)
                organizationDbConnection(ehrTables.empTdsHistory + ' as ETH')
                    .select('ETH.Employee_Id',
                            organizationDbConnection.raw('SUM(ETH.Total_Employer_NPS_Contribution) as Total_NPS_Contribution'))
                    .whereIn('ETH.Employee_Id', employeeIds)
                    .where('ETH.From_Date', '>=', fiscalStartDate)
                    .where('ETH.To_Date', '<', currentSalaryDate)
                    .groupBy('ETH.Employee_Id')
            ]);

            // Map the results from emp_etf_payment
            npsPayments.forEach(payment => {
                const currentTotal = resultMap.get(payment.Employee_Id) || 0;
                resultMap.set(payment.Employee_Id, currentTotal + parseFloat(payment.Total_Org_Share || 0));
            });

            // Add the results from emp_tds_history
            tdsHistory.forEach(history => {
                const currentTotal = resultMap.get(history.Employee_Id) || 0;
                resultMap.set(history.Employee_Id, currentTotal + parseFloat(history.Total_NPS_Contribution || 0));
            });

        } else {
            // For other retirals: Get from salary_deduction table using JOIN (uses Salary_Month column - convert to date for comparison)
            const payslips = await organizationDbConnection(ehrTables.salaryPayslip + ' as SP')
                .select('SP.Employee_Id',
                        organizationDbConnection.raw('SUM(SD.Deduction_Amount) as Total_Deduction'))
                .innerJoin(ehrTables.salaryDeduction + ' as SD', 'SD.Payslip_Id', 'SP.Payslip_Id')
                .whereIn('SP.Employee_Id', employeeIds)
                .whereRaw('STR_TO_DATE(SP.Salary_Month, "%m,%Y") >= ?', [fiscalStartDate])
                .whereRaw('STR_TO_DATE(SP.Salary_Month, "%m,%Y") < ?', [currentSalaryDate])
                .where('SD.Form_Id', retiralFormId)
                .groupBy('SP.Employee_Id');

            // Map the results
            payslips.forEach(payslip => {
                resultMap.set(payslip.Employee_Id, parseFloat(payslip.Total_Deduction || 0));
            });
        }

        return resultMap;
    } catch (error) {
        console.error('Error in getPreviousMonthsRetiralsDeductions:', error);
        const errorMap = new Map();
        employeeIds.forEach(empId => errorMap.set(empId, 0));
        return errorMap;
    }
}

/**
 * Get employee retiral configuration details based on formId
 */
async function getEmployeeRetiralDetails(organizationDbConnection, employeeIds, retiralFormId) {
    try {
        const retiralDetails = await organizationDbConnection(ehrTables.employeeSalaryRetirals)
            .select('*')
            .whereIn('Employee_Id', employeeIds)
            .where('Form_Id', retiralFormId);

        return retiralDetails || [];
    } catch (error) {
        console.error('Error in getEmployeeRetiralDetails:', error);
        throw error;
    }
}

/**
 * Get NPS Investment Category for tax declaration (Form_Id=126, Tax_Status='Active')
 */
async function getNPSInvestmentCategory(organizationDbConnection, assessmentYear, retiralFormId) {
    try {
        const category = await organizationDbConnection('section_investment_category')
            .select('Investment_Cat_Id', 'Investment_Category', 'Tax_Section_ID')
            .where('Form_Id', retiralFormId)
            .where('Tax_Status', 'Active')
            .where('Assessment_Year', assessmentYear)
            .first();

        return category || null;
    } catch (error) {
        console.error('Error in getNPSInvestmentCategory:', error);
        throw error;
    }
}

/**
 * Get last Declaration_Id from tax_declaration table
 */
async function getLastDeclarationId(organizationDbConnection) {
    try {
        const result = await organizationDbConnection('tax_declaration')
            .max('Declaration_Id as maxDeclarationId')
            .first();

        return result?.maxDeclarationId || 0;
    } catch (error) {
        console.error('Error in getLastDeclarationId:', error);
        throw error;
    }
}

