// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError } = require('apollo-server-lambda');
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');

// resolver definition
const resolvers = {
    Query: {
        // function to list formula functions for formula building
        listFormulaFunctions: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside listFormulaFunctions function');
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Validate formId parameter
                if (!args.formId) {
                    throw 'IVE0001';
                }

                // Check access rights
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );

                if (Object.keys(checkRights).length === 0) {
                    throw '_DB0100';
                }

                // get formula functions
                const functions = await getFormulaFunctions(organizationDbConnection);

                return {
                    errorCode: '',
                    message: 'Formula functions retrieved successfully.',
                    success: true,
                    data: functions
                };

            } catch (mainCatchError) {
                console.log('Error in listFormulaFunctions function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0028');
                throw new ApolloError(errResult.message, errResult.code);
            } finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

/**
 * Get formula functions for formula building
 * @param {Object} organizationDbConnection - Database connection object
 * @returns {Promise<Array>} - Promise resolving to flat list of functions
 */
async function getFormulaFunctions(organizationDbConnection) {
    try {
        // Get all functions from functions_master table
        const formulaFunctions = await organizationDbConnection('functions_master as FM')
            .select(
                'FM.Function_Id',
                'FM.Name',
                'FM.Category',
                'FM.Min_Params',
                'FM.Max_Params',
                'FM.Syntax',
                'FM.Description',
                'FM.Example'
            )
            .orderBy('FM.Name', 'asc');

        // Return flat list without categorization
        const functions = formulaFunctions.map(func => ({
            functionId: func.Function_Id,
            name: func.Name,
            category: func.Category,
            minParams: func.Min_Params,
            maxParams: func.Max_Params,
            syntax: func.Syntax,
            description: func.Description,
            example: func.Example
        }));

        return functions;

    } catch (error) {
        console.error('Error in getFormulaFunctions:', error);
        throw error;
    }
}

exports.resolvers = resolvers;
