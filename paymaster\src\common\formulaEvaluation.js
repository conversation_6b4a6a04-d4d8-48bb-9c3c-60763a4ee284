// Formula Evaluation Engine for Salary Calculations
// This module handles the evaluation of custom formulas in allowance calculations

/**
 * Evaluates a custom formula by substituting component values and calculating the result
 * @param {string} formula - The custom formula to evaluate
 * @param {Object} componentValues - Object containing component codes as keys and their values
 * @param {Object} organizationDbConnection - Database connection for additional data if needed
 * @param {boolean} throwOnError - If true, throw errors instead of returning 0 (for calculateOneTimeEarningAmount)
 * @returns {Object} - { success: boolean, result: number, error: string }
 */
async function evaluateFormula(formula, componentValues, organizationDbConnection = null, throwOnError = false) {
    try {


        // Step 1: Input validation
        if (!formula || formula.trim().length === 0) {
            return {
                success: false,
                result: 0,
                error: 'Formula is empty or null'
            };
        }

        if (!componentValues || typeof componentValues !== 'object') {
            return {
                success: false,
                result: 0,
                error: 'Component values object is required'
            };
        }

        // Step 1.5: Handle Excel-style formulas (remove leading =)
        let cleanFormula = formula.trim();
        if (cleanFormula.startsWith('=')) {
            cleanFormula = cleanFormula.substring(1).trim();
        }

        // Step 2: Substitute component values in the formula
        const substitutedFormula = substituteComponentValues(cleanFormula, componentValues);

        // Step 3: Handle function calls (if any) - this includes IF statements
        const processedFormula = await processFunctionCalls(substitutedFormula, organizationDbConnection);
        // Step 4: Evaluate the mathematical expression
        const result = evaluateMathExpression(processedFormula, throwOnError);

        // Check if result is an error object (when throwOnError=true)
        if(result && typeof result === 'object' && result.error === true){
            return {
                success: false,
                result: 0,
                error: result.message,
                missingComponents: result.missingComponents
            };
        }

        return {
            success: true,
            result: result,
            error: null
        };

    } catch (error) {
        console.error('Formula evaluation error:', error);
        return {
            success: false,
            result: 0,
            error: error.message || 'Unknown error during formula evaluation'
        };
    }
}

/**
 * Substitutes component codes with their actual values in the formula
 * @param {string} formula - The formula containing component codes
 * @param {Object} componentValues - Object with component codes as keys and values
 * @returns {string} - Formula with substituted values
 */
function substituteComponentValues(formula, componentValues) {
    let substitutedFormula = formula;

    // Handle component aliases (fixed_pay_amount -> gross_inclusive_of_retirals)
    const componentAliases = {
        'fixed_pay_amount': 'gross_inclusive_of_retirals'
    };

    // Apply aliases to formula
    for (const [alias, actualComponent] of Object.entries(componentAliases)) {
        if (substitutedFormula.includes(alias) && componentValues[actualComponent] !== undefined) {
            const regex = new RegExp(`\\b${alias}\\b`, 'g');
            substitutedFormula = substitutedFormula.replace(regex, actualComponent);
        }
    }

    // Sort component codes by length (descending) to handle longer codes first
    // This prevents partial matches (e.g., 'basic' matching in 'basic_salary_amount')
    const sortedComponents = Object.keys(componentValues).sort((a, b) => b.length - a.length);

    // Available components for substitution: sortedComponents

    for (const componentCode of sortedComponents) {
        const value = componentValues[componentCode];

        // Substitute component values in formula

        // Create a regex that matches the component code exactly
        // Handle underscores properly by using explicit boundary checking
        const escapedComponentCode = componentCode.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

        // Split formula by the component code and check boundaries manually
        const parts = substitutedFormula.split(new RegExp(`(${escapedComponentCode})`, 'g'));
        let newFormula = '';

        for (let i = 0; i < parts.length; i++) {
            if (parts[i] === componentCode) {
                // Check if this is a whole word (not part of another identifier)
                const prevChar = i > 0 ? parts[i-1].slice(-1) : '';
                const nextChar = i < parts.length - 1 ? parts[i+1].charAt(0) : '';

                const isWholeWord = !/[a-zA-Z0-9_]/.test(prevChar) && !/[a-zA-Z0-9_]/.test(nextChar);

                if (isWholeWord) {
                    // CRITICAL: Check if value is valid before substitution
                    if (value === undefined || value === null || isNaN(value)) {
                        newFormula += 'UNRESOLVED_' + componentCode; // Mark as unresolved
                    } else {
                        newFormula += value.toString();
                    }
                } else {
                    newFormula += parts[i];
                }
            } else {
                newFormula += parts[i];
            }
        }

        substitutedFormula = newFormula;
        // Component substitution completed
    }


    return substitutedFormula;
}

/**
 * Processes function calls in the formula (e.g., max, min, round, etc.)
 * @param {string} formula - Formula that may contain function calls
 * @param {Object} organizationDbConnection - Database connection for function data
 * @returns {string} - Formula with function calls evaluated
 */
async function processFunctionCalls(formula, organizationDbConnection) {
    // For now, we'll handle basic mathematical functions
    // This can be extended to handle more complex functions from the database

    let processedFormula = formula;



    // Handle IF statements first (most complex)
    processedFormula = processIFStatements(processedFormula);

    // Handle common mathematical functions
    // Note: We'll use JavaScript's built-in Math functions for safety

    // Handle max function: max(a, b, c) -> Math.max(a, b, c) (case insensitive)
    processedFormula = processedFormula.replace(/\bmax\s*\(/gi, 'Math.max(');
    processedFormula = processedFormula.replace(/\bMAX\s*\(/g, 'Math.max(');

    // Handle min function: min(a, b, c) -> Math.min(a, b, c) (case insensitive)
    processedFormula = processedFormula.replace(/\bmin\s*\(/gi, 'Math.min(');
    processedFormula = processedFormula.replace(/\bMIN\s*\(/g, 'Math.min(');

    // Handle round function: round(value, decimals) -> Math.round(value * 10^decimals) / 10^decimals
    // For now, we'll handle simple round(value) -> Math.round(value)
    processedFormula = processedFormula.replace(/\bround\s*\(/gi, 'Math.round(');
    processedFormula = processedFormula.replace(/\bROUND\s*\(/g, 'Math.round(');

    // Handle abs function: abs(value) -> Math.abs(value)
    processedFormula = processedFormula.replace(/\babs\s*\(/gi, 'Math.abs(');
    processedFormula = processedFormula.replace(/\bABS\s*\(/g, 'Math.abs(');

    // Handle floor function: floor(value) -> Math.floor(value)
    processedFormula = processedFormula.replace(/\bfloor\s*\(/gi, 'Math.floor(');
    processedFormula = processedFormula.replace(/\bFLOOR\s*\(/g, 'Math.floor(');

    // Handle ceil function: ceil(value) -> Math.ceil(value)
    processedFormula = processedFormula.replace(/\bceil\s*\(/gi, 'Math.ceil(');
    processedFormula = processedFormula.replace(/\bCEIL\s*\(/g, 'Math.ceil(');


    return processedFormula;
}

/**
 * Processes IF statements in the formula
 * IF(condition, value_if_true, value_if_false)
 * @param {string} formula - Formula containing IF statements
 * @returns {string} - Formula with IF statements evaluated
 */
function processIFStatements(formula) {


    let processedFormula = formula;

    // Find IF statements manually to handle nested parentheses correctly
    let startIndex = 0;
    while (true) {
        const ifIndex = processedFormula.indexOf('IF(', startIndex);
        if (ifIndex === -1) break;

        // Find the matching closing parenthesis
        let parenCount = 0;
        let endIndex = ifIndex + 3; // Start after 'IF('

        for (let i = ifIndex + 3; i < processedFormula.length; i++) {
            if (processedFormula[i] === '(') parenCount++;
            else if (processedFormula[i] === ')') {
                if (parenCount === 0) {
                    endIndex = i;
                    break;
                }
                parenCount--;
            }
        }

        const fullMatch = processedFormula.substring(ifIndex, endIndex + 1);
        const innerContent = processedFormula.substring(ifIndex + 3, endIndex);

        // Split by commas, but respect parentheses
        const parts = [];
        let currentPart = '';
        let parenLevel = 0;

        for (let i = 0; i < innerContent.length; i++) {
            const char = innerContent[i];
            if (char === '(') parenLevel++;
            else if (char === ')') parenLevel--;
            else if (char === ',' && parenLevel === 0) {
                parts.push(currentPart.trim());
                currentPart = '';
                continue;
            }
            currentPart += char;
        }
        parts.push(currentPart.trim());

        if (parts.length !== 3) {

            startIndex = endIndex + 1;
            continue;
        }

        const [condition, trueValue, falseValue] = parts;

        try {
            // Evaluate the condition safely
            const conditionResult = evaluateCondition(condition.trim());
            const result = conditionResult ? trueValue.trim() : falseValue.trim();



            processedFormula = processedFormula.replace(fullMatch, result);
            startIndex = ifIndex; // Restart search from current position
            break; // Process one IF at a time to avoid index issues
        } catch (error) {
            // If we can't evaluate the condition, keep the IF statement for later processing
            startIndex = endIndex + 1;
        }
    }

    return processedFormula;
}

/**
 * Safely evaluates a condition for IF statements
 * @param {string} condition - The condition to evaluate
 * @returns {boolean} - Result of the condition
 */
function evaluateCondition(condition) {
    // CRITICAL: Check if condition contains ANY unidentified values
    const variablePattern = /[a-z_]+_amount/i;
    const hasVariables = variablePattern.test(condition);

    // Check for NaN, undefined, null values that indicate unresolved substitutions
    const hasUnresolvedValues = /\b(NaN|undefined|null)\b/i.test(condition);

    // Check for UNRESOLVED markers from substitution
    const hasUnresolvedMarkers = /UNRESOLVED_/i.test(condition);

    if (hasVariables || hasUnresolvedValues || hasUnresolvedMarkers) {
        return false;
    }

    // Security check for condition - allow comparison operators and numbers
    const safeConditionPattern = /^[0-9+\-*/.() \t\n<>=!]+$/;

    if (!safeConditionPattern.test(condition)) {
        return false;
    }

    try {
        // SECURE: Use safe expression evaluator instead of Function constructor
        const result = safeEvaluateExpression(condition);
        return Boolean(result);
    } catch (error) {
        return false;
    }
}

/**
 * Safely evaluates a mathematical expression
 * @param {string} expression - Mathematical expression to evaluate
 * @param {boolean} throwOnError - If true, throw errors for unresolved variables/IF statements
 * @returns {number} - Calculated result
 */
function evaluateMathExpression(expression, throwOnError = false) {


    // Check if expression still contains unresolved variables (component names)
    const variablePattern = /[a-z_]+_amount/gi;
    const matches = expression.match(variablePattern);
    if (matches && matches.length > 0) {
        if (throwOnError) {
            // Return object with error info including missing component(s)
            return {
                error: true,
                missingComponents: matches,
                message: `Formula contains unresolved component variable(s): ${matches.join(', ')}`
            };
        }
        return 0;
    }

    // Check if expression still contains unresolved IF statements
    const ifPattern = /IF\s*\(/i;
    if (ifPattern.test(expression)) {
        if (throwOnError) {
            throw new Error('Formula contains unresolved IF statements. Please check the IF statement syntax and ensure all conditions can be evaluated.');
        }
        return 0;
    }

    // Security check: Only allow safe mathematical operations and variable names
    const safePattern = /^[0-9a-z_+\-*/.() \t\nMath.maxminroundabsfloorceiling,]+$/i;

    if (!safePattern.test(expression)) {
        console.log('🚨 UNSAFE EXPRESSION:', expression);
        console.log('🔍 Failed pattern test:', safePattern);
        throw new Error('Formula contains unsafe characters or operations');
    }

    try {
        // SECURE: Use safe expression evaluator instead of Function constructor
        const result = safeEvaluateExpression(expression);

        // Ensure result is a valid number
        if (isNaN(result) || !isFinite(result)) {
            throw new Error('Formula evaluation resulted in invalid number');
        }

        return result;
    } catch (error) {
        console.error('Error evaluating mathematical expression:', error);
        throw new Error(`Mathematical evaluation failed: ${error.message}`);
    }
}

/**
 * SECURE: Safe expression evaluator that prevents code injection
 * Supports mathematical operations and comparisons without using eval() or Function()
 * @param {string} expression - Mathematical expression to evaluate
 * @returns {number|boolean} - Calculated result
 */
function safeEvaluateExpression(expression) {
    // Remove whitespace
    const cleanExpr = expression.replace(/\s+/g, '');

    // Handle comparison operations first (for conditions)
    const comparisonMatch = cleanExpr.match(/^(.+?)(>=|<=|>|<|==|!=)(.+?)$/);
    if (comparisonMatch) {
        const [, left, operator, right] = comparisonMatch;
        const leftValue = safeEvaluateMath(left);
        const rightValue = safeEvaluateMath(right);

        switch (operator) {
            case '>': return leftValue > rightValue;
            case '<': return leftValue < rightValue;
            case '>=': return leftValue >= rightValue;
            case '<=': return leftValue <= rightValue;
            case '==': return leftValue == rightValue;
            case '!=': return leftValue != rightValue;
            default: throw new Error(`Unsupported comparison operator: ${operator}`);
        }
    }

    // Handle mathematical expressions
    return safeEvaluateMath(cleanExpr);
}

/**
 * SECURE: Safe mathematical expression evaluator
 * @param {string} expr - Mathematical expression
 * @returns {number} - Calculated result
 */
function safeEvaluateMath(expr) {
    // Handle Math functions first (before removing spaces or processing parentheses)
    let changed = true;
    while (changed) {
        changed = false;

        // Math.max
        const maxMatch = expr.match(/Math\.max\(([^)]+)\)/);
        if (maxMatch) {
            const values = maxMatch[1].split(',').map(arg => safeEvaluateMath(arg.trim()));
            expr = expr.replace(maxMatch[0], Math.max(...values).toString());
            changed = true;
            continue;
        }

        // Math.min
        const minMatch = expr.match(/Math\.min\(([^)]+)\)/);
        if (minMatch) {
            const values = minMatch[1].split(',').map(arg => safeEvaluateMath(arg.trim()));
            expr = expr.replace(minMatch[0], Math.min(...values).toString());
            changed = true;
            continue;
        }

        // Math.round
        const roundMatch = expr.match(/Math\.round\(([^)]+)\)/);
        if (roundMatch) {
            const value = safeEvaluateMath(roundMatch[1].trim());
            expr = expr.replace(roundMatch[0], Math.round(value).toString());
            changed = true;
            continue;
        }

        // Math.abs
        const absMatch = expr.match(/Math\.abs\(([^)]+)\)/);
        if (absMatch) {
            const value = safeEvaluateMath(absMatch[1].trim());
            expr = expr.replace(absMatch[0], Math.abs(value).toString());
            changed = true;
            continue;
        }

        // Math.floor
        const floorMatch = expr.match(/Math\.floor\(([^)]+)\)/);
        if (floorMatch) {
            const value = safeEvaluateMath(floorMatch[1].trim());
            expr = expr.replace(floorMatch[0], Math.floor(value).toString());
            changed = true;
            continue;
        }

        // Math.ceil
        const ceilMatch = expr.match(/Math\.ceil\(([^)]+)\)/);
        if (ceilMatch) {
            const value = safeEvaluateMath(ceilMatch[1].trim());
            expr = expr.replace(ceilMatch[0], Math.ceil(value).toString());
            changed = true;
            continue;
        }
    }

    // Handle parentheses recursively (after Math functions are processed)
    while (expr.includes('(')) {
        const innerMatch = expr.match(/\(([^()]+)\)/);
        if (!innerMatch) break;

        const innerResult = safeEvaluateMath(innerMatch[1]);
        expr = expr.replace(innerMatch[0], innerResult.toString());
    }



    // Handle basic arithmetic operations in order of precedence
    // Division and multiplication first
    while (/[\*/]/.test(expr)) {
        const match = expr.match(/(-?\d+(?:\.\d+)?)\s*([\*/])\s*(-?\d+(?:\.\d+)?)/);
        if (!match) break;

        const [fullMatch, left, operator, right] = match;
        const leftNum = parseFloat(left);
        const rightNum = parseFloat(right);

        let result;
        if (operator === '*') {
            result = leftNum * rightNum;
        } else if (operator === '/') {
            if (rightNum === 0) throw new Error('Division by zero');
            result = leftNum / rightNum;
        }

        expr = expr.replace(fullMatch, result.toString());
    }

    // Addition and subtraction
    while (/[+-]/.test(expr.replace(/^-/, ''))) { // Ignore leading minus
        const match = expr.match(/(-?\d+(?:\.\d+)?)\s*([+-])\s*(-?\d+(?:\.\d+)?)/);
        if (!match) break;

        const [fullMatch, left, operator, right] = match;
        const leftNum = parseFloat(left);
        const rightNum = parseFloat(right);

        let result;
        if (operator === '+') {
            result = leftNum + rightNum;
        } else if (operator === '-') {
            result = leftNum - rightNum;
        }

        expr = expr.replace(fullMatch, result.toString());
    }

    // Return final number
    const result = parseFloat(expr);
    if (isNaN(result)) {
        throw new Error(`Invalid mathematical expression: ${expr}`);
    }

    return result;
}


module.exports = {
    evaluateFormula,
    substituteComponentValues,
    processFunctionCalls,
    evaluateMathExpression
};
