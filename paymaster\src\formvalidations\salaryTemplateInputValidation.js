// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common validation file
const validation = require('./validation');
// require plugin to validate uan
const { Validator } = require('format-utils');

module.exports = {
    salaryTemplateValidation : async(args,action,sourceForm=null,context) =>{
        //include validation library no need to use thi
        // variable declaration
        let validationError = {};
        let validate;

        // validate annual Ctc field
        if (args.annualCTC){
            let ctcValidation = validation.decimalValidation(args.annualCTC);
            if (!ctcValidation) {
                validationError['IVE0058'] = commonLib.func.getError('', 'IVE0058').message1;
            }
        }
        else{
            validationError['IVE0058'] = commonLib.func.getError('', 'IVE0058').message2;
        }

        if(!sourceForm){
            // If function is edit then we need to validate templateid
            if (action==='edit'){
                if (args.templateId){
                    validate = validation.onlyNumbers(args.templateId);
                    if (!validate) {
                        validationError['IVE0056'] = commonLib.func.getError('', 'IVE0056').message1;
                    }
                }
                else{
                    validationError['IVE0056'] = commonLib.func.getError('', 'IVE0056').message2;
                }  
            }

            // validate template name field
            if (args.templateName){
                let templateNameValidate = validation.alphaNumSpace(args.templateName);
                let lengthValidate = validation.checkLength(args.templateName, 1, 30);
                if (!templateNameValidate) {
                    validationError['IVE0057'] = commonLib.func.getError('', 'IVE0057').message1;
                }
                else if(!lengthValidate){
                    validationError['IVE0057'] = commonLib.func.getError('', 'IVE0057').message3;
                }
            }
            else{
                validationError['IVE0057'] = commonLib.func.getError('', 'IVE0057').message2;
            }

            // validate basic payType field
            if (args.basicPayType){
                let basicTypeValidate = validation.onlyAlphabet(args.basicPayType);
                let lengthValidate = validation.checkLength(args.basicPayType, 1, 10);
                if (!basicTypeValidate) {
                    validationError['IVE0059'] = commonLib.func.getError('', 'IVE0059').message1;
                }
                else if(!lengthValidate){
                    validationError['IVE0059'] = commonLib.func.getError('', 'IVE0059').message3;
                }
                else{
                    if(args.basicPayType.toLowerCase()==='percentage'){
                        if(args.percentage){
                            let percentageValidation = validation.percentageValidation(args.percentage);
                            if (!percentageValidation) {
                                validationError['IVE0060'] = commonLib.func.getError('', 'IVE0060').message1;
                            }
                        }
                        else{
                            validationError['IVE0060'] = commonLib.func.getError('', 'IVE0060').message2;
                        }
                    }
                    else{
                        if(args.amount){
                            let amountValidation = validation.decimalValidation(args.amount);
                            if (!amountValidation) {
                                validationError['IVE0061'] = commonLib.func.getError('', 'IVE0061').message1;
                            }
                        }
                        else{
                            validationError['IVE0061'] = commonLib.func.getError('', 'IVE0061').message2;
                        }
                    }
                }
            }
            else{
                validationError['IVE0059'] = commonLib.func.getError('', 'IVE0059').message2;
            }

            // validate description field
            if (args.description){
                let descriptionValidate = validation.alphaNumSpace(args.description);
                let lengthValidate = validation.checkLength(args.basicPayType, 1, 500);
                if (!descriptionValidate) {
                    validationError['IVE0062'] = commonLib.func.getError('', 'IVE0062').message1;
                }
                else if(!lengthValidate){
                    validationError['IVE0062'] = commonLib.func.getError('', 'IVE0062').message2;
                }
            }
        }
        else{
            if(args.basicPay){
                let basicPayValidation = validation.decimalValidation(args.basicPay);
                if (!basicPayValidation) {
                    validationError['IVE0086'] = commonLib.func.getError('', 'IVE0086').message;
                }
            }

            if(args.effectiveFrom ){
                let validate = await validation.effectiveDateValidation(args,action,context);
                if(validate=="IVE0087"){
                    validationError[validate] = commonLib.func.getError('', validate).message;
                }
            }
            if(args.annualGrossSalary){
                let annualGrossSalaryValidation = validation.decimalValidation(args.annualGrossSalary);
                if (!annualGrossSalaryValidation) {
                    validationError['IVE0088'] = commonLib.func.getError('', 'IVE0088').message;
                }
            }

            if(args.monthlyGrossSalary){
                let monthlyGrossSalaryValidation = validation.decimalValidation(args.monthlyGrossSalary);
                if (!monthlyGrossSalaryValidation) {
                    validationError['IVE0089'] = commonLib.func.getError('', 'IVE0089').message;
                }
            }
        }

        // validate allowance
        if(args.allowance.length>0){
            let allowanceData=args.allowance;
            for (let key of allowanceData){
                if(key.allowanceId){
                    validate = validation.onlyNumbers(key.allowanceId);
                    if (!validate) {
                        validationError['IVE0063'] = commonLib.func.getError('', 'IVE0063').message;
                    }    
                }
                if(key.allowanceType)
                {
                    let allowanceTypeValidate = validation.onlyAlphabet(key.allowanceType);
                    let lengthValidate = validation.checkLength(key.allowanceType, 1, 10);
                    if (!allowanceTypeValidate) {
                        validationError['IVE0064'] = commonLib.func.getError('', 'IVE0064').message1;
                    }
                    else if(!lengthValidate){
                        validationError['IVE0064'] = commonLib.func.getError('', 'IVE0064').message2;
                    }
                    else{
                        if(key.allowanceType.toLowerCase()==='percentage'){
                            if(key.percentage){
                                let percentageValidation = validation.percentageValidation(key.percentage);
                                if (!percentageValidation) {
                                    validationError['IVE0065'] = commonLib.func.getError('', 'IVE0065').message1;
                                }
                            }
                            else{
                                validationError['IVE0065'] = commonLib.func.getError('', 'IVE0065').message2;
                            }
                        }
                        else{
                            if(key.amount){
                                let amountValidation = validation.decimalValidation(key.amount);
                                if (!amountValidation) {
                                    validationError['IVE0066'] = commonLib.func.getError('', 'IVE0066').message1;
                                } 
                            }
                            else{
                                validationError['IVE0066'] = commonLib.func.getError('', 'IVE0066').message2;
                            } 
                        }
                    }
                }
            }
        }

        // validate retirals
        if(args.retirals.length>0){
            let retiralsData=args.retirals;
            for (let key of retiralsData){
                // validate retirals id
                if(key.retiralsId){
                    validate = validation.onlyNumbers(key.retiralsId);
                    if (!validate) {
                        validationError['IVE0067'] = commonLib.func.getError('', 'IVE0067').message;
                    }    
                }
                // validate formid field
                if(key.formId){
                    validate = validation.onlyNumbers(key.formId);
                    if (!validate) {
                        validationError['IVE0068'] = commonLib.func.getError('', 'IVE0068').message;
                    }    
                }
                // validate retirals type
                if(key.retiralsType)
                {
                    let retiralsTypeValidate = validation.onlyAlphabet(key.retiralsType);
                    let lengthValidate = validation.checkLength(key.retiralsType, 1, 10);
                    if (!retiralsTypeValidate) {
                        validationError['IVE0069'] = commonLib.func.getError('', 'IVE0069').message1;
                    }
                    else if(!lengthValidate){
                        validationError['IVE0069'] = commonLib.func.getError('', 'IVE0069').message2;
                    }
                    else{
                        if(key.retiralsType.toLowerCase()==='percentage'){
                            if(key.employerSharePercentage){
                                let percentageValidation = validation.percentageValidation(key.employerSharePercentage);
                                if (!percentageValidation) {
                                    validationError['IVE0070'] = commonLib.func.getError('', 'IVE0070').message1;
                                }
                                else{
                                    if(sourceForm){
                                        let percentageValidation = validation.percentageValidation(key.employeeSharePercentage);
                                        if (!percentageValidation) {
                                            validationError['IVE0090'] = commonLib.func.getError('', 'IVE0090').message;
                                        }        
                                    }
                                }
                            }
                            else{
                                validationError['IVE0070'] = commonLib.func.getError('', 'IVE0070').message2;
                            }
                        }
                        else{
                            if(key.employerShareAmount){
                                let amountValidation = validation.decimalValidation(key.employerShareAmount);
                                if (!amountValidation) {
                                    validationError['IVE0071'] = commonLib.func.getError('', 'IVE0071').message1;
                                } 
                                else{
                                    if(sourceForm){
                                        let amountValidation = validation.decimalValidation(key.employeeShareAmount);
                                        if (!amountValidation) {
                                            validationError['IVE0091'] = commonLib.func.getError('', 'IVE0091').message1;
                                        }         
                                    }
                                }
                            }
                        }
                    }
                }
                if(key.pfEmployerContribution){
                    let employerContributionValidation = validation.alphaNumSpace(key.pfEmployerContribution);
                    let lengthValidate = validation.checkLength(key.pfEmployerContribution, 1, 20);
                    if (!employerContributionValidation) {
                        validationError['IVE0072'] = commonLib.func.getError('', 'IVE0072').message1;
                    }
                    else if(!lengthValidate){
                        validationError['IVE0072'] = commonLib.func.getError('', 'IVE0072').message2;
                    }
                }
                // validate employer statutory limit
                if(key.employerStatutoryLimit){
                    let statutoryLimitValidation = validation.decimalValidation(key.employerStatutoryLimit);
                    if (!statutoryLimitValidation) {
                        validationError['IVE0073'] = commonLib.func.getError('', 'IVE0073').message;
                    }
                }
                // validate eligible for EPS
                if(key.eligibleForEPS){
                    if (!(key.eligibleForEPS===0 || key.eligibleForEPS===1)) {
                        validationError['IVE0074'] = commonLib.func.getError('', 'IVE0074').message;
                    }
                }
                // validate admin charge
                if(key.adminCharge){
                    let adminChargeValidation = validation.decimalValidation(key.adminCharge);
                    if (!adminChargeValidation) {
                        validationError['IVE0075'] = commonLib.func.getError('', 'IVE0075').message;
                    }
                }
                // validate edli charge
                if(key.edliCharge){
                    let edliChargeValidation = validation.decimalValidation(key.edliCharge);
                    if (!edliChargeValidation) {
                        validationError['IVE0076'] = commonLib.func.getError('', 'IVE0076').message;
                    }    
                }
                if(key.pfEmployeeContribution){
                    let lengthValidate = validation.checkLength(key.pfEmployeeContribution, 1, 20);
                    let employeeContributionValidation = validation.decimalValidation(key.pfEmployeeContribution);
                    if (!employeeContributionValidation) {
                        validationError['IVE0092'] = commonLib.func.getError('', 'IVE0092').message1;
                    }
                    else if(!lengthValidate){
                        validationError['IVE0092'] = commonLib.func.getError('', 'IVE0092').message2;
                    }    
                }
                // validate employee statutory limit
                if(key.employeeStatutoryLimit){
                    let statutoryLimitValidation = validation.decimalValidation(key.employeeStatutoryLimit);
                    if (!statutoryLimitValidation) {
                        validationError['IVE0093'] = commonLib.func.getError('', 'IVE0093').message;
                    }
                }  
            }
        }

        //validate bond
        if(args.bondRecoveryApplicable === 'Yes'){
            // validate minimumMonthsToBeServed
            if (args.minimumMonthsToBeServed){
                let minimumMonthsValidation = validation.checkMinMaxValue(args.minimumMonthsToBeServed, 1, 60);
                if (!minimumMonthsValidation) {
                    validationError['IVE0300'] = commonLib.func.getError('', 'IVE0300').message1;
                }
            }else{
                validationError['IVE0300'] = commonLib.func.getError('', 'IVE0300').message;
            }

            // validate bondValue
            if(args.bondValue){
                let bondValueValidation = validation.checkMinMaxValue(args.bondValue, 1.00, 99999999.00)
                if (!bondValueValidation) {
                    validationError['IVE0301'] = commonLib.func.getError('', 'IVE0301').message1;
                }
            }else{
                validationError['IVE0301'] = commonLib.func.getError('', 'IVE0301').message;
            }
        }
        return validationError;
    },
    // function to check whether template name already exist or not based on action
    checkTemplateNameExist : (organizationDbConnection,tableName,templateName,templateId,action) =>{
        try{
            // formation of sub query
            let subQuery= organizationDbConnection(tableName)
            .where('Template_Name',templateName)

            // if it is add function then check template name exist for any other record
            if(action==='add'){
                return (
                    subQuery
                    .then(templateNameExists => {
                        if(templateNameExists.length>0){
                            return 0;
                        }
                        else{
                            // if template name not exist then return 1
                            return 1;
                        }
                    })
                    .catch(function (catchError) {
                        console.log('Error in checkTemplateNameExist add function .catch block',catchError);
                        throw 'PST0007';
                    })
                );
            }
            else{
                // if it is edit function - check whether it is same templatename while adding 
                // else check whether template name exist or any other records
                return (
                    subQuery
                    .andWhere('Template_Id',templateId)
                    .then(templateNameExists => {
                        if(templateNameExists.length>0){
                            return 1;
                        }
                        else{
                            return (
                                subQuery
                                .then(templateNameExists => {
                                    if(templateNameExists.length>0){
                                        return 0;
                                    }
                                    else{
                                        return 1;
                                    }
                                })
                            )
                        }
                    })
                    .catch(function (catchError) {
                        console.log('Error in checkTemplateNameExist edit function .catch block',catchError);
                        throw 'PST0008';
                    })
                );
            }
        }
        catch(error){
            console.log('Error in checkTemplateNameExist main catch block',error);
            throw (action==='add')?'PST0007':'PST0008';
        }
    },
    salaryConfigurationValidation : (args)=>{
        // variable declaration
        let validationError = {};
        //validate uan
        if(args.UAN){
            let uanValidation = Validator.uan(args.UAN);
            if(!uanValidation){
                validationError['IVE0079'] = commonLib.func.getError('', 'IVE0079').message;
            }
        }
        // validate esi number
        if(args.ESINumber){
            let esiValidation = validation.onlyNumbers(args.ESINumber);
            let lengthValidate = validation.checkLength(args.ESINumber, 1, 30);
            if(!esiValidation){
                validationError['IVE0080'] = commonLib.func.getError('', 'IVE0080').message1;
            }
            else if(!lengthValidate){
                validationError['IVE0080'] = commonLib.func.getError('', 'IVE0080').message2;
            }
        }
        // validate pf number
        if(args.PfPolicyNo){
            let pfValidation = validation.alphaNumberSlash(args.PfPolicyNo);
            let lengthValidate = validation.checkLength(args.PfPolicyNo, 1, 30);
            if(!pfValidation){
                validationError['IVE0081'] = commonLib.func.getError('', 'IVE0081').message1;
            }
            else if(!lengthValidate){
                validationError['IVE0081'] = commonLib.func.getError('', 'IVE0081').message2;
            }
        }
        // validate nps number
        if(args.NpsNumber){
            let npsValidation = validation.onlyNumbers(args.NpsNumber);
            let lengthValidate = validation.checkLength(args.NpsNumber, 1, 30);
            if(!npsValidation){
                validationError['IVE0082'] = commonLib.func.getError('', 'IVE0082').message1;
            }
            else if(!lengthValidate){
                validationError['IVE0082'] = commonLib.func.getError('', 'IVE0082').message2;
            }
        }
        // validate eligible for PT
        if(typeof args.eligibleForPT == 'undefined'){
            validationError['IVE0171'] = commonLib.func.getError('', 'IVE0171').message;
        } else {
            if (!(args.eligibleForPT===0 || args.eligibleForPT===1)) {
                validationError['IVE0171'] = commonLib.func.getError('', 'IVE0171').message;
            }
        }
        // validate eligible for Pension
        if(typeof args.eligibleForPension == 'undefined'){
            validationError['IVE0172'] = commonLib.func.getError('', 'IVE0172').message;
        } else {
            if (!(args.eligibleForPension===0 || args.eligibleForPension===1)) {
                validationError['IVE0172'] = commonLib.func.getError('', 'IVE0172').message;
            }
        }

        if(args.eligibleForVpf)
        {
            if(!args.vpfEmployeeShare && !args.vpfEmployeeShareAmount)
            {
                validationError['IVE0292'] = commonLib.func.getError('', 'IVE0292').message;
            }
            
            if(args.vpfEmployeeShare && (args.vpfEmployeeShare<=0 || args.vpfEmployeeShare>100))
            {
                validationError['IVE0293'] = commonLib.func.getError('', 'IVE0293').message;
            }
            if(args.vpfEmployeeShareAmount && (args.vpfEmployeeShareAmount<=0 || args.vpfEmployeeShareAmount>100000))
            {
                validationError['IVE0294'] = commonLib.func.getError('', 'IVE0294').message;
            }
        }
        return validationError;
    }
};
