// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError } = require('apollo-server-lambda');
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');

// resolver definition
const resolvers = {
    Query: {
        // function to list formula operators for formula building
        listFormulaOperators: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside listFormulaOperators function');
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Validate formId parameter
                if (!args.formId) {
                    throw 'IVE0001';
                }

                // Check access rights
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );

                if (Object.keys(checkRights).length === 0) {
                    throw '_DB0100';
                }

                // get formula operators
                const operators = await getFormulaOperators(organizationDbConnection);

                return {
                    errorCode: '',
                    message: 'Formula operators retrieved successfully.',
                    success: true,
                    data: operators
                };

            } catch (mainCatchError) {
                console.log('Error in listFormulaOperators function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0029');
                throw new ApolloError(errResult.message, errResult.code);
            } finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

/**
 * Get formula operators for formula building
 * @param {Object} organizationDbConnection - Database connection object
 * @returns {Promise<Array>} - Promise resolving to flat list of operators
 */
async function getFormulaOperators(organizationDbConnection) {
    try {
        // Get all operators from operators_master table
        const formulaOperators = await organizationDbConnection('operators_master as OM')
            .select(
                'OM.Operator_Id',
                'OM.Symbol',
                'OM.Name',
                'OM.Return_Data_Type'
            )
            .orderBy('OM.Name', 'asc');

        // Return flat list without categorization
        const operators = formulaOperators.map(operator => ({
            operatorId: operator.Operator_Id,
            symbol: operator.Symbol,
            name: operator.Name,
            returnDataType: operator.Return_Data_Type
        }));

        return operators;

    } catch (error) {
        console.error('Error in getFormulaOperators:', error);
        throw error;
    }
}

exports.resolvers = resolvers;
