// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError } = require('apollo-server-lambda');
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const commonFunctions = require('../common/salaryTemplateCommonFunctions');
const { formId } = require('../common/appconstants');

// resolver definition
const resolvers = {
    Query: {
        // function to list salary template components
        listSalaryComponents: async (parent, args, context, info) => {
            let organizationDbConnection;
            try{
                console.log('Inside listSalaryComponents function');
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // check access right based on employeeid
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    206 // formId for salary template
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100';
                }

                // get allowance components
                const allowanceDetails = await getAllowanceComponents(organizationDbConnection);
                const allowanceLength = Object.keys(allowanceDetails).length;
                const fixedAllowanceArray = (allowanceLength > 0) ? allowanceDetails.fixedAllowanceArray : [];
                const flexiBenefitPlanArray = (allowanceLength > 0) ? allowanceDetails.flexiBenefitPlanArray : [];
                const reimbursementArray = (allowanceLength > 0) ? allowanceDetails.reimbursementArray : [];
                const bonusArray = (allowanceLength > 0) ? allowanceDetails.bonusArray : [];
                const allowanceArray = (allowanceLength > 0) ? allowanceDetails.allowanceArray : [];
                const basicPayArray = (allowanceLength > 0) ? allowanceDetails.basicPayArray : [];

                // get retiral components
                const [retiralDetails, grossConfiguration] = await Promise.all([
                    getRetiralComponents(organizationDbConnection),
                    getGrossConfiguration(organizationDbConnection)
                ]);

                // form output json response
                const salaryComponents = {
                    'allowances': {
                        fixedAllowanceArray,
                        flexiBenefitPlanArray,
                        reimbursementArray,
                        bonusArray,
                        allowanceArray,
                        basicPayArray
                    },
                    'retirals': retiralDetails,
                    'grossConfiguration': grossConfiguration
                };

                // return response
                return {
                    errorCode: '',
                    message: 'Salary components listed successfully.',
                    salaryComponents: JSON.stringify(salaryComponents)
                };
            }
            catch (mainCatchError){
                console.log('Error in listSalaryComponents function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0004');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally{
                // destroy database connection
                if (organizationDbConnection) organizationDbConnection.destroy();
            }
        }
    }
};

//Function to retrieve gross configuration details
async function getGrossConfiguration(organizationDbConnection) {
    try {
        const grossConfiguration = await organizationDbConnection(ehrTables.grossConfiguration + ' as GC')
            .select(
                'GC.Gross_Id',
                'GC.Salary_Component_Id',
                'GC.Gross_Name',
                'GC.Display_Name',
                'GC.Calculation_Type',
                'GC.Custom_Formula',
                'GC.Status',
                'GC.Description',
                'GC.Added_On',
                'GC.Added_By',
                'GC.Updated_On',
                'GC.Updated_By',
                'SC.Component_Name as Salary_Component_Name',
                'SC.Component_Code as Salary_Component_Code',
                'SC.Component_Type as Salary_Component_Type'
            )
            .leftJoin(ehrTables.salaryComponents + ' as SC', 'SC.Component_Id', 'GC.Salary_Component_Id')
            .where('GC.Status', 'Active')
            .orderBy('GC.Gross_Name', 'asc');

        return grossConfiguration;
    } catch (error) {
        console.log('Error in getGrossConfiguration function', error);
        return [];
    }
}

// function to get allowance components
async function getAllowanceComponents(organizationDbConnection) {
    // variable declarations
    let fixedAllowanceArray = [];
    let flexiBenefitPlanArray = [];
    let reimbursementArray = [];
    let allowanceArray = [];
    let bonusArray = [];
    let basicPayArray = [];

    try {
        return await organizationDbConnection.transaction(async (trx) => {
            // get the allowance components directly from allowance_type table (active status only)
            // Only include allowances that have a Salary_Component_Id
            const getDetails = await organizationDbConnection(ehrTables.allowanceType + ' as AT')
                .select(
                    'AT.*',
                    'AT.Allowance_Amount as Amount',
                    'AT.Allowance_Percentage as Percentage',
                    'AT.Allowance_Amount as Org_Amount',
                    'AT.Calculation_Type as Allowance_Type',
                    organizationDbConnection.raw('GROUP_CONCAT(BA.Form_Id) as Form_Id'),
                    'AT.FBP_Max_Declaration_Amount as FBP_Max_Declaration',
                    'AT.Allowance_Type_Id',
                    'SC.Component_Code as Salary_Component_Code',
                    'SC.Component_Name as Salary_Component_Name',
                    organizationDbConnection.raw('NULL as Allowance_Wages')
                )
                .leftJoin('allowance_type_benefit_association as BA', 'AT.Allowance_Type_Id', 'BA.Allowance_Type_Id')
                .leftJoin(ehrTables.ehrForms + ' as EF', 'EF.Form_Id', 'BA.Form_Id')
                .leftJoin('salary_components as SC', 'SC.Component_Id', 'AT.Salary_Component_Id')
                .where('AT.AllowanceType_Status', 'Active')
                .whereNotNull('AT.Salary_Component_Id') // Only include allowances with Salary_Component_Id
                .groupBy('AT.Allowance_Type_Id')
                .orderBy([
                    { column: 'AT.Allowance_Sequence', order: 'asc' },
                    { column: 'AT.Allowance_Name', order: 'asc' }
                ])
                .transacting(trx);

            if (getDetails.length > 0) {
                for (let allowanceData of getDetails) {
                    if(!allowanceData['Calculation_Type'] || allowanceData['Calculation_Type'] == '') {
                        continue;
                    }
                    const response = await commonFunctions.benefitAssociation(allowanceData);
                    
                    console.log('After benefitAssociation:', JSON.stringify({
                        Allowance_Type_Id: response.Allowance_Type_Id,
                        Allowance_Name: response.Allowance_Name,
                        Amount: response.Amount,
                        Allowance_Type: response.Allowance_Type,
                        Percentage: response.Percentage,
                        Org_Amount: response.Org_Amount,
                        Form_Id: response.Form_Id,
                        pfMapped: response.pfMapped,
                        variableInsuranceMapped: response.variableInsuranceMapped
                    }));
                    
                    const mappedData = mapAllowanceData(response);
                    
                    // Categorize based on allowance type properties
                    if (allowanceData['Salary_Component_Code']?.toLowerCase() === 'fixed_allowance_amount') {
                        fixedAllowanceArray.push(mappedData);
                    } else if (allowanceData['Is_Claim_From_Reimbursement'] && allowanceData['Is_Claim_From_Reimbursement'].toLowerCase() === 'yes') {
                        reimbursementArray.push(mappedData);
                    } else if (allowanceData['Is_Flexi_Benefit_Plan'] && allowanceData['Is_Flexi_Benefit_Plan'].toLowerCase() === 'yes') {
                        flexiBenefitPlanArray.push(mappedData);
                    } else if (allowanceData['Allowance_Mode'] && allowanceData['Allowance_Mode'].toLowerCase() === 'bonus') {
                        bonusArray.push(mappedData);
                    } else if (allowanceData['Allowance_Mode'] && allowanceData['Allowance_Mode'].toLowerCase() === 'non bonus' &&
                              allowanceData['Salary_Component_Code']?.toLowerCase() !== 'basic_salary_amount') {
                        allowanceArray.push(mappedData);
                    } else if (allowanceData['Salary_Component_Code']?.toLowerCase() === 'basic_salary_amount') {
                        basicPayArray.push(mappedData);
                    } else {
                        // Default to allowance array for any unmatched cases
                        allowanceArray.push(mappedData);
                    }
                }
                return {
                    fixedAllowanceArray,
                    flexiBenefitPlanArray,
                    reimbursementArray,
                    bonusArray,
                    allowanceArray,
                    basicPayArray
                };
            } else {
                return {};
            }
        });
    } catch (error) {
        console.log('Error in getAllowanceComponents function main catch block', error);
        return {};
    }
}

/**
 * Map allowance data to consistent format (same as listSalaryTemplateDetails.js)
 * @param {Object} response - Raw allowance data
 * @returns {Object} - Formatted allowance data
 */
function mapAllowanceData(response) {
    return {
        'Allowance_Type_Id': response['Allowance_Type_Id'],
        'Allowance_Name': response['Allowance_Name'],
        'Name_In_Payslip': response['Name_In_Payslip'],
        'Allowance_Type': response['Allowance_Type'],
        'Is_Basic_Pay': response['Is_Basic_Pay'], // Keep for backward compatibility
        'Component_Code': response['Salary_Component_Code'],
        'Component_Name': response['Salary_Component_Name'],
        'Salary_Component_Id': response['Salary_Component_Id'],
        'Period': response['Period'],
        'Amount': response['Amount'],
        'AllowanceWages': response['Allowance_Wages'],
        'Percentage': response['Percentage'],
        'orgAmount': response['Org_Amount'],
        'Custom_Formula': response['Custom_Formula'],
        'Formula_Based': response['Formula_Based'], // Keep for backward compatibility
        'Form_Id': response['Form_Id'],
        'Form_Name': response['Form_Name'],
        'pfMapped': response['pfMapped'],
        'variableInsuranceMapped': response['variableInsuranceMapped'],
        'fixedInsuranceMapped': response['fixedInsuranceMapped'],
        'gratuityMapped': response['gratuityMapped'],
        'npsMapped': response['npsMapped'],
        'Is_Flexi_Benefit_Plan': response['Is_Flexi_Benefit_Plan'],
        'Restrict_Employee_FBP_Override': response['Restrict_Employee_FBP_Override'],
        'FBP_Max_Declaration': response['FBP_Max_Declaration']
    };
}

// function to get retiral components
async function getRetiralComponents(organizationDbConnection) {
    try {
        // Get payroll general settings for slab-wise flags
        const payrollSettings = await organizationDbConnection('payroll_general_settings')
            .select('Slab_Wise_NPS', 'Slab_Wise_PF')
            .first();

        // Get insurance components from insurance_configuration table
        const insuranceDetails = await organizationDbConnection(ehrTables.insuranceConfiguration)
            .select('*')
            .where('InsuranceType_Status', 'Active');

        // Categorize insurance types
        const fixedInsurance = insuranceDetails.filter(item =>
            item.Insurance_Type?.toLowerCase() === 'fixed'
        );

        const variableInsurance = insuranceDetails.filter(item =>
            item.Insurance_Type?.toLowerCase() === 'variable' &&
            item.Employee_State_Insurance?.toLowerCase() !== 'yes'
        );

        const esiDetails = insuranceDetails.filter(item =>
            item.Employee_State_Insurance?.toLowerCase() === 'yes'
        );
        const slabWiseInsurance = insuranceDetails.filter(item =>
            item.Slab_Wise_Insurance?.toLowerCase() === 'yes'
        );
        // Get NPS details
        const npsDetails = await getNPSDetails(organizationDbConnection, payrollSettings);

        // Get Gratuity details
        const gratuityDetails = await getGratuityDetails(organizationDbConnection);

        // Get PF details
        const pfDetails = await getPFDetails(organizationDbConnection, payrollSettings);

        return {
            fixedInsurance: fixedInsurance.map(item => ({
                ...item,
                Form_Id: formId.insurance,
                Retiral_Type: item.Insurance_Type==='Fixed' ? 'Amount' : 'Percentage',
                Period: item.Payment_Frequency
            })),
            variableInsurance: variableInsurance.map(item => ({
                ...item,
                Form_Id: formId.insurance,
                Retiral_Type: item.Insurance_Type==='Fixed' ? 'Amount' : 'Percentage',
                Period: item.Payment_Frequency
            })),
            esiDetails: esiDetails.map(item => ({
                ...item,
                Form_Id: formId.insurance,
                Retiral_Type: item.Insurance_Type==='Fixed' ? 'Amount' : 'Percentage',
                Period: item.Payment_Frequency
            })),
            slabWiseInsurance: slabWiseInsurance.map(item => ({
                Form_Id: formId.insurance,
                Retiral_Type: item.Insurance_Type==='Fixed' ? 'Amount' : 'Percentage',
                ...item,
                Period: item.Payment_Frequency
            })),
            npsDetails,
            gratuityDetails,
            pfDetails
        };
    } catch (error) {
        console.log('Error in getRetiralComponents function:', error);
        return {};
    }
}

// function to get NPS details
async function getNPSDetails(organizationDbConnection, payrollSettings) {
    try {
        const npsResult = {
            Slab_Wise_NPS: payrollSettings?.Slab_Wise_NPS || 'No',
            Period: 'Monthly',
            Form_Id: formId.npsId,
            Retiral_Type: 'Percentage'
        };

        // If Slab_Wise_NPS is No, get details from nps_configuration
        if (payrollSettings?.Slab_Wise_NPS?.toLowerCase() === 'no') {
            const npsConfig = await organizationDbConnection('nps_configuration')
                .select('Calculation_Type', 'Employee_Share', 'Employer_Share', 'Employee_Share_Amount', 'Employer_Share_Amount')
                .first();
            const npsRules = await organizationDbConnection(ehrTables.npsRules)
                .select('Enable_Employee_To_Declare_Employer_Share')
                .first();
            if (npsConfig && (npsConfig.Employee_Share || npsConfig.Employer_Share || npsConfig.Employee_Share_Amount || npsConfig.Employer_Share_Amount)) {
                npsResult.Calculation_Type = npsConfig.Calculation_Type;
                npsResult.Retiral_Type = npsConfig.Calculation_Type.toLowerCase()==='amount' ? 'Fixed' : 'Percentage';
                npsResult.Employee_Share = npsConfig.Employee_Share;
                npsResult.Employer_Share = npsConfig.Employer_Share;
                npsResult.Employee_Share_Amount = npsConfig.Employee_Share_Amount;
                npsResult.Employer_Share_Amount = npsConfig.Employer_Share_Amount;
                npsResult.Enable_Employee_To_Declare_Employer_Share = npsRules?.Enable_Employee_To_Declare_Employer_Share;
            }
            else{
                return null;
            }
        }

        return [npsResult];
    } catch (error) {
        console.log('Error in getNPSDetails function:', error);
        return [];
    }
}

// function to get gratuity details
async function getGratuityDetails(organizationDbConnection) {
    try {
        const gratuityDetails = await organizationDbConnection(ehrTables.gratuitySettings)
            .select('Working_Days', 'Org_Salary_Days', 'Part_Of_GratuityAct')
            .where('Part_Of_GratuityAct', 1); // Only return if organization is part of Gratuity Act

        if (gratuityDetails.length > 0) {
            return gratuityDetails.map(item => ({
                ...item,
                Period: 'Annually',
                Form_Id: formId.gratuityId,
                Retiral_Type: 'Percentage'
            }));
        } else {
            return [];
        }
    } catch (error) {
        console.log('Error in getGratuityDetails function:', error);
        return [];
    }
}

// function to get PF details
async function getPFDetails(organizationDbConnection, payrollSettings) {
    try {
        const pfResult = {
            Slab_Wise_PF: payrollSettings?.Slab_Wise_PF || 'No',
            Period: 'Monthly',
            Form_Id: formId.pfId,
            Retiral_Type: 'Percentage'
        };

        // If Slab_Wise_PF is No, get details from provident_fund_settings and provident_fund
        if (payrollSettings?.Slab_Wise_PF?.toLowerCase() === 'no') {
            const pfSettings = await organizationDbConnection('provident_fund_settings')
                .select('Restricted_PF_Wage_Amount', 'Employee_Share', 'Employer_Share', 'Admin_Charge', 'Admin_Charge_Max_Amount', 'EDLI_Charge', 'EDLI_Charge_Max_Amount')
                .first();

            const pfConfig = await organizationDbConnection('provident_fund')
                .select('Employee_Contribution_Rate', 'Employer_Contribution_Rate','Override_PF_Contribution_Rate_At_Employee_Level', 'Admin_Charge_Part_Of_CTC', 'Edli_Charge_Part_Of_CTC')
                .first();

            if (pfSettings) {
                pfResult.Restricted_PF_Wage_Amount = pfSettings.Restricted_PF_Wage_Amount;
                pfResult.Employee_Share = pfSettings.Employee_Share;
                pfResult.Employer_Share = pfSettings.Employer_Share;
                pfResult.Admin_Charge = pfSettings.Admin_Charge;
                pfResult.Admin_Charge_Max_Amount = pfSettings.Admin_Charge_Max_Amount;
                pfResult.EDLI_Charge = pfSettings.EDLI_Charge;
                pfResult.EDLI_Charge_Max_Amount = pfSettings.EDLI_Charge_Max_Amount;
            }

            if (pfConfig && (pfConfig.Employee_Contribution_Rate || pfConfig.Employer_Contribution_Rate)) {
                pfResult.Employee_Contribution_Rate = pfConfig.Employee_Contribution_Rate;
                pfResult.Employer_Contribution_Rate = pfConfig.Employer_Contribution_Rate;
                pfResult.Override_PF_Contribution_Rate_At_Employee_Level = pfConfig.Override_PF_Contribution_Rate_At_Employee_Level;
                pfResult.Admin_Charge_Part_Of_CTC = pfConfig.Admin_Charge_Part_Of_CTC;
                pfResult.Edli_Charge_Part_Of_CTC = pfConfig.Edli_Charge_Part_Of_CTC;
            }
            else{
                return null;
            }
        }

        return [pfResult];
    } catch (error) {
        console.log('Error in getPFDetails function:', error);
        return [];
    }
}

exports.resolvers = resolvers;
