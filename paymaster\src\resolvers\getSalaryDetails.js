// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// Organization database connection
const knex = require('knex');
// require common constant files
const constants = require('../common/appconstants');
// require table alias
const {ehrTables} = require('../common/tablealias');
// require common function file
const commonFunctions=require('../common/salaryTemplateCommonFunctions');

// variable declarations
let errResult = {};
let organizationDbConnection,appmanagerDbConnection = '';
let checkRights=false;

// resolver definition
const resolvers = {
    Query: {
        // function to list salary template components
        getSalaryDetails: async (parent, args, context, info) => {
            try{
                console.log('Inside getSalaryDetails function');
                let loggedInEmpId=context.logInEmpId;
                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                //  get the appmanager data base connection
                appmanagerDbConnection = knex(context.connection.AppManagerDb);
                // check access right based on employeeid
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loggedInEmpId,constants.formName.salaryDetails,constants.roles.roleView);
                if (checkRights === true) {
                    return(
                        organizationDbConnection
                        .transaction(function(trx){
                            // based on employee id get the salary details
                            // Get the record with Effective_To as null.There will be multiple records for an employee. 
                            // There will be only one record with effective to as null
                            return(
                                organizationDbConnection(ehrTables.employeeSalaryDetails)
                                .select('Employee_Salary_Id','Template_Id','Basic_Pay','Effective_From','Annual_Ctc','Annual_Gross_Salary','Monthly_Gross_Salary')
                                .where('Employee_Id',args.employeeId)
                                .where('Effective_To',null)
                                .transacting(trx)
                                .then(async (salaryData) =>{
                                    // check data exist or not
                                    if (salaryData.length>0){
                                        let allowanceDetails= await getAllowanceComponents(organizationDbConnection,trx,salaryData[0].Employee_Salary_Id);
                                        let allowanceLength=Object.keys(allowanceDetails).length
                                        let fixedAllowanceArray=(allowanceLength>0)?allowanceDetails.fixedAllowanceArray:[];
                                        let flexiBenefitPlanArray=(allowanceLength>0)?allowanceDetails.flexiBenefitPlanArray:[];
                                        let reimbursementArray=(allowanceLength>0)?allowanceDetails.reimbursementArray:[];
                                        let bonusArray=(allowanceLength>0)?allowanceDetails.bonusArray:[];
                                        let allowanceArray=(allowanceLength>0)?allowanceDetails.allowanceArray:[];
                                        
                                        let retiralsDetails= await getRetiralsComponents(organizationDbConnection,trx,salaryData[0].Employee_Salary_Id);

                                        // get currency symbol
                                        // Return ₹, if the currency sybmol does not exists
                                        let currencySymbol=await commonLib.func.getCurrencySymbol(organizationDbConnection);

                                        let outputResponse={
                                            'allowances':{fixedAllowanceArray,flexiBenefitPlanArray,reimbursementArray,bonusArray,allowanceArray},
                                            'retirals'  :retiralsDetails,
                                            'salaryData':salaryData
                                        }
                                        return {errorCode:'',message:'Salary details retrieved successfully.',salaryDetails:JSON.stringify(outputResponse),currencySymbol:(currencySymbol)?currencySymbol:constants.defaultValues.currencySymbol};
                                    }
                                    else{
                                        return {errorCode:'',message:'No salary details exist for this employee.',salaryDetails:'',currencySymbol:''};
                                    }
                                })
                            )
                        })
                        .then(function (result) {
                            return result;
                        })
                        //catch db-connectivity errors
                        .catch(function (catchError) {
                            console.log('Error in getSalaryDetails function .catch() block', catchError);
                            errResult = commonLib.func.getError(catchError, 'PST0108');
                            throw new ApolloError(errResult.message,errResult.code);
                        })
                        /**close db connection */
                        .finally(() => {
                            organizationDbConnection.destroy();
                        })
                    )
                }
                else if (checkRights === false) {
                    throw '_DB0100';
                } else {
                    // throw error
                    throw (checkRights);
                }
            }
            catch (mainCatchError){
                console.log('Error in getSalaryDetails function main catch block',mainCatchError);
                // destroy database connection
                (organizationDbConnection)?organizationDbConnection.destroy():'';
                (appmanagerDbConnection)?appmanagerDbConnection.destroy():'';
                errResult = commonLib.func.getError(mainCatchError, 'PST0015');
                throw new ApolloError(errResult.message,errResult.code);
            }
        }
    }
}

// function to get allowance components
async function getAllowanceComponents(organizationDbConnection,trx,salaryId){
    try{
        // variable declarations
        let fixedAllowanceArray=[];
        let flexiBenefitPlanArray=[];
        let reimbursementArray=[];
        let allowanceArray=[];
        let bonusArray=[];
        return(
            // get the allowance details from employee salary allowance table
            organizationDbConnection(ehrTables.employeeSalaryAllowance)
            .select('Allowance_Id','Allowance_Type','Percentage','Amount')
            .where('Employee_Salary_Id',salaryId)
            .transacting(trx)
            .then(async (employeeAllowanceData) =>{
                // check data exist or not
                if (employeeAllowanceData.length>0){
                    // iterate the allowance details based on id
                    for (let empAllowance of employeeAllowanceData){
                        // based on allowance id get the allowance details which are in organization coverage
                       await organizationDbConnection(ehrTables.allowances + ' as A')
                        .select('A.Allowance_Type_Id','AT.Allowance_Mode','A.Percentage','A.Amount as Org_Amount','AT.Period','AT.Allowance_Name','AT.Name_In_Payslip',
                        'AT.Formula_Based','AT.Is_Flexi_Benefit_Plan','AT.Is_Claim_From_Reimbursement','BA.Form_Id')
                        .innerJoin(ehrTables.allowanceType + ' as AT', 'A.Allowance_Type_Id', 'AT.Allowance_Type_Id')
                        .leftJoin(ehrTables.allowanceBenefitAssociation + ' as BA','A.Allowance_Id','BA.Allowance_Type_Id')
                        .where('A.Coverage','ORG')
                        .where('A.Allowance_Status','Active')
                        .where('AT.AllowanceType_Status','Active')
                        .where('A.Allowance_Id',empAllowance.Allowance_Id)
                        .transacting(trx)
                        .then(async (allowanceData) =>{
                            // check data exist or not
                            if (allowanceData.length>0){
                                for (let key of allowanceData){
                                    // if the formula based is 1 then it will be come under fixed allowances
                                    if(parseInt(key['Formula_Based']) === 1){
                                        let response=await commonFunctions.benefitAssociation(key);
                                        fixedAllowanceArray.push({
                                            'Allowance_Id'   : empAllowance.Allowance_Id,
                                            'Allowance_Name' : key.Allowance_Name,
                                            'Allowance_Type' : empAllowance.Allowance_Type,
                                            'Period'         : key.Period,
                                            'Amount'         : empAllowance.Amount,
                                            'Percentage'     : empAllowance.Percentage,
                                            'orgAmount'      : key.Org_Amount,
                                            'pfMapped'       :response.pfMapped,
                                            'variableInsuranceMapped':response.variableInsuranceMapped,
                                            'fixedInsuranceMapped':response.fixedInsuranceMapped,
                                            'gratuityMapped':response.gratuityMapped,
                                            'npsMapped':response.npsMapped
                                        });
                                    }
                                    // if flexi benefit is enabled then it will be come under flexi benefit allowances
                                    else if(parseInt(key['Is_Flexi_Benefit_Plan']) === 1 &&
                                    parseInt(key['Is_Claim_From_Reimbursement']) === 0){
                                        let response=await commonFunctions.benefitAssociation(key);
                                        flexiBenefitPlanArray.push({
                                            'Allowance_Id'   : empAllowance.Allowance_Id,
                                            'Allowance_Name' : key.Allowance_Name,
                                            'Allowance_Type' : empAllowance.Allowance_Type,
                                            'Period'         : key.Period,
                                            'Amount'         : empAllowance.Amount,
                                            'Percentage'     : empAllowance.Percentage,
                                            'orgAmount'      : key.Org_Amount,
                                            'pfMapped'       :response.pfMapped,
                                            'variableInsuranceMapped':response.variableInsuranceMapped,
                                            'fixedInsuranceMapped':response.fixedInsuranceMapped,
                                            'gratuityMapped':response.gratuityMapped,
                                            'npsMapped':response.npsMapped
                                        });
                                    }
                                    // if both flexi benefit and reimbursement is enabled then it will come under reimbursement
                                    else if(parseInt(key['Is_Flexi_Benefit_Plan']) === 1 &&
                                    parseInt(key['Is_Claim_From_Reimbursement']) === 1){
                                        let response=await commonFunctions.benefitAssociation(key);
                                        reimbursementArray.push({
                                            'Allowance_Id'   : empAllowance.Allowance_Id,
                                            'Allowance_Name' : key.Allowance_Name,
                                            'Allowance_Type' : empAllowance.Allowance_Type,
                                            'Period'         : key.Period,
                                            'Amount'         : empAllowance.Amount,
                                            'Percentage'     : empAllowance.Percentage,
                                            'orgAmount'      : key.Org_Amount,
                                            'pfMapped'       :response.pfMapped,
                                            'variableInsuranceMapped':response.variableInsuranceMapped,
                                            'fixedInsuranceMapped':response.fixedInsuranceMapped,
                                            'gratuityMapped':response.gratuityMapped,
                                            'npsMapped':response.npsMapped
                                        });
                                    }
                                    // if the allowance mode is enable then it will come under bonus component
                                    else if(parseInt(key['Allowance_Mode']) === 1){
                                        let response=await commonFunctions.benefitAssociation(key);
                                        bonusArray.push({
                                            'Allowance_Id'   : empAllowance.Allowance_Id,
                                            'Allowance_Name' : key.Allowance_Name,
                                            'Allowance_Type' : empAllowance.Allowance_Type,
                                            'Period'         : key.Period,
                                            'Amount'         : empAllowance.Amount,
                                            'Percentage'     : empAllowance.Percentage,
                                            'orgAmount'      : key.Org_Amount,
                                            'pfMapped'       :response.pfMapped,
                                            'variableInsuranceMapped':response.variableInsuranceMapped,
                                            'fixedInsuranceMapped':response.fixedInsuranceMapped,
                                            'gratuityMapped':response.gratuityMapped,
                                            'npsMapped':response.npsMapped
                                        });
                                    }
                                    // else will be allowance component
                                    else{
                                        let response=await commonFunctions.benefitAssociation(key);
                                        allowanceArray.push({
                                            'Allowance_Id'   : empAllowance.Allowance_Id,
                                            'Allowance_Name' : key.Allowance_Name,
                                            'Allowance_Type' : empAllowance.Allowance_Type,
                                            'Period'         : key.Period,
                                            'Amount'         : empAllowance.Amount,
                                            'Percentage'     : empAllowance.Percentage,
                                            'orgAmount'      : key.Org_Amount,
                                            'pfMapped'       :response.pfMapped,
                                            'variableInsuranceMapped':response.variableInsuranceMapped,
                                            'fixedInsuranceMapped':response.fixedInsuranceMapped,
                                            'gratuityMapped':response.gratuityMapped,
                                            'npsMapped':response.npsMapped
                                        });
                                    }
                                }
                            }
                        })
                    }
                    let outputResponse={fixedAllowanceArray,flexiBenefitPlanArray,reimbursementArray,bonusArray,allowanceArray};
                    return outputResponse;
                }
                else{
                    console.log('No allowance record mapped to employee salary id');
                    return {};
                }
            })
            .catch(function (catchError) {
                console.log('Error in getAllowanceComponents function .catch() block', catchError);
                return {};
            })
        );
    }
    catch(allowanceError){
        console.log('Error in getAllowanceComponents function main catch block',allowanceError);
        return {};
    }
}

// function to get retiral components
async function getRetiralsComponents(organizationDbConnection,trx,salaryId){
    try{
        // variable declarations
        let retirals={};

        // get the retirals data based on employee salary id
        return(
            organizationDbConnection(ehrTables.employeeSalaryRetirals)
            .select('Form_Id','Retirals_Id','Retirals_Type','Retiral_Wages','Employee_Share_Percentage','Employer_Share_Percentage',
            'Employee_Share_Amount','Employer_Share_Amount','PF_Employee_Contribution','PF_Employer_Contribution',
            'Employee_Statutory_Limit','Employer_Statutory_Limit','Eligible_For_EPS','Admin_Charge','EDLI_Charge')
            .where('Employee_Salary_Id',salaryId)
            .transacting(trx)
            .then(async (retiralsData) =>{
                // check data exist or not
                if (retiralsData.length>0){
                    // iterate each retirals record
                    for (let key of retiralsData){
                        // if formid is 208 then it is fixed insurance
                        if(key.Form_Id===constants.formId.fixedInsuranceId){
                            let fixedInsuranceData=await commonFunctions.fixedInsuranceArray(organizationDbConnection,appmanagerDbConnection,'salary details',key);
                            retirals.fixedInsuranceDetails= fixedInsuranceData;
                        }
                        // if formid is 209 then it is variable insurance
                        else if(key.Form_Id===constants.formId.variableInsuranceId){
                            let variableInsuranceData=await commonFunctions.variableInsuranceArray(organizationDbConnection,appmanagerDbConnection,'salary details',key);
                            if(variableInsuranceData.esiArray)
                            {
                                retirals.esiDetails=variableInsuranceData;
                                retirals.variableInsuranceDetails={};
                            }
                            else{
                                retirals.variableInsuranceDetails= variableInsuranceData;
                                retirals.esiDetails={};
                            }
                        }
                        // if formid is 152 then it is fixed health insurance
                        else if(key.Form_Id===constants.formId.fixedHealthInsuranceId){
                            let fixedHealthInsuranceData=await commonFunctions.fixedHealthInsuranceArray(organizationDbConnection,appmanagerDbConnection,'salary details',key);
                            retirals.fixedHealthInsuranceDetails= fixedHealthInsuranceData;            
                        }
                        // if formid is 52 then it is pf
                        else if(key.Form_Id===constants.formId.pfId){
                            let pfJson=await commonFunctions.pfArray(organizationDbConnection,appmanagerDbConnection,'salary details',key);
                            retirals.pfDetails= pfJson;
                        }
                        // if formid is 126 then it is nps
                        else if(key.Form_Id===constants.formId.npsId){
                            let npsJson=await commonFunctions.npsArray(appmanagerDbConnection,key,'salary details');
                            retirals.npsDetails= npsJson;
                        }
                        // if formid is 110 then it is gratuity
                        else{
                            let gratuityJson=await commonFunctions.gratuityArray(appmanagerDbConnection,key,'salary details');
                            retirals.gratuityDetails= gratuityJson;
                        }
                    }
                    /* Push the retirals components associated */
                    return retirals;
                }
                else{
                    return {};
                }
            })
            .catch(function (catchError) {
                console.log('Error in getRetiralsComponents function .catch() block', catchError);
                return {};
            })
        )
    }
    catch(retiralsCatchError){
        console.log('Error in getRetiralsComponents main catch block',retiralsCatchError);
        return {};
    }
}
    
exports.resolvers = resolvers;
