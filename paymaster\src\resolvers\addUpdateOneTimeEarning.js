// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const { validateFormula } = require('../common/formulaValidation');
const { resolvers: calculateResolvers } = require('./calculateOneTimeEarningAmount');
const {
  getOneTimeEarningWorkflowProcessInstanceId,
  getEventId,
  deleteOldApprovalRecordsWithoutTrx
} = require('../common/commonfunctions');
const { ehrTables } = require('../common/tablealias');

// resolver definition
const resolvers = {
    Mutation: {
        // function to delete one time earning
        deleteOneTimeEarning: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Validate required inputs
                if (!args.oneTimeEarningId) {
                    throw 'IVE0770';
                }

                if (!args.formId) {
                    throw 'IVE0658';
                }

                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );
                if (Object.keys(checkRights).length === 0 || checkRights.Role_Delete !== 1) {
                    throw '_DB0103';
                }

                // Check if user is Admin (required for One Time Earnings)
                if (!checkRights.Employee_Role || checkRights.Employee_Role.toLowerCase() !== 'admin') {
                    console.log('Login employee does not have admin access', checkRights);
                    throw '_DB0114';
                }

                // Perform deletion with all business logic checks
                const result = await deleteOneTimeEarningWithChecks(
                    organizationDbConnection,
                    args.oneTimeEarningId
                );

                return result;
            }
            catch (mainCatchError) {
                console.log('Error in deleteOneTimeEarning function main catch block', mainCatchError);

                if (mainCatchError === 'IVE0770' || mainCatchError === 'IVE0771' || mainCatchError === 'IVE0772') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new UserInputError(errResult.message1, { errorCode: mainCatchError });
                } else if (mainCatchError === '_DB0100' || mainCatchError === '_DB0103' || mainCatchError === '_DB0114') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new ApolloError(errResult.message, errResult.code);
                } else {
                    const errResult = commonLib.func.getError(mainCatchError, 'PST0047');
                    throw new ApolloError(errResult.message, errResult.code);
                }
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        },

        // function to add/update one time earning
        addUpdateOneTimeEarning: async (parent, args, context, info) => {
            let organizationDbConnection;
            let validationError = {};
            
            try {
                console.log('Inside addUpdateOneTimeEarning function');
                
                const loggedInEmpId = context.logInEmpId;
                const orgCode = context.orgCode;
                const userIp = context.userIp;
                const isEditMode = args.oneTimeEarningId ? true : false;

                organizationDbConnection = knex(context.connection.OrganizationDb);

                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );

                if (isEditMode) {
                    if (Object.keys(checkRights).length === 0 || checkRights.Role_Update !== 1) {
                        throw '_DB0102';
                    }
                } else {
                    if (Object.keys(checkRights).length === 0 || checkRights.Role_Add !== 1) {
                        throw '_DB0101';
                    }
                }

                // Check if user is Admin (required for One Time Earnings)
                if (!checkRights.Employee_Role || checkRights.Employee_Role.toLowerCase() !== 'admin') {
                    console.log('Login employee does not have admin access', checkRights);
                    throw '_DB0114';
                }

                const currentTimestamp = moment().utc().format('YYYY-MM-DD HH:mm:ss');

                const orgDetails = await organizationDbConnection('org_details')
                    .select('Payroll_Period')
                    .first();

                const typeConfig = await organizationDbConnection(ehrTables.onetimeEarningTypes)
                    .where('One_Time_Earning_Type_Id', args.oneTimeEarningTypeId)
                    .where('Status', 'Active')
                    .first();

                if (!typeConfig) {
                    throw 'IVE0763';
                }

                if (args.customFormula && args.customFormula.trim().length > 0) {
                    const validation = await validateFormula(organizationDbConnection, args.customFormula);
                    if (!validation.isValid) {
                        throw validation.errors[0];
                    }
                }

                // Check override permission for clawback formula
                if (args.clawbackFormula && args.clawbackFormula.trim().length > 0) {
                    if (typeConfig.Override_Custom_Formula !== 'Yes') {
                        throw 'IVE0769'; // Override not allowed for clawback formula
                    }
                    // Validate clawback formula
                    const validation = await validateFormula(organizationDbConnection, args.clawbackFormula);
                    if (!validation.isValid) {
                        throw validation.errors[0];
                    }
                }

                // Validate formula amount if custom formula is provided
                if (args.customFormula && args.customFormula.trim().length > 0) {
                    // Call calculateOneTimeEarningAmount to get calculated amount
                    const calculationResult = await callCalculateOneTimeEarningAmount(
                        organizationDbConnection,
                        args.employeeId,
                        args.customFormula,
                        context
                    );

                    const calculatedAmount = parseFloat(calculationResult.amount).toFixed(2);
                    const providedAmount = parseFloat(args.amount).toFixed(2);

                    // Compare calculated amount with provided amount
                    if (calculatedAmount !== providedAmount) {
                        validationError['IVE0773'] = `Amount mismatch. Calculated from formula: ${calculatedAmount}, Provided: ${providedAmount}`;
                    }
                }

                // Validate clawback formula amount if clawback formula is provided
                if (args.clawbackFormula && args.clawbackFormula.trim().length > 0 && args.clawbackAmount) {
                    // Call calculateOneTimeEarningAmount to get calculated clawback amount
                    const clawbackCalculationResult = await callCalculateOneTimeEarningAmount(
                        organizationDbConnection,
                        args.employeeId,
                        args.clawbackFormula,
                        context
                    );

                    const calculatedClawbackAmount = parseFloat(clawbackCalculationResult.amount).toFixed(2);
                    const providedClawbackAmount = parseFloat(args.clawbackAmount).toFixed(2);

                    // Compare calculated clawback amount with provided clawback amount
                    if (calculatedClawbackAmount !== providedClawbackAmount) {
                        validationError['IVE0774'] = `Clawback amount mismatch. Calculated from formula: ${calculatedClawbackAmount}, Provided: ${providedClawbackAmount}`;
                    }
                }

                if (Object.keys(validationError).length > 0) {
                    throw { code: 'IVE0000', validationError };
                }

                // Check for duplicate one-time earning with Applied or Approved status
                // For edit mode, exclude current record; for add mode, check all records
                const existingEarning = await organizationDbConnection(ehrTables.employeeOneTimeEarnings)
                    .where('Employee_Id', args.employeeId)
                    .where('One_Time_Earning_Type_Id', args.oneTimeEarningTypeId)
                    .whereRaw('LOWER(Approval_Status) IN (?)', [['applied', 'approved']])
                    .modify((queryBuilder) => {
                        if (isEditMode) {
                            queryBuilder.whereNot('One_Time_Earning_Id', args.oneTimeEarningId);
                        }
                    })
                    .first();

                if (existingEarning) {
                    validationError['IVE0810'] = commonLib.func.getError('', 'IVE0810').message;
                }

                if (Object.keys(validationError).length > 0) {
                    throw { code: 'IVE0000', validationError };
                }

                // Note: Commitment dates (Start/End) are in M,YYYY format
                // Commitment_Period_Months can be overridden at employee level
                // Commitment_Period and Commitment_Period_Reference come from type config

                let insertId;
                if (isEditMode) {
                    await updateOneTimeEarning(
                        organizationDbConnection,
                        args,
                        typeConfig,
                        loggedInEmpId,
                        currentTimestamp,
                        orgCode,
                        orgDetails
                    );
                    insertId = args.oneTimeEarningId;

                    return {
                        errorCode: '',
                        message: 'One Time Earning updated successfully',
                        success: true
                    };
                } else {
                    insertId = await insertOneTimeEarning(
                        organizationDbConnection,
                        args,
                        typeConfig,
                        loggedInEmpId,
                        currentTimestamp,
                        orgCode,
                        orgDetails
                    );

                    return {
                        errorCode: '',
                        message: 'One Time Earning added successfully',
                        success: true
                    };
                }

            } catch (error) {
                console.log('Error in addUpdateOneTimeEarning function main catch block', error);

                // Handle structured validation errors with code and validationError payload
                if ((error && error.code === 'IVE0000') || error === 'IVE0000') {
                    // Extract validationError from structured error or fall back to outer scope
                    const errorPayload = error.validationError || validationError;

                    // Get the first validation error message to use as main error message
                    const firstErrorCode = Object.keys(errorPayload)[0];
                    const firstErrorMessage = errorPayload[firstErrorCode];

                    throw new UserInputError(firstErrorMessage, { validationError: errorPayload });
                } else if (error === '_DB0101' || error === '_DB0102' || error === '_DB0114') {
                    const errResult = commonLib.func.getError('', error);
                    throw new ApolloError(errResult.message, errResult.code);
                }

                const errorCode = error || 'PST0045';
                const errResult = commonLib.func.getError(errorCode, 'PST0045');
                throw new ApolloError(errResult.message, errResult.code);
            } finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};
async function insertOneTimeEarning(organizationDbConnection, args, typeConfig, loggedInEmpId, currentTimestamp, orgCode,orgDetails) {
    return await organizationDbConnection.transaction(async (trx) => {
        let customFormula = null;
        let clawbackFormula = null;

        if (typeConfig.Override_Custom_Formula === 'Yes') {

            customFormula = args.customFormula || typeConfig.Custom_Formula || null;
            clawbackFormula = args.clawbackFormula || typeConfig.Clawback_Formula || null;
        } else {

            customFormula = typeConfig.Custom_Formula || null;
            clawbackFormula = typeConfig.Clawback_Formula || null;
        }

        let payoutPeriod = null;
        let clawbackPeriod = null;
        if (orgDetails && orgDetails.Payroll_Period && orgDetails.Payroll_Period.toLowerCase() === 'bimonthly') {
            clawbackPeriod = args.clawbackPeriod || null;
            payoutPeriod = args.payoutPeriod || null;
        }




        const oneTimeEarningData = {
            Employee_Id: args.employeeId,
            One_Time_Earning_Type_Id: args.oneTimeEarningTypeId,
            Payout_Month: args.payoutMonth, // M,YYYY format
            Payout_Period: payoutPeriod,
            Clawback_Period: clawbackPeriod,
            Amount: args.amount,
            Custom_Formula: customFormula,
            Commitment_Period_Months: args.commitmentPeriodMonths || null,
            Commitment_Start_Month: args.commitmentStartMonth || null, // M,YYYY format
            Commitment_End_Month: args.commitmentEndMonth || null, // M,YYYY format
            Clawback_Amount: args.clawbackAmount || null,
            Clawback_Formula: clawbackFormula,
            Approval_Status: typeConfig.Auto_Approval === 'Yes' ? 'Approved' : 'Applied',
            Process_Instance_Id: null,
            Added_On: currentTimestamp,
            Added_By: loggedInEmpId,
            Updated_On: null,
            Updated_By: null,
            Approved_On: typeConfig.Auto_Approval === 'Yes' ? currentTimestamp : null,
            Approved_By: typeConfig.Auto_Approval === 'Yes' ? loggedInEmpId : null
        };
        // Insert record
        const [insertId] = await organizationDbConnection(ehrTables.employeeOneTimeEarnings)
            .insert(oneTimeEarningData)
            .transacting(trx);

        if (typeConfig.Auto_Approval !== 'Yes') {

            const employeeNameObject = await getEmployeeName(organizationDbConnection, args.employeeId);
            const employeeName = employeeNameObject ? employeeNameObject.employeeName : '';


            const typeTitle = typeConfig.Title || '';

            const instanceData = {
                formId: Number(args.formId),
                oneTimeEarningId: Number(insertId),
                employeeId: Number(args.employeeId),
                Employee_Id: Number(args.employeeId),
                employeeName: String(employeeName),
                oneTimeEarningTypeId: Number(args.oneTimeEarningTypeId),
                adhocAllowanceTypeTitle: String(typeTitle),
                amount: String(args.amount),
                payoutMonth: String(args.payoutMonth), // M,YYYY format
                payoutPeriod: String(payoutPeriod || ''),
                payoutDurationMonths: Number(typeConfig.Default_Payout_Duration_Months || 0),
                calculationType: String(typeConfig.Calculation_Type || ''),
                customFormula: String(customFormula || ''),
                commitmentPeriod: String(typeConfig.Commitment_Period || ''),
                commitmentPeriodMonths: Number(args.commitmentPeriodMonths || typeConfig.Commitment_Period_Months || 0),
                commitmentStartMonth: String(args.commitmentStartMonth || ''), // M,YYYY format
                commitmentEndMonth: String(args.commitmentEndMonth || ''), // M,YYYY format
                clawbackType: String(typeConfig.Clawback_Type || ''),
                clawbackAmount: String(args.clawbackAmount || ''),
                clawbackFormula: String(clawbackFormula || ''),
                approvalStatus: String('Applied'),
                autoApproval: String(typeConfig.Auto_Approval),
                Added_On: String(currentTimestamp),
                Added_By: Number(loggedInEmpId)
            };

            const addedByNameObject = await getEmployeeName(organizationDbConnection, loggedInEmpId);
            if (!addedByNameObject) {
                console.log('Login employee details not found', loggedInEmpId);
                throw 'ETR0003';
            }
            instanceData.Added_By_Name = addedByNameObject.employeeName;
            const eventId = await getEventId(organizationDbConnection, args.formId);
            if (eventId) {
                const workflowResponse = await commonLib.func.initiateWorkflow(
                    eventId,
                    instanceData,
                    orgCode,
                    args.formId,
                    loggedInEmpId
                );

                if (workflowResponse && workflowResponse.status === 200 && workflowResponse.data?.workflowProcessInstanceId) {
                    await trx('employee_one_time_earnings')
                        .where('One_Time_Earning_Id', insertId)
                        .update({ Process_Instance_Id: workflowResponse.data.workflowProcessInstanceId });
                } else {
                    throw 'ATS0009';
                }
            }
        }

        return insertId;
    });
}
async function updateOneTimeEarning(organizationDbConnection, args, typeConfig, loggedInEmpId, currentTimestamp, orgCode, orgDetails) {
    return await organizationDbConnection.transaction(async (trx) => {
        let customFormula = null;
        let clawbackFormula = null;

        if (typeConfig.Override_Custom_Formula === 'Yes') {
            customFormula = args.customFormula || typeConfig.Custom_Formula || null;
            clawbackFormula = args.clawbackFormula || typeConfig.Clawback_Formula || null;
        } else {
            customFormula = typeConfig.Custom_Formula || null;
            clawbackFormula = typeConfig.Clawback_Formula || null;
        }

        // Apply same conditional logic as insert path for Payout_Period and Clawback_Period
        let payoutPeriodUpdate = null;
        let clawbackPeriodUpdate = null;
        if (orgDetails && orgDetails.Payroll_Period && orgDetails.Payroll_Period.toLowerCase() === 'bimonthly') {
            payoutPeriodUpdate = args.payoutPeriod || null;
            clawbackPeriodUpdate = args.clawbackPeriod || null;
        }

        const updateData = {
            Employee_Id: args.employeeId,
            One_Time_Earning_Type_Id: args.oneTimeEarningTypeId,
            Payout_Month: args.payoutMonth, // M,YYYY format
            Payout_Period: payoutPeriodUpdate,
            Clawback_Period: clawbackPeriodUpdate,
            Amount: args.amount,
            Custom_Formula: customFormula,
            Commitment_Period_Months: args.commitmentPeriodMonths || null,
            Commitment_Start_Month: args.commitmentStartMonth || null, // M,YYYY format
            Commitment_End_Month: args.commitmentEndMonth || null, // M,YYYY format
            Clawback_Amount: args.clawbackAmount || null,
            Clawback_Formula: clawbackFormula,
            Updated_On: currentTimestamp,
            Updated_By: loggedInEmpId
        };
        await organizationDbConnection(ehrTables.employeeOneTimeEarnings)
            .where('One_Time_Earning_Id', args.oneTimeEarningId)
            .update(updateData)
            .transacting(trx);

        const responseObject = await getOneTimeEarningWorkflowProcessInstanceId(
            organizationDbConnection,
            args.oneTimeEarningId
        );

        const employeeNameObject = await getEmployeeName(organizationDbConnection, args.employeeId);
        const employeeName = employeeNameObject ? employeeNameObject.employeeName : '';

        const typeTitle = typeConfig.Title || '';

        const instanceData = {
            formId: Number(args.formId),
            oneTimeEarningId: Number(args.oneTimeEarningId),
            employeeId: Number(args.employeeId),
            Employee_Id: Number(args.employeeId),
            employeeName: String(employeeName),
            oneTimeEarningTypeId: Number(args.oneTimeEarningTypeId),
            adhocAllowanceTypeTitle: String(typeTitle),
            amount: String(args.amount),
            payoutMonth: String(args.payoutMonth),
            payoutPeriod: String(payoutPeriodUpdate || ''),
            // From type config (not stored in employee table)
            payoutDurationMonths: Number(typeConfig.Default_Payout_Duration_Months || 0),
            calculationType: String(typeConfig.Calculation_Type || ''),
            customFormula: String(customFormula || ''),
            commitmentPeriod: String(typeConfig.Commitment_Period || ''),
            // Use overridden value if provided, else use type default
            commitmentPeriodMonths: Number(args.commitmentPeriodMonths || typeConfig.Commitment_Period_Months || 0),
            commitmentStartMonth: String(args.commitmentStartMonth || ''), // M,YYYY format
            commitmentEndMonth: String(args.commitmentEndMonth || ''), // M,YYYY format
            clawbackType: String(typeConfig.Clawback_Type || ''),
            clawbackAmount: String(args.clawbackAmount || ''),
            clawbackFormula: String(clawbackFormula || ''),
            approvalStatus: String('Applied'),
            Added_By: Number(responseObject[0].Added_By),
            Added_By_Name: String(responseObject[0].Added_By_Name),
            autoApproval: String(typeConfig.Auto_Approval),
            Updated_On: String(currentTimestamp),
            Updated_By: Number(loggedInEmpId)
        };

        const updaterNameObject = await getEmployeeName(organizationDbConnection, loggedInEmpId);
        if (updaterNameObject) {
            instanceData['Updated_By_Name'] = updaterNameObject.employeeName;
        }

        const eventId = await getEventId(organizationDbConnection, args.formId);
        if (eventId) {
            const workflowResponse = await commonLib.func.initiateWorkflow(
                eventId,
                instanceData,
                orgCode,
                args.formId,
                loggedInEmpId
            );

            if (workflowResponse && workflowResponse.status === 200 && workflowResponse.data?.workflowProcessInstanceId) {
                
                await trx('employee_one_time_earnings')
                    .where('One_Time_Earning_Id', args.oneTimeEarningId)
                    .update({ Process_Instance_Id: workflowResponse.data.workflowProcessInstanceId });

                if (responseObject && responseObject[0]?.Process_Instance_Id) {
                    await deleteOldApprovalRecordsWithoutTrx(
                        organizationDbConnection,
                        responseObject[0].Process_Instance_Id
                    );
                }
            } else {
                throw 'ATS0009';
            }
        }
    });
}

/**
 * Get employee name for workflow
 * @param {Object} organizationDbConnection - Database connection
 * @param {Number} employeeId - Employee ID
 * @returns {Promise<Object>} - Employee name object
 */
async function getEmployeeName(organizationDbConnection, employeeId) {
    try {
        const employee = await organizationDbConnection('emp_personal_info')
            .select('Emp_First_Name', 'Emp_Middle_Name', 'Emp_Last_Name')
            .where('Employee_Id', employeeId)
            .first();

        if (!employee) return null;

        return {
            employeeName: `${employee.Emp_First_Name} ${employee.Emp_Middle_Name || ''} ${employee.Emp_Last_Name || ''}`.trim()
        };
    } catch (error) {
        console.log('Error in getEmployeeName function:', error);
        throw error;
    }
}

async function deleteOneTimeEarningWithChecks(organizationDbConnection, oneTimeEarningId) {
    return await organizationDbConnection.transaction(async (trx) => {
        // Check if one time earning exists
        const oneTimeEarning = await trx('employee_one_time_earnings')
            .where('One_Time_Earning_Id', oneTimeEarningId)
            .first();

        if (!oneTimeEarning) {
            throw 'IVE0771'; // One time earning not found
        }

        // Check if status is "Approved" - Approved records cannot be deleted
        // Can delete: Applied, Rejected
        // Cannot delete: Approved
        const status = oneTimeEarning.Approval_Status ? oneTimeEarning.Approval_Status.toLowerCase() : '';
        if (status === 'approved') {
            throw 'IVE0776'; // Cannot delete approved one time earning
        }

        // Delete workflow records if exists
        const responseObject = await getOneTimeEarningWorkflowProcessInstanceId(
            organizationDbConnection,
            oneTimeEarningId
        );

        if (responseObject && responseObject[0]?.Process_Instance_Id) {
            await deleteOldApprovalRecordsWithoutTrx(
                organizationDbConnection,
                responseObject[0].Process_Instance_Id
            );
        }

        // Delete the one time earning record
        await trx('employee_one_time_earnings')
            .where('One_Time_Earning_Id', oneTimeEarningId)
            .delete();

        return {
            errorCode: '',
            message: 'One time earning deleted successfully.',
            success: true
        };
    });
}

/**
 * Helper function to call calculateOneTimeEarningAmount
 * @param {Object} organizationDbConnection - Database connection
 * @param {Number} employeeId - Employee ID
 * @param {String} formula - Formula to calculate
 * @param {Object} context - Context object with connection info
 * @returns {Promise<Object>} - Calculation result with amount
 */
async function callCalculateOneTimeEarningAmount(organizationDbConnection, employeeId, formula, context) {
    try {
        // Call the calculateOneTimeEarningAmount resolver function with skipAccessControl flag
        const result = await calculateResolvers.Query.calculateOneTimeEarningAmount(
            null, // parent
            {
                employeeId: employeeId,
                formula: formula,
                skipAccessControl: true // Skip access control for internal calls
            },
            context, // context with connection info
            null // info
        );

        return result;
    } catch (error) {
        console.log('Error in callCalculateOneTimeEarningAmount function:', error);
        throw error;
    }
}

exports.resolvers = resolvers;
