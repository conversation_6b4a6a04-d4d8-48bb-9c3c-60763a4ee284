//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const { ehrTables } = require('../../common/tablealias');



//BIR-2316 Certificate of Compensation Payment/Tax Withheld
module.exports.getIndonesiaStatutoryReport = async (parent, args, context, info) => { 

    let organizationDbConnection;
    try{
        console.log("Inside getIndonesiaStatutoryReport() function ")
        organizationDbConnection= knex(context.connection.OrganizationDb);


        let monthYear = args.resignationMonthYear.split(",")
        
        // First day of the month
        const startDate = moment(`${monthYear[1]}-${monthYear[0]}`, "YYYY-MM").startOf('month').format("YYYY-MM-DD");

        // Last day of the month
        const endDate = moment(`${monthYear[1]}-${monthYear[0]}`, "YYYY-MM").endOf('month').format("YYYY-MM-DD");

        let exsitReportResult = await organizationDbConnection(ehrTables.empResignation + ' as ER')
        .select('EJ.User_Defined_EmpId as Employee_Identification_Number', 'ER.Reason_Id as Reason_Id',
            organizationDbConnection.raw("CASE WHEN EPI.Aadhaar_Card_Number IS NOT NULL THEN EPI.Aadhaar_Card_Number ELSE '' END as Population_Identification"),
            organizationDbConnection.raw(" \'\' as Participant_Number"),
            organizationDbConnection.raw("CASE WHEN SD.Monthly_Gross_Salary IS NOT NULL THEN SD.Monthly_Gross_Salary ELSE 0 END as Final_Wages"),
            organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Full_Name_Worker"),
            organizationDbConnection.raw("CASE WHEN EPI.DOB IS NOT NULL THEN  DATE_FORMAT(EPI.DOB, 'd/%m/%Y') ELSE NULL END as Birth_Date"))
        .join(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'ER.Employee_Id')
        .leftJoin(ehrTables.salaryDetails + ' as SD', 'SD.Employee_Id', 'ER.Employee_Id')
        .join(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EPI.Employee_Id')
        .where('ER.Approval_Status', 'Approved')
        .andWhereBetween('ER.Resignation_Date', [startDate, endDate]);

        let exitReportHeaders = [
            { key: "Auto_Increment", header: "Number" },
            { key: "Employee_Identification_Number", header: "Employee Identification Number" },
            { key: "Participant_Number", header: "Participant's Number" },
            { key: "Population_Identification", header: "Population Identification Number (NIK) / Passport Number (for Foreign TK)" },
            { key: "Full_Name_Worker", header: "Full Name of Worker (According to the KTP, the title/title is placed at the back)" },
            { key: "Final_Wages", header: "Final Wages (in IDR)" },
            { Key: "Birth_Date", header: "Date of Birth (Date/Month/Year)" },
            { Key: "Reason_Id", header: "Information" },
        ]

        exsitReportResult = exsitReportResult.map((item, index) => {
            return {
                ...item,
                Auto_Increment: index + 1 // Adding 1 to start sequence from 1
            };
        });
        

        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return { errorCode: "", message: "BPJSTk exit report has been fetched successfully.",  headers: JSON.stringify(exitReportHeaders), reportData: JSON.stringify(exsitReportResult)}
    }catch(err){
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.error('Error in getIndonesiaStatutoryReport function main catch block.', err);
        let errResult = commonLib.func.getError(err, '_UH0001');
        throw new ApolloError(errResult.message, errResult.code)
    }

}
