const formName = {
    salaryTemplate: 'Salary Template',
    salaryDetails: 'Salary Details',
    employeeDetails:'Employee Details'
}

const formId = {
    salaryTemplateId : 206,
    fixedHealthInsuranceId:152,
    fixedInsuranceId:208,
    variableInsuranceId:209,
    gratuityId:110,
    npsId:126,
    pfId:52,
    insurance:58,
    bonus:46,
    variableInsurance:58,
    salary: 37,
    allowances: 45,
    serviceProviderAdmin: 219,
    grossConfiguration: 403
}

const roles = {
    roleAdd: 'Role_Add',
    roleUpdate: 'Role_Update',
    roleDelete: 'Role_Delete',
    roleView: 'Role_View'
}

const systemLogs = {
    roleAdd: 'Add',
    roleUpdate: 'Update',
    roleDelete: 'Delete'
}

const defaultValues={
    retiralFormIds: [52,110,126,209,208,152],
    esiDisplayName:'Employee State Insurance',
    currencySymbol:'₹',
    salaryType:'Monthly'
}

const deductionName={
    providentFund: 'Provident Fund',
    professionalTax: 'Professional Tax'
}

module.exports.formName = formName;
module.exports.roles = roles;
module.exports.systemLogs = systemLogs;
module.exports.formId = formId;
module.exports.defaultValues= defaultValues;
module.exports.deductionName=deductionName;