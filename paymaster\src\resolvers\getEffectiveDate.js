module.exports.getEffectiveDate = async (parent, args, context, info) => { 
    // require common library to access common function
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
    // require apollo server to return error message
    const { ApolloError } = require('apollo-server-lambda');
    // Organization database connection
    const knex = require('knex');
    // require common constant files
    const constants = require('../common/appconstants');

    // variable declarations
    let errResult = {};
    let organizationDbConnection = '';
    let effectiveDateRange;

    try{
        console.log('Inside getEffectiveDate function');
        // validate input action
        if(args.action==='edit' || args.action==='add')
        {
            // get the organization data base connection
            organizationDbConnection = knex(context.connection.OrganizationDb);

            // get the effective date for the input employee
            let effectiveDate=await commonLib.func.getEffectiveDateBasedOnAction(context.orgCode,organizationDbConnection,args.employeeId,constants.defaultValues.salaryType,args.action)

            // get the range of effective date for presenting date to the user
            if(effectiveDate){
                effectiveDateRange=await commonLib.func.getEffectiveDateDetails(context.orgCode,organizationDbConnection,constants.defaultValues.salaryType,effectiveDate)
            }
            // return response
            return {
                errorCode: "",
                message: "Effective date retrieved successfully.",
                effectiveDate:effectiveDate,
                effectiveDateRange:effectiveDateRange
            }
        }
        else{
            throw 'IVE0097'
        }
    }
    catch (mainCatchError){
        console.log('Error in getEffectiveDate function main catch block',mainCatchError);
        // destroy database connection
        (organizationDbConnection)?organizationDbConnection.destroy():'';
        errResult = commonLib.func.getError(mainCatchError, 'PST0017');
        throw new ApolloError(errResult.message,errResult.code)
    }
    finally {
        // destroy database connection
        (organizationDbConnection)?organizationDbConnection.destroy():'';
    }        
}