//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../common/tablealias');


//SSS monthly contribution collection list of r3 form report 
module.exports.getR3MonthlyContributionReport = async (parent, args, context, info) => {

    let organizationDbConnection;
    try{
        console.log("Inside getR3MonthlyContributionReport() function ")
        organizationDbConnection= knex(context.connection.OrganizationDb);

        
        const[ month, year] = args.quarterEndMonth.split(",");
        const [currentMonthYear, previousMonthYear1, previousMonthYear2] = getQuarterMonths(month, year);
        
        const [employeeDetails, companyDetails, companyLocation] = await Promise.all([ 
        
                organizationDbConnection(ehrTables.empPersonalInfo +' as epi')
                .select('epi.UAN as identificationNumber', 'epi.Emp_First_Name as firstName', 'epi.Emp_Middle_Name as middleName', 'epi.Emp_Last_Name as lastName','er.Resignation_Date as separationDate', 
                    'epfp1.Org_ShareAmount as orgShareAmount1', 'epfp1.Emp_ShareAmount as empShareAmount1', 'epfp2.Org_ShareAmount as orgShareAmount2', 'epfp2.Emp_ShareAmount as empShareAmount2',
                    'epfp3.Org_ShareAmount as orgShareAmount3', 'epfp3.Emp_ShareAmount as empShareAmount3',

                    organizationDbConnection.raw("CASE WHEN epfp1.Org_ShareAmount IS NOT NULL THEN Sum(epfp1.Org_ShareAmount) ELSE 0 END as  totalOrgShareAmount1"),
                    organizationDbConnection.raw("CASE WHEN epfp2.Org_ShareAmount IS NOT NULL THEN Sum(epfp2.Org_ShareAmount) ELSE 0 END as  totalOrgShareAmount2"),
                    organizationDbConnection.raw("CASE WHEN epfp3.Org_ShareAmount IS NOT NULL THEN Sum(epfp3.Org_ShareAmount) ELSE 0 END as  totalOrgShareAmount3"),
                    organizationDbConnection.raw("CASE WHEN epfp1.Emp_ShareAmount IS NOT NULL THEN Sum(epfp1.Emp_ShareAmount) ELSE 0 END as  totalEmpShareAmount1"),
                    organizationDbConnection.raw("CASE WHEN epfp2.Emp_ShareAmount IS NOT NULL THEN Sum(epfp2.Emp_ShareAmount) ELSE 0 END as  totalEmpShareAmount2"),
                    organizationDbConnection.raw("CASE WHEN epfp3.Emp_ShareAmount IS NOT NULL THEN Sum(epfp3.Emp_ShareAmount) ELSE 0 END as  totalEmpShareAmount3")
                ) 
                .leftJoin(ehrTables.empResignation + ' as er', 'er.Employee_Id', 'epi.Employee_Id')
                .innerJoin(ehrTables.empPfPayment + ' as epfp1', function() {
                    this.on('epfp1.Employee_Id', '=', 'epi.Employee_Id')
                      .andOn('epfp1.Salary_Month', '=', organizationDbConnection.raw('?', [previousMonthYear2]));
                })
                .innerJoin(ehrTables.empPfPayment+ ' as epfp2', function() {
                    this.on('epfp2.Employee_Id', '=', 'epi.Employee_Id')
                      .andOn('epfp2.Salary_Month', '=', organizationDbConnection.raw('?', [previousMonthYear1]));
                })
                .innerJoin(ehrTables.empPfPayment + ' as epfp3', function() {
                    this.on('epfp3.Employee_Id', '=', 'epi.Employee_Id')
                      .andOn('epfp3.Salary_Month', '=', organizationDbConnection.raw('?', [currentMonthYear]) );
                }),

                organizationDbConnection(ehrTables.orgDetails).select('Org_Name').limit(1),

                organizationDbConnection(ehrTables.location+" as L")
                .select('L.Phone','L.Street1', 'L.Street2', 'city.City_Name', 'state.State_Name', 'country.Country_Name', 'L.Pincode')
                .leftJoin(ehrTables.country, 'L.Country_Code','country.Country_Code')
                .leftJoin(ehrTables.state, 'L.State_Id','state.State_Id')
                .leftJoin(ehrTables.city, 'L.City_Id', 'city.City_Id')
                .where('L.Location_Type','MainBranch')
            ]);


            let employeeDetail = {
                maillingAddress: {
                    street1:  companyLocation[0]?.Street1,
                    street2: companyLocation[0]?.Street2,
                    cityName: companyLocation[0]?.City_Name,
                    stateName: companyLocation[0]?.State_Name,
                    countryName: companyLocation[0]?.Country_Name,
                    pincode: companyLocation[0]?.Pincode
                },
                mobileNo: companyLocation[0]?.Phone,
                employerName: companyDetails[0]?.Org_Name,
                typeOfEmployer: 'Regular',
                employeeDetails : employeeDetails,
                totalOrgShareAmount1: employeeDetails[0]?.totalOrgShareAmount1,
                totalOrgShareAmount2: employeeDetails[0]?.totalOrgShareAmount2,
                totalOrgShareAmount3: employeeDetails[0]?.totalOrgShareAmount3,
                totalEmpShareAmount1: employeeDetails[0]?.totalEmpShareAmount1,
                totalEmpShareAmount2: employeeDetails[0]?.totalEmpShareAmount2,
                totalEmpShareAmount3: employeeDetails[0]?.totalEmpShareAmount3,

                errorCode: "", 
                message: "SSS R3 Monthly report has been fetched successfully.",
            }

            organizationDbConnection ? organizationDbConnection.destroy() : null;

            return employeeDetail;

    } catch(err){
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.error('Error in getR3MonthlyContributionReport function main catch block.', err);
        let errResult = commonLib.func.getError(err, 'PFF0014');
        throw new ApolloError(errResult.message, errResult.code)
    }
}

const getQuarterMonths = (month, year) => {
    // Create a date object for the given month and year
    const date = new Date(year, month - 1);
  
    // Helper function to format the month and year
    const formatMonthYear = (date) => {
      const formattedMonth = String(date.getMonth() + 1);
      return `${formattedMonth},${date.getFullYear()}`;
    };
  
    // Get the formatted current month and year
    const currentMonthYear = formatMonthYear(date);

    // Subtract one month
    date.setMonth(date.getMonth() - 1);
    const previousMonthYear1 = formatMonthYear(date);
  
    // Subtract another month
    date.setMonth(date.getMonth() - 1);
    const previousMonthYear2 = formatMonthYear(date);
  
    return [currentMonthYear, previousMonthYear1, previousMonthYear2];
  };