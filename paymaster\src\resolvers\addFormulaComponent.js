// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError } = require('apollo-server-lambda');
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');

// resolver definition
const resolvers = {
    Mutation: {
        addFormulaComponent: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                const loggedInEmpId = context.logInEmpId;
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Validate required parameters
                if (!args.formId || !args.componentName || !args.componentType) {
                    throw 'IVE0001'; // Required field missing
                }

                const allowedTypes = ['EARNING', 'BONUS', 'REIMBURSEMENT', 'GROSS', 'RETIRAL', 'CTC'];
                if (!allowedTypes.includes(args.componentType.toUpperCase())) {
                    throw new Error(`Invalid component type. Allowed types are: ${allowedTypes.join(', ')}`);
                }

                // Check access rights - using formId for salary components management
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                   args.formId
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_Add !== 1) {
                    throw '_DB0100'; // Access denied
                }

                // Generate component code from component name
                const componentCode = generateComponentCode(args.componentName, args.componentType);

                // Check uniqueness of both component name and component code
                const existingComponent = await organizationDbConnection(ehrTables.salaryComponents)
                    .where(function() {
                        this.where('Component_Name', args.componentName)
                            .orWhere('Component_Code', componentCode);
                    })
                    .first();

                if (existingComponent) {
                    if (existingComponent.Component_Name === args.componentName) {
                        throw 'PST0031'; // Component name already exists
                    } else {
                        throw 'PST0032'; // Component code already exists
                    }
                }

                // Insert new component
                const insertResult = await organizationDbConnection(ehrTables.salaryComponents)
                    .insert({
                        Component_Code: componentCode,
                        Component_Name: args.componentName,
                        Component_Type: args.componentType.toUpperCase(),
                        Description: args.description || null,
                        Added_On: new Date()
                    });

                const newComponentId = insertResult[0];

                // Fetch the newly created component
                const newComponent = await organizationDbConnection(ehrTables.salaryComponents)
                    .where('Component_Id', newComponentId)
                    .first();

                return {
                    errorCode: '',
                    message: 'Formula component added successfully.',
                    success: true,
                    data: {
                        componentId: newComponent.Component_Id,
                        componentCode: newComponent.Component_Code,
                        componentName: newComponent.Component_Name,
                        componentType: newComponent.Component_Type,
                        description: newComponent.Description
                    }
                };

            } catch (mainCatchError) {
                console.log('Error in addFormulaComponent function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0030');
                throw new ApolloError(errResult.message, errResult.code);
            } finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

/**
 * Generate component code from component name and type
 * @param {String} componentName - Name of the component
 * @param {String} componentType - Type of the component
 * @returns {String} - Generated component code
 */
function generateComponentCode(componentName, componentType) {
    // Convert to lowercase and replace spaces/special chars with underscores
    let baseCode = componentName
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '_')
        .replace(/_+/g, '_')
        .replace(/^_|_$/g, '');

    // Add suffix based on component type
    const typeSuffixes = {
        'EARNING': '_amount',
        'BONUS': '_amount',
        'REIMBURSEMENT': '_amount',
        'GROSS': '_amount',
        'RETIRAL': '_share',
        'CTC': '_amount'
    };

    const suffix = typeSuffixes[componentType.toUpperCase()] || '_amount';
    
    // Ensure the code doesn't already end with the suffix
    if (!baseCode.endsWith(suffix.substring(1))) { // Remove the underscore for checking
        baseCode += suffix;
    }

    return baseCode;
}

module.exports.resolvers = resolvers;
