//Require common library to access common function
const commonLib = require("@cksiva09/hrapp-corelib").CommonLib;
const knex = require("knex");
const { ApolloError } = require("apollo-server-lambda");
const { ehrTables } = require("../../common/tablealias");
const numberToWords = require("number-to-words");
const { getMainBranchLocation } = require("../../common/reportsCommonFunctions");

module.exports.getPND1Report = async (parent, args, context, info) => {
  let organizationDbConnection,
    appManagerDbConnection = "";
    let errorMessage;
  try {
    console.log("Inside getPND1Report() function ");
    let orgCode = context.Org_Code;
    organizationDbConnection = knex(context.connection.OrganizationDb);
    appManagerDbConnection = knex(context.connection.AppManagerDb);
    let [
      employeeDetails,
      companyDetails,
      taxDetails,
      tdsPayment,
      companyLocation,
    ] = await Promise.all([
      organizationDbConnection(ehrTables.empJob + " as EJ")
        .select(
          organizationDbConnection.raw(
            "COUNT(EJ.Employee_Id) as employeeCount"
          ),
          organizationDbConnection.raw(
            "COALESCE(SUM(MFS.Current_Month_Taxable_Salary), 0) as taxableSalaryCount"
          )
        )
        .modify((queryBuilder) => {
          if (args.serviceProviderId) {
            queryBuilder.where(
              "EJ.Service_Provider_Id",
              args.serviceProviderId
            );
          }
        })
        .innerJoin(
          ehrTables.salaryPayslip + " as SP",
          "SP.Employee_Id",
          "EJ.Employee_Id"
        )
        .where("SP.Salary_Month", args.payRollMonth)
        .innerJoin(
          ehrTables.monthlyForm16Snapshot + " as MFS",
          "MFS.Payslip_Id",
          "SP.Payslip_Id"
        ),

      organizationDbConnection(ehrTables.orgDetails)
        .select("Org_Name")
        .limit(1),

      organizationDbConnection(ehrTables.taxConfiguration)
        .select("Service_Provider_Id", "Org_Type", "TAN")
        .modify((queryBuilder) => {
          if (args.serviceProviderId) {
            queryBuilder.where("Service_Provider_Id", args.serviceProviderId);
          } else {
            queryBuilder.limit(1);
          }
        }),
      organizationDbConnection(ehrTables.tdsPayment + " as TDSP")
        .select(
          "TDSPT.Payment_Date as paymentDate",
          "TDSPT.Bank_Name as bankName",
          "TDSPT.Branch_Name as BranchName",
          "TDSPT.Receipt_No",
          "TDSPT.Document_No as documentNo",
          "TDSP.Total_Amount as totalAmount",
          "TDSPT.Amount_Paid"
        )
        .leftJoin(
          ehrTables.tdsPaymentTracker + " as TDSPT",
          "TDSPT.Payment_Id",
          "TDSP.Payment_Id"
        )
        .where("TDSP.Salary_Month", args.payRollMonth),
        getMainBranchLocation(
        organizationDbConnection,
        orgCode,
        appManagerDbConnection,
        args
      ), // Removed await
    ]);
    if(!employeeDetails.length || !tdsPayment.length || !companyDetails.length || !taxDetails.length || !companyLocation){
      if (!employeeDetails.length) {
        errorMessage = "Payslip details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!tdsPayment.length) {
        errorMessage = "TDS payment details are unavailable. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!companyDetails.length) {
        errorMessage = "Company details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!taxDetails.length) {
        errorMessage = "Tax details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!companyLocation) {
        errorMessage = "Company location details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    }
    let name = "";
    if (args.serviceProviderId && companyLocation.organizationName) {
      name = companyLocation.organizationName || "";
    } else if (companyDetails.length > 0) {
      name = companyDetails[0].Org_Name || "";
    }
    companyLocation = companyLocation.location;
    let employeeDetail = {
      maillingAddress: {
        tan: taxDetails[0]?.TAN || "",
        taxAgent: name || "",
        street1: companyLocation[0]?.Street1 || "",
        street2: companyLocation[0]?.Street2 || "",
        cityName: companyLocation[0]?.City_Name || "",
        stateName: companyLocation[0]?.State_Name || "",
        countryName: companyLocation[0]?.Country_Name || "",
        pincode: companyLocation[0]?.Pincode || "",
        mobileNo: companyLocation[0]?.Phone || "",
      },
      taxDetails: {
        Receipt_No: tdsPayment[0]?.Receipt_No || "",
        paymentDate: tdsPayment[0]?.paymentDate || "",
        bankName: tdsPayment[0]?.bankName || "",
        branchName: tdsPayment[0]?.BranchName || "",
        documentNo: tdsPayment[0]?.documentNo || "",
        totalAmountOfIncome: tdsPayment[0]?.Amount_Paid || 0, // Sourced from `tdsPaymentTracker` (Amount Paid) (Total Income Amount)
        totalAmountOfIncomeInWords: numberToWords.toWords(tdsPayment[0]?.Amount_Paid || 0), // tdsPaymentTracker Converts Amount_Paid to words
      },
      reportType: "PND-1",
      employeeDetails: employeeDetails || [],
      employeeCount: employeeDetails[0]?.employeeCount || 0, // Sourced from `employeeDetails` (Employee Count)
      summaryHoldingTaxDetails: {
        generalSalariesAndWages: {
          name: "generalSalariesAndWages", // Static name
          employeeCount: employeeDetails[0]?.employeeCount || 0, // Sourced from `employeeDetails` (Employee Count)
          totalAmountOfIncome: employeeDetails[0]?.taxableSalaryCount || 0, // Sourced from `monthly_form16_snapshot` (Taxable Salary Count)
          amountWithHoldingTax: tdsPayment[0]?.totalAmount || 0, // Sourced from `tds_payment` (Total Amount)
          total:
            (employeeDetails[0]?.taxableSalaryCount || 0) +
            (tdsPayment[0]?.totalAmount || 0), // Sum of `taxableSalaryCount` and `totalAmount` from `employeeDetails` and `tdsPayment`
        },
        approvedSalariesWithholdingTax: {
          name: "approvedSalariesWithholdingTax",
          employeeCount: 0,
          totalAmountOfIncome: 0,
          amountWithHoldingTax: 0,
          total: 0,
        },
        terminationPaymentIncome: {
          name: "terminationPaymentIncome",
          employeeCount: 0,
          totalAmountOfIncome: 0,
          amountWithHoldingTax: 0, 
          total: 0,
        },
        residentIncomeSection40_2: {
          name: "residentIncomeSection40_2", 
          employeeCount: 0,
          totalAmountOfIncome: 0,
          amountWithHoldingTax: 0,
          total: 0,
        },
        nonResidentIncomeSection40_2: {
          name: "nonResidentIncomeSection40_2",
          employeeCount: 0,
          totalAmountOfIncome: 0,
          amountWithHoldingTax: 0,
          total: 0,
        },
      },
      errorCode: "",
      message: "PND Report has been fetched successfully.",
    };
    organizationDbConnection ? organizationDbConnection.destroy() : null;

    return {
      errorCode: "",
      message: "PND Report Report has been fetched successfully.",
      data: employeeDetail,
    };
  } catch (err) {
    organizationDbConnection ? organizationDbConnection.destroy() : null;
    console.error("Error in getPND1Report function main catch block.", err);
    let errResult = commonLib.func.getError(err, "PFF0017");
    if(err == "PFF0018"){
      throw new ApolloError(errorMessage, err);
    }
    throw new ApolloError(errResult.message, errResult.code);
  }
};