// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
// Organization database connection
const knex = require('knex');
// require common constant files
const constants = require('../common/appconstants');
// require table alias
const {ehrTables} = require('../common/tablealias');
// require validation file
const validate=require('../formvalidations/salaryTemplateInputValidation')

// variable declarations
let errResult = {};
let organizationDbConnection = '';
let validationError={};
let checkRights=false;

// resolver definition
const resolvers = {
    Mutation: {
        // function to add salary template
        addSalaryTemplate: async (parent, args, context, info) => {
            try{
                console.log('Inside addSalaryTemplate function');
                // variable declarations
                let loggedInEmpId=context.logInEmpId;
                let ipAddress=context.userIp;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                
                // calculate timezone based on loggedInEmpId
                let employeeTimeZone= await commonLib.func.getEmployeeTimeZone(loggedInEmpId, organizationDbConnection, 1);

                // check whether it is update or insert function based on isEditForm input
                if(args.isEditForm===1){

                    // Check update access right based on employeeid
                    // based on formId no need to recieve we can hardcode
                    checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loggedInEmpId,constants.formName.salaryTemplate,constants.roles.roleUpdate);
                                    
                    if (checkRights === true) {
                        // validate the inputs based on action
                        validationError = await validate.salaryTemplateValidation(args,'edit','',context);

                        if (Object.keys(validationError).length === 0) {
                            // check whether template name already exist or not
                            let checkTemplateNameExist= await validate.checkTemplateNameExist(organizationDbConnection,ehrTables.salaryTemplate,args.templateName,args.templateId,'edit');
                            // if template name does not exist then 1 will be return else 0
                            if(checkTemplateNameExist){
                                return(
                                    organizationDbConnection
                                    .transaction(function(trx){
                                        return(
                                          //not needed remove this
                                            organizationDbConnection(ehrTables.salaryTemplate)
                                            .select('Lock_Flag')
                                            .where('Template_Id',args.templateId)
                                            .transacting(trx)
                                            .then(async (templateData) =>{
                                                // check data exist or not
                                                if (templateData.length>0){
                                                    if (templateData[0].Lock_Flag === 0){
                                                        // update the salary template fields
                                                        return(
                                                            organizationDbConnection(ehrTables.salaryTemplate)
                                                            .update({
                                                                Template_Name : args.templateName,
                                                                Annual_Ctc    : args.annualCTC,
                                                                BasicPay_Type : args.basicPayType,
                                                                Percentage    : args.percentage,
                                                                Amount        : args.amount,
                                                                Description   : args.description,
                                                                Updated_On    : employeeTimeZone, //replace latest api
                                                                Updated_By    : loggedInEmpId
                                                            })
                                                            .where('Template_Id',args.templateId)
                                                            .transacting(trx)
                                                            .then(updateTemplateData =>{
                                                                // delete the records in templateallowance component and template retiralComponents table
                                                                return(
                                                                    organizationDbConnection(ehrTables.templateAllowanceComponents)
                                                                    .del()
                                                                    .where('Template_Id',args.templateId)
                                                                    .transacting(trx)
                                                                    .then(async (delAllowanceRecord) => {
                                                                        return(
                                                                            organizationDbConnection(ehrTables.templateRetiralComponents)
                                                                            .del()
                                                                            .where('Template_Id',args.templateId)
                                                                            .transacting(trx)
                                                                            .then(async (delRetiralsRecord) => {
                                                                                // function to insert record in subtable
                                                                                await insertAllowanceRetirals(organizationDbConnection,args.allowance,args.retirals,args.templateId,ipAddress,loggedInEmpId,args.isEditForm,trx);
                                                                                // return success response to UI
                                                                                return { errorCode : '',message : 'Salary template updated successfully' };

                                                                            })
                                                                        );
                                                                    })
                                                                );
                                                            })
                                                        );
                                                    }
                                                    else{
                                                        // throw error if lock flag is set
                                                        throw '_EC0003';
                                                    }
                                                }
                                                else{
                                                    console.log('Template does not exist');
                                                    throw 'PST0003';
                                                }
                                            })
                                        );
                                    })
                                    .then(function (result) {
                                        return result;
                                    })
                                    //catch db-connectivity errors
                                    .catch(function (catchError) {
                                        console.log('Error in updateSalaryTemplate function .catch() block', catchError);
                                        errResult = commonLib.func.getError(catchError, 'PST0008');
                                        throw new ApolloError(errResult.message,errResult.code);
                                    })
                                    /**close db connection */
                                    .finally(() => {
                                        organizationDbConnection.destroy();
                                    })                        
                                );
                            }
                            else{
                                throw 'IVE0078';
                            }
                        }
                        else{
                            // throw validation error
                            throw ('IVE0000');
                        }
                    }
                    else if (checkRights === false) {
                        throw '_DB0102';
                    } else {
                        // throw error
                        throw (checkRights);
                    }
                }
                else if(args.isEditForm===0){
                    // Check add access right based on employeeid
                    checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loggedInEmpId,constants.formName.salaryTemplate,constants.roles.roleAdd);
                    if (checkRights === true) {
                        // validate the inputs based on action
                        validationError = validate.salaryTemplateValidation(args,'add','',context);

                        if (Object.keys(validationError).length === 0) {   
                            // check whether template name already exist or not
                            let checkTemplateNameExist= await validate.checkTemplateNameExist(organizationDbConnection,ehrTables.salaryTemplate,args.templateName,'','add');
                            // if template name does not exist then 1 will be return else 0
                            if(checkTemplateNameExist){
                                // form input params
                                let salaryTableParams = {             
                                    Template_Name: args.templateName,
                                    Annual_Ctc: args.annualCTC,
                                    BasicPay_Type: args.basicPayType,
                                    Percentage: args.percentage,
                                    Amount: args.amount,
                                    Description: args.description,
                                    Added_On: employeeTimeZone,
                                    Added_By: loggedInEmpId
                                };
                                return(
                                    organizationDbConnection
                                    .transaction(function(trx){
                                        return(
                                            // insert record in salary template table
                                            organizationDbConnection(ehrTables.salaryTemplate)
                                            .insert(salaryTableParams)
                                            .transacting(trx)
                                            .then(async(templateData) =>{
                                                if(templateData.length>0){
                                                    // get the last inserted id
                                                    let insertedTemplateId=templateData[0];  
                                                    // function to insert record in subtable                          
                                                    await insertAllowanceRetirals(organizationDbConnection,args.allowance,args.retirals,insertedTemplateId,ipAddress,loggedInEmpId,args.isEditForm,trx);
                                                    // return success response to UI
                                                    return { errorCode : '',message : 'Salary template added successfully' };
                                                }
                                            })
                                        );
                                    })
                                    .then(function (result) {
                                        return result;
                                    })
                                    //catch db-connectivity errors
                                    .catch(function (catchError) {
                                        console.log('Error in addSalaryTemplate function .catch() block', catchError);
                                        errResult = commonLib.func.getError(catchError, 'PST0007');
                                        throw new ApolloError(errResult.message,errResult.code);
                                    })
                                    /**close db connection */
                                    .finally(() => {
                                        organizationDbConnection.destroy();
                                    })  
                                );
                            }
                            else{
                                throw 'IVE0078';
                            }
                        }
                        else{
                            // throw validation error
                            throw 'IVE0000';
                        }  
                    }
                    else if (checkRights === false) {
                        throw '_DB0101';
                    } else {
                        // throw error
                        throw (checkRights);
                    }
                }
                else{
                    throw 'IVE0077';
                }
            } catch (addSalaryTemplateMainCatch){
                console.log('Error in addSalaryTemplate function main catch block',addSalaryTemplateMainCatch);
                // destroy database connection
                (organizationDbConnection)?organizationDbConnection.destroy():'';
                if (addSalaryTemplateMainCatch === 'IVE0000') {
                    errResult = commonLib.func.getError('', addSalaryTemplateMainCatch);
                    // return response
                    throw new UserInputError(errResult.message,{validationError: validationError});
                }
                else{
                    errResult = commonLib.func.getError(addSalaryTemplateMainCatch, 'PST0009');
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};

// function to insert template components and include system log
async function insertAllowanceRetirals(organizationDbConnection,allowanceArray,retiralsArray,templateId,ipAddress,loggedInEmpId,isEdit,trx){

    //before adding we need to validate whether ctc value and everything in total matches or not if not throw error
    try{
        // variable declarations
        let allowanceDetails = [];
        let retiralsDetails = [];
        let systemLogParams={};

        // form and insert records in subtable
        allowanceDetails = allowanceArray.map(field=>({
            Template_Id   : templateId,
            Allowance_Id  : field.allowanceId,
            Allowance_Type:field.allowanceType,
            Amount        : field.amount,
            Percentage    : field.percentage
        }))

        retiralsDetails = retiralsArray.map(retiralsData=>({
            Template_Id : templateId,
            Form_Id     : retiralsData.formId,
            Retirals_Id : retiralsData.retiralsId,
            Retiral_Wages:retiralsData.retiralWages,
            Retirals_Type:retiralsData.retiralsType,
            Employer_Share_Percentage:retiralsData.employerSharePercentage,
            Employer_Share_Amount:retiralsData.employerShareAmount,
            PF_Employer_Contribution:retiralsData.pfEmployerContribution,
            Employer_Statutory_Limit:retiralsData.employerStatutoryLimit,
            Eligible_For_EPS:retiralsData.eligibleForEPS,
            Admin_Charge:retiralsData.adminCharge,
            EDLI_Charge:retiralsData.edliCharge
        }))

        return(
            // insert allowance record in template_allowance_components table
            organizationDbConnection(ehrTables.templateAllowanceComponents)
            .insert(allowanceDetails)
            .transacting(trx)
            .then(async (insertAllowance) => {
                return(
                    // insert retials record in template_retial_components table
                    organizationDbConnection(ehrTables.templateRetiralComponents)
                    .insert(retiralsDetails)
                    .transacting(trx)
                    .then(async (insertRetirals) => { 
                        // form system log parameters
                        systemLogParams = {
                            action : (isEdit)?constants.systemLogs.roleUpdate:constants.systemLogs.roleAdd,
                            userIp : ipAddress,
                            employeeId: loggedInEmpId,
                            formName : constants.formName.salaryTemplate,
                            trackingColumn: '',
                            organizationDbConnection: organizationDbConnection,
                            uniqueId : templateId
                        }
                        // call function to update system log activities
                        await commonLib.func.createSystemLogActivities(systemLogParams);
                        return 'success';
                    })
                );
            })
        );
    }
    catch(insertRecordError){
        console.log('Error in insertAllowanceRetirals main catch block',insertRecordError);
        throw (isEdit)?'PST0008':'PST0007';
    }
}

exports.resolvers = resolvers;
