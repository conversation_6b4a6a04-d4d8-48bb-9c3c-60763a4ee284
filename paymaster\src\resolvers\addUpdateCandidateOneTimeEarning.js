// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const { validateFormula } = require('../common/formulaValidation');
const { resolvers: calculateResolvers } = require('./calculateOneTimeEarningAmount');
const { ehrTables } = require('../common/tablealias');

// resolver definition
const resolvers = {
    Mutation: {
        // function to delete candidate one time earning
        deleteCandidateOneTimeEarning: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );
                if (Object.keys(checkRights).length === 0 || checkRights.Role_Delete !== 1) {
                    throw '_DB0103';
                }

                // Check if user is Admin (required for Candidate One Time Earnings)
                if (!checkRights.Employee_Role || checkRights.Employee_Role.toLowerCase() !== 'admin') {
                    console.log('Login employee does not have admin access', checkRights);
                    throw '_DB0114';
                }

                // Get candidate ID from the record
                const candidateOneTimeEarning = await organizationDbConnection(ehrTables.candidateOneTimeEarnings)
                    .select('Candidate_Id')
                    .where('Candidate_One_Time_Earning_Id', args.candidateOneTimeEarningId)
                    .first();

                if (!candidateOneTimeEarning) {
                    throw 'IVE0927'; // Candidate one time earning not found
                }

                // Validate candidate status before deletion
                await validateCandidateStatus(
                    organizationDbConnection,
                    candidateOneTimeEarning.Candidate_Id,
                    'delete'
                );

                // Perform deletion with all business logic checks
                const result = await deleteCandidateOneTimeEarningWithChecks(
                    organizationDbConnection,
                    args.candidateOneTimeEarningId
                );

                return result;
            }
            catch (mainCatchError) {
                console.log('Error in deleteCandidateOneTimeEarning function main catch block', mainCatchError);

                if (mainCatchError === 'IVE0924' || mainCatchError === 'IVE0927' || mainCatchError === 'IVE0930' || mainCatchError === 'IVE0528' || mainCatchError === 'IVE0720') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new UserInputError(errResult.message1, { errorCode: mainCatchError });
                } else if (mainCatchError === '_DB0100' || mainCatchError === '_DB0103' || mainCatchError === '_DB0114') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new ApolloError(errResult.message, errResult.code);
                } else {
                    const errResult = commonLib.func.getError(mainCatchError, 'PST0047');
                    throw new ApolloError(errResult.message, errResult.code);
                }
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        },

        // function to add/update candidate one time earning
        addUpdateCandidateOneTimeEarning: async (parent, args, context, info) => {
            let organizationDbConnection;
            let validationError = {};

            try {
                console.log('Inside addUpdateCandidateOneTimeEarning function');

                const loggedInEmpId = context.logInEmpId;
                const orgCode = context.orgCode;
                const userIp = context.userIp;
                const isEditMode = args.candidateOneTimeEarningId ? true : false;

                organizationDbConnection = knex(context.connection.OrganizationDb);

                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );

                if (isEditMode) {
                    if (Object.keys(checkRights).length === 0 || checkRights.Role_Update !== 1) {
                        throw '_DB0102';
                    }
                } else {
                    if (Object.keys(checkRights).length === 0 || checkRights.Role_Add !== 1) {
                        throw '_DB0101';
                    }
                }

                // Check if user is Admin (required for Candidate One Time Earnings)
                if (!checkRights.Employee_Role || checkRights.Employee_Role.toLowerCase() !== 'admin') {
                    console.log('Login employee does not have admin access', checkRights);
                    throw '_DB0114';
                }

                // Get type configuration
                const typeConfig = await organizationDbConnection(ehrTables.onetimeEarningTypes)
                    .where('One_Time_Earning_Type_Id', args.oneTimeEarningTypeId)
                    .where('Status', 'Active')
                    .first();

                if (!typeConfig) {
                    throw 'IVE0919'; // Adhoc allowance type not found or inactive
                }

                // Validate formula if custom formula is provided and type allows override
                if (args.customFormula && typeConfig.Override_Custom_Formula === 'Yes') {
                    const formulaValidation = await validateFormula(organizationDbConnection, args.customFormula);
                    if (!formulaValidation.isValid) {
                        throw 'IVE0920'; // Invalid formula syntax
                    }
                }

                // Validate clawback formula if provided
                if (args.clawbackFormula) {
                    const clawbackFormulaValidation = await validateFormula(organizationDbConnection,args.clawbackFormula);
                    if (!clawbackFormulaValidation.isValid) {
                        throw 'IVE0923'; // Invalid clawback formula syntax
                    }
                }

                // Validate amount against formula if type has formula
                if (typeConfig.Calculation_Type === 'Custom Formula') {
                    const formulaToUse = args.customFormula || typeConfig.Custom_Formula;

                    if (formulaToUse) {
                        const calculationResult = await calculateResolvers.Query.calculateCandidateOneTimeEarningAmount(
                            parent,
                            {
                                candidateId: args.candidateId,
                                formula: formulaToUse,
                                skipAccessControl: true // Skip access control for internal calls
                            },
                            context,
                            info
                        );

                        if (calculationResult.amount !== null && Math.abs(calculationResult.amount - args.amount) > 0.01) {
                            throw 'IVE0921'; // Amount does not match formula calculation
                        }
                    }
                }

                // Validate clawback amount against clawback formula if provided
                if (args.clawbackFormula && args.clawbackAmount) {
                    const clawbackCalculationResult = await calculateResolvers.Query.calculateCandidateOneTimeEarningAmount(
                        parent,
                        {
                            candidateId: args.candidateId,
                            formula: args.clawbackFormula,
                            skipAccessControl: true // Skip access control for internal calls
                        },
                        context,
                        info
                    );

                    if (clawbackCalculationResult.amount !== null && Math.abs(clawbackCalculationResult.amount - args.clawbackAmount) > 0.01) {
                        throw 'IVE0922'; // Clawback amount does not match formula calculation
                    }
                }

                // Validate candidate status before proceeding
                await validateCandidateStatus(
                    organizationDbConnection,
                    args.candidateId,
                    'update' // Use 'update' for both add and update operations
                );

                // Perform business validations
                await performBusinessValidations(
                    organizationDbConnection,
                    args,
                    isEditMode,
                    typeConfig,
                    validationError
                );

                // Perform add/update operation
                const result = await organizationDbConnection.transaction(async (trx) => {
                    let candidateOneTimeEarningId;
                    const currentTimestamp = moment.utc().format('YYYY-MM-DD HH:mm:ss');

                    if (isEditMode) {
                        // Update existing record
                        candidateOneTimeEarningId = args.candidateOneTimeEarningId;
                        await updateCandidateOneTimeEarning(
                            organizationDbConnection,
                            trx,
                            args,
                            loggedInEmpId,
                            currentTimestamp,
                            typeConfig
                        );
                    } else {
                        // Insert new record
                        candidateOneTimeEarningId = await insertCandidateOneTimeEarning(
                            organizationDbConnection,
                            trx,
                            args,
                            loggedInEmpId,
                            currentTimestamp
                        );
                    }

                    return { candidateOneTimeEarningId };
                });

                // All records are saved as 'Applied' status (no workflow for candidates)
                // Workflow will be triggered during candidate-to-employee migration

                return {
                    errorCode: '',
                    message: isEditMode ? 'Candidate one time earning updated successfully' : 'Candidate one time earning added successfully',
                    success: true,
                    candidateOneTimeEarningId: result.candidateOneTimeEarningId,
                    approvalStatus: 'Applied'
                };
            }
            catch (mainCatchError) {
                console.log('Error in addUpdateCandidateOneTimeEarning function main catch block', mainCatchError);

                if (mainCatchError === 'IVE0000') {
                    const errResult = commonLib.func.getError('', 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                } else if (typeof mainCatchError === 'string' && (mainCatchError.startsWith('IVE') || mainCatchError === 'IVE0528' || mainCatchError === 'IVE0720')) {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new UserInputError(errResult.message1, { errorCode: mainCatchError });
                } else if (mainCatchError === '_DB0101' || mainCatchError === '_DB0102' || mainCatchError === '_DB0114') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new ApolloError(errResult.message, errResult.code);
                } else {
                    const errResult = commonLib.func.getError(mainCatchError, 'PST0047');
                    throw new ApolloError(errResult.message, errResult.code);
                }
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

/**
 * Delete candidate one time earning with business logic checks
 */
async function deleteCandidateOneTimeEarningWithChecks(organizationDbConnection, candidateOneTimeEarningId) {
    try {
        // Get the candidate one time earning record
        const candidateOneTimeEarning = await organizationDbConnection(ehrTables.candidateOneTimeEarnings)
            .where('Candidate_One_Time_Earning_Id', candidateOneTimeEarningId)
            .first();

        if (!candidateOneTimeEarning) {
            throw 'IVE0927'; // Candidate one time earning not found
        }

        // Check if status is "Approved" - Approved records cannot be deleted
        // Can delete: Applied, Rejected
        // Cannot delete: Approved
        const status = candidateOneTimeEarning.Approval_Status ? candidateOneTimeEarning.Approval_Status.toLowerCase() : '';
        if (status === 'approved') {
            throw 'IVE0924'; // Cannot delete approved candidate one time earning
        }

        // Delete the record
        await organizationDbConnection(ehrTables.candidateOneTimeEarnings)
            .where('Candidate_One_Time_Earning_Id', candidateOneTimeEarningId)
            .del();

        return {
            errorCode: '',
            message: 'Candidate one time earning deleted successfully',
            success: true
        };
    } catch (error) {
        console.log('Error in deleteCandidateOneTimeEarningWithChecks', error);
        throw error;
    }
}

/**
 * Perform business validations
 */
async function performBusinessValidations(organizationDbConnection, args, isEditMode, typeConfig, validationError) {
    try {
        // Validate candidate exists
        const candidate = await organizationDbConnection('candidate_personal_info')
            .where('Candidate_Id', args.candidateId)
            .first();

        if (!candidate) {
            throw 'IVE0928'; // Candidate not found
        }

        // Check for duplicate one-time earning with Applied or Approved status for the same type
        // For edit mode, exclude current record; for add mode, check all records
        const existingRecord = await organizationDbConnection(ehrTables.candidateOneTimeEarnings)
            .where('Candidate_Id', args.candidateId)
            .where('One_Time_Earning_Type_Id', args.oneTimeEarningTypeId)
            .whereRaw('LOWER(Approval_Status) IN (?)', [['applied', 'approved']])
            .modify((queryBuilder) => {
                if (isEditMode) {
                    queryBuilder.whereNot('Candidate_One_Time_Earning_Id', args.candidateOneTimeEarningId);
                }
            })
            .first();

        if (existingRecord) {
            validationError['IVE0929'] = commonLib.func.getError('', 'IVE0929').message;
        }

        if (Object.keys(validationError).length > 0) {
            throw 'IVE0000';
        }

        // Validate payout period for bimonthly payroll
        const orgDetails = await organizationDbConnection('org_details')
            .select('Payroll_Period')
            .first();

        if (orgDetails && orgDetails.Payroll_Period && orgDetails.Payroll_Period.toLowerCase() === 'bimonthly') {
            if (!args.payoutPeriod) {
                throw 'IVE0925'; // Payout period is required for bimonthly payroll
            }

            const validPayoutPeriods = ['first half', 'second half'];
            if (!validPayoutPeriods.includes(args.payoutPeriod.toLowerCase())) {
                throw 'IVE0926'; // Invalid payout period
            }
        }

        return true;
    } catch (error) {
        console.log('Error in performBusinessValidations', error);
        throw error;
    }
}

/**
 * Insert new candidate one time earning record
 */
async function insertCandidateOneTimeEarning(organizationDbConnection, trx, args, loggedInEmpId, currentTimestamp) {
    try {
        // All candidate one time earnings are saved as 'Applied' status
        // Workflow will be triggered during candidate-to-employee migration
        const insertData = {
            Candidate_Id: args.candidateId,
            One_Time_Earning_Type_Id: args.oneTimeEarningTypeId,
            Payout_Month: args.payoutMonth || null, // M,YYYY format - now optional
            Payout_Period: args.payoutPeriod || null,
            Clawback_Period: args.clawbackPeriod || null,
            Amount: args.amount,
            Custom_Formula: args.customFormula || null,
            Commitment_Period_Months: args.commitmentPeriodMonths ?? null,
            Commitment_Start_Month: args.commitmentStartMonth || null, // M,YYYY format
            Commitment_End_Month: args.commitmentEndMonth || null, // M,YYYY format
            Clawback_Amount: args.clawbackAmount || null,
            Clawback_Formula: args.clawbackFormula || null,
            Approval_Status: 'Applied',
            Process_Instance_Id: null,
            Added_On: currentTimestamp,
            Added_By: loggedInEmpId,
            Approved_On: null,
            Approved_By: null
        };

        const [candidateOneTimeEarningId] = await organizationDbConnection(ehrTables.candidateOneTimeEarnings)
            .insert(insertData)
            .transacting(trx);

        return candidateOneTimeEarningId;
    } catch (error) {
        console.log('Error in insertCandidateOneTimeEarning', error);
        throw error;
    }
}

/**
 * Update existing candidate one time earning record
 */
async function updateCandidateOneTimeEarning(organizationDbConnection, trx, args, loggedInEmpId, currentTimestamp, typeConfig) {
    try {
        // All candidate one time earnings remain as 'Applied' status
        const updateData = {
            One_Time_Earning_Type_Id: args.oneTimeEarningTypeId,
            Payout_Month: args.payoutMonth || null, // M,YYYY format - now optional
            Payout_Period: args.payoutPeriod || null,
            Clawback_Period: args.clawbackPeriod || null,
            Amount: args.amount,
            Custom_Formula: args.customFormula || null,
            Commitment_Period_Months: args.commitmentPeriodMonths ?? null,
            Commitment_Start_Month: args.commitmentStartMonth || null, // M,YYYY format
            Commitment_End_Month: args.commitmentEndMonth || null, // M,YYYY format
            Clawback_Amount: args.clawbackAmount || null,
            Clawback_Formula: args.clawbackFormula || null,
            Updated_On: currentTimestamp,
            Updated_By: loggedInEmpId,
            Approval_Status: 'Applied',
            Process_Instance_Id: null,
            Approved_On: null,
            Approved_By: null
        };

        await organizationDbConnection(ehrTables.candidateOneTimeEarnings)
            .where('Candidate_One_Time_Earning_Id', args.candidateOneTimeEarningId)
            .update(updateData)
            .transacting(trx);

        return true;
    } catch (error) {
        console.log('Error in updateCandidateOneTimeEarning', error);
        throw error;
    }
}

/**
 * Validate candidate status before allowing operations
 */
async function validateCandidateStatus(organizationDbConnection, candidateId, operation = 'update') {
    try {
        const candidateStatus = await organizationDbConnection(ehrTables.candidateRecruitmentInfo + " as CRI")
            .select('CRI.Candidate_Status', 'AST.Status')
            .leftJoin('ats_status_table as AST', 'AST.Id', 'CRI.Candidate_Status')
            .where('CRI.Candidate_Id', candidateId)
            .first();

        // Check if candidate exists
        if (!candidateStatus) {
            throw 'IVE0528'; // Invalid candidate ID
        }

        // Different validation rules for update vs delete operations
        if (operation === 'delete') {
            // For DELETE: Only allow "Hired with compensation" status
            if (candidateStatus.Status !== 'Hired with compensation') {
                throw 'IVE0720'; // Cannot delete - only allowed for "Hired with compensation" status
            }
        } else {
            // For UPDATE/ADD: Allow 3 specific statuses
            const allowedStatuses = [
                'Offer letter Declined',
                'Offer Letter Rejected (Manager/Admin)',
                'Hired with compensation'
            ];

            if (!allowedStatuses.includes(candidateStatus.Status)) {
                throw 'IVE0930'; // Cannot modify - status not allowed
            }
        }

        return true;
    } catch (error) {
        console.log('Error in validateCandidateStatus', error);
        throw error;
    }
}

module.exports = { resolvers };

