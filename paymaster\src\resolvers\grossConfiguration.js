// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const knex = require('knex');
const moment = require('moment');
const { ehrTables } = require('../common/tablealias');
const { formId, systemLogs } = require('../common/appconstants');
const { validateCommonRuleInput } = require('../common/commonfunctions');
const { validateFormula } = require('../common/formulaValidation');

// resolver definition
const resolvers = {
    Query: {
        // function to list gross configurations
        listGrossConfiguration: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside listGrossConfiguration function');
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Check access rights
                const accessFormId = args.formId || formId.grossConfiguration;
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    accessFormId
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100';
                }

                // Build the query
                let grossConfigurations = await organizationDbConnection(ehrTables.grossConfiguration + ' as GC')
                    .select(
                        'GC.Gross_Id',
                        'GC.Salary_Component_Id',
                        'GC.Gross_Name',
                        'GC.Display_Name',
                        'GC.Calculation_Type',
                        'GC.Custom_Formula',
                        'GC.Status',
                        'GC.Description',
                        'GC.Added_On',
                        'GC.Added_By',
                        'GC.Updated_On',
                        'GC.Updated_By',
                        'SC.Component_Name as Salary_Component_Name',
                        'SC.Component_Code as Salary_Component_Code',
                        'SC.Component_Type as Salary_Component_Type',
                        organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as AddedByName"),
                        organizationDbConnection.raw("CONCAT_WS(' ', EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as UpdatedByName")
                    )
                    .leftJoin(ehrTables.salaryComponents + ' as SC', 'SC.Component_Id', 'GC.Salary_Component_Id')
                    .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'GC.Added_By')
                    .leftJoin(ehrTables.empPersonalInfo + ' as EPI2', 'EPI2.Employee_Id', 'GC.Updated_By');

                // Get associated templates for each gross configuration
                const grossConfigurationsWithTemplates = await getAssociatedTemplates(organizationDbConnection, grossConfigurations);

                return {
                    errorCode: '',
                    message: 'Gross configurations retrieved successfully.',
                    grossConfigurations: JSON.stringify(grossConfigurationsWithTemplates)
                };
            }
            catch (mainCatchError) {
                console.log('Error in listGrossConfiguration function', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0032');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    },
    Mutation: {
        // function to delete gross configuration
        deleteGrossConfiguration: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Validate required inputs
                if (!args.grossId) {
                    console.log('Gross Id is required');
                    throw 'PST0033'
                }

                // Check access rights
                const accessFormId = args.formId || formId.grossConfiguration;
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    accessFormId
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_Delete !== 1) {
                    throw '_DB0103';
                }

                await validateGrossConfigurationTemplateAssociation(organizationDbConnection, args, 'delete');

                // Perform deletion with business logic checks
                await organizationDbConnection(ehrTables.grossConfiguration)
                    .where('Gross_Id', args.grossId)
                    .del();

                // System Log
                const systemLogParam = {
                    action: systemLogs.roleDelete,
                    userIp: context.User_Ip,
                    employeeId: loggedInEmpId,
                    formId: accessFormId,
                    trackingColumn: '',
                    organizationDbConnection: organizationDbConnection,
                    uniqueId: args.grossId,
                    message: `Gross configuration deleted successfully for Gross Id: ${args.grossId}`
                };
                await commonLib.func.createSystemLogActivities(systemLogParam);

                return {
                    errorCode: '',
                    message: 'Gross configuration deleted successfully.',
                };
            }
            catch (mainCatchError) {
                console.log('Error in deleteGrossConfiguration function', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0033');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        },

        // function to add/update gross configuration
        addUpdateGrossConfiguration: async (parent, args, context, info) => {
            let organizationDbConnection;
            let validationError = {};
            try {
                const loggedInEmpId = context.logInEmpId;
                const isEditMode = !!args.grossId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Determine formId for access rights (default to gross configuration if not provided)
                const accessFormId = args.formId || formId.grossConfiguration;

                // check access right based on employee id
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    accessFormId
                );

                if (Object.keys(checkRights).length === 0) {
                    throw '_DB0111';
                }

                if (isEditMode && checkRights.Role_Update !== 1) {
                    throw '_DB0102';
                } else if (!isEditMode && checkRights.Role_Add !== 1) {
                    throw '_DB0101';
                }

                // Input validation using validateCommonRuleInput
                const fieldValidations = {};

                // Map fields to validation rules
                if (args.grossName) fieldValidations.grossName = 'IVE0731';
                if (args.description) fieldValidations.description = 'IVE0662';

                validationError = await validateCommonRuleInput(args, fieldValidations);

                // Formula validation for Custom Formula type
                if (args.calculationType?.toLowerCase() === 'custom formula') {
                    if (!args.customFormula || args.customFormula.trim().length === 0) {
                        validationError['IVE0721'] = commonLib.func.getError('', 'IVE0721').message;
                    } else {
                        // Validate formula syntax and semantics
                        const formulaValidationResult = await validateFormula(organizationDbConnection, args.customFormula);
                        if (!formulaValidationResult.isValid) {
                            const firstError = formulaValidationResult.errors[0];
                            validationError[firstError] = commonLib.func.getError('', firstError).message;
                        }
                    }
                }

                if (Object.keys(validationError).length > 0) {
                    throw 'IVE0000';
                }

                // Additional specific validations
                validateGrossConfigurationInput(args, validationError);
                await validateExternalGrossConfiguration(organizationDbConnection, args);

                if (Object.keys(validationError).length > 0) {
                    throw 'IVE0000';
                }

                // Process add/update operation
                const grossId = await processGrossConfiguration(organizationDbConnection, args, loggedInEmpId, isEditMode);

                // System Log
                const systemLogParam = {
                    action: isEditMode ? systemLogs.roleUpdate : systemLogs.roleAdd,
                    userIp: context.User_Ip,
                    employeeId: loggedInEmpId,
                    formId: accessFormId,
                    trackingColumn: '',
                    organizationDbConnection: organizationDbConnection,
                    uniqueId: grossId,
                    message: isEditMode ? `Gross configuration updated successfully for Gross Id: ${grossId}` : `Gross configuration added successfully for Gross Id: ${grossId}`
                };
                await commonLib.func.createSystemLogActivities(systemLogParam);

                // return response
                return {
                    errorCode: '',
                    message: isEditMode ? 'Gross configuration updated successfully.' : 'Gross configuration added successfully.',
                };
            }
            catch (mainCatchError) {
                console.log('Error in addUpdateGrossConfiguration function', mainCatchError);
                if (mainCatchError === 'IVE0000') {
                    const errResult = commonLib.func.getError('', 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError });
                } else {
                    const errResult = commonLib.func.getError(mainCatchError, 'PST0031');
                    throw new ApolloError(errResult.message, errResult.code);
                }
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

async function validateExternalGrossConfiguration(organizationDbConnection, args) {
    try {
        const duplicateName = await organizationDbConnection(ehrTables.grossConfiguration)
            .where('Gross_Name', args.grossName.trim())
            .where('Status', 'Active')
            .where(function () {
                if (args.grossId) {
                    this.whereNot('Gross_Id', args.grossId);
                }
            })
            .first();

        if (duplicateName) {
            throw 'PST0117';
        }

        //Validate if gross configuration is associated with template_gross_components and if it is being edited
        if (args.grossId) {
            await validateGrossConfigurationTemplateAssociation(organizationDbConnection, args);
        }

        // Note: Multiple gross configurations can have the same Salary_Component_Id
        // Each gross configuration is uniquely identified by Gross_Id

    } catch (error) {
        console.log('Error in validateExternalGrossConfiguration:', error);
        throw error;
    }
}

// Validate gross configuration specific inputs
function validateGrossConfigurationInput(args, validationError) {
    try {
        // Validate enum fields
        const validCalculationType = ['Custom Formula', 'Default'];
        if (args.calculationType && !validCalculationType.includes(args.calculationType)) {
            validationError['IVE0713'] = commonLib.func.getError('', 'IVE0713').message1;
        }

        const validStatus = ['Active', 'Inactive'];
        if (args.status && !validStatus.includes(args.status)) {
            validationError['IVE0670'] = 'Status must be either Active or Inactive.';
        }

        // Validate required fields
        if (!args.grossName || args.grossName.trim().length === 0) {
            validationError['IVE0659'] = 'Gross name is required.';
        }

        if (!args.salaryComponentId) {
            validationError['IVE0722'] = 'Salary Component is required.';
        }

        if (!args.calculationType) {
            validationError['IVE0744'] = 'Calculation Type is required.';
        }

        return validationError;
    } catch (error) {
        console.log('Error in validateGrossConfigurationInput:', error);
        throw error;
    }
}

// Process gross configuration add/update
async function processGrossConfiguration(organizationDbConnection, args, loggedInEmpId, isEditMode) {
    try {
        // Prepare data for insertion/update
        const grossData = {
            Salary_Component_Id: args.salaryComponentId,
            Gross_Name: args.grossName.trim(),
            Display_Name: args.displayName ? args.displayName.trim() : args.grossName.trim(),
            Calculation_Type: args.calculationType,
            Custom_Formula: args.calculationType === 'Custom Formula' ? args.customFormula : null,
            Status: args.status || 'Active',
            Description: args.description ? args.description.trim() : null
        };

        // Set audit fields
        const currentTimestamp = moment.utc().format('YYYY-MM-DD HH:mm:ss');
        if (isEditMode) {
            grossData.Updated_By = loggedInEmpId;
            grossData.Updated_On = currentTimestamp;
        } else {
            grossData.Added_By = loggedInEmpId;
            grossData.Added_On = currentTimestamp;
        }

        let grossId;

        if (isEditMode) {
            // Update existing gross configuration
            await organizationDbConnection(ehrTables.grossConfiguration)
                .where('Gross_Id', args.grossId)
                .update(grossData);

            grossId = args.grossId;
        } else {
            // Insert new gross configuration
            const [insertId] = await organizationDbConnection(ehrTables.grossConfiguration).insert(grossData);
            grossId = insertId;
        }

        return grossId;

    } catch (error) {
        console.log('Error in processGrossConfiguration:', error);
        throw error;
    }
}

// Validate gross configuration template association for edit mode
async function validateGrossConfigurationTemplateAssociation(organizationDbConnection, args, operation) {
    try {
        //Validate gross configuration exists
        const grossConfiguration = await organizationDbConnection(ehrTables.grossConfiguration)
            .where('Gross_Id', args.grossId)
            .first();

        if (!grossConfiguration) {
            throw 'PST0119';
        } else {
            //Validate if other than gross name is changed
            if (operation === 'delete' || grossConfiguration.Salary_Component_Id !== args.salaryComponentId ||
                grossConfiguration.Calculation_Type !== args.calculationType ||
                grossConfiguration.Custom_Formula !== args.customFormula ||
                grossConfiguration.Status !== args.status) {
                //Check if it is associated with template_gross_components
                const associatedWithTemplate = await organizationDbConnection(ehrTables.templateGrossComponents + ' as TGC')
                    .leftJoin(ehrTables.salaryTemplate + ' as ST', 'ST.Template_Id', 'TGC.Template_Id')
                    .where('Gross_Id', args.grossId)
                    .where('ST.Template_Status', 'Active')
                    .first();

                if (associatedWithTemplate) {
                    throw 'PST0120';
                }
            }
        }
    } catch (error) {
        console.log('Error in validateGrossConfigurationTemplateAssociation:', error);
        throw error;
    }
}

// Get associated templates for each gross configuration
async function getAssociatedTemplates(organizationDbConnection, grossConfigurations) {
    try {
        if (!grossConfigurations || grossConfigurations.length === 0) {
            return grossConfigurations;
        }

        // Extract all gross IDs
        const grossIds = grossConfigurations.map(gc => gc.Gross_Id);

        // Get all template associations for these gross configurations
        const templateAssociations = await organizationDbConnection(ehrTables.templateGrossComponents + ' as TGC')
            .select(
                'TGC.Template_Id',
                'ST.Template_Name',
                'TGC.Gross_Id'
            )
            .leftJoin(ehrTables.salaryTemplate + ' as ST', 'ST.Template_Id', 'TGC.Template_Id')
            .whereIn('TGC.Gross_Id', grossIds)
            .where('ST.Template_Status', 'Active')
            .orderBy('ST.Template_Name', 'asc');

        console.log('templateAssociations', templateAssociations);

        // Add associated templates to each gross configuration
        const grossConfigurationsWithTemplates = grossConfigurations.map(grossConfig => {
            return {
                ...grossConfig,
                templateAssociations: templateAssociations.filter(ta => ta.Gross_Id === grossConfig.Gross_Id)
            };
        });

        return grossConfigurationsWithTemplates;
    } catch (error) {
        console.log('Error in getAssociatedTemplates:', error);
        // Return original data if there's an error getting templates
        return grossConfigurations;
    }
}

module.exports.resolvers = resolvers;
