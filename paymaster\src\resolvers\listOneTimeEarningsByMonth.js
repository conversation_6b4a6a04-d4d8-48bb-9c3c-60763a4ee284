// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');

// resolver definition
const resolvers = {
    Query: {
        // function to list one time earnings by payout or clawback month or for payroll prerequisite
        listBenefitAndDeductionByMonth: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside listBenefitAndDeductionByMonth function');
                const loggedInEmpId = context.logInEmpId;
                const { formId, month, year, action, employeeId, status } = args;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // check access right based on employeeid
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    formId
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100';
                }

                // Validate action
                if (!['payout', 'clawback', 'payrollprereq'].includes(action.toLowerCase())) {
                    throw 'IVE0779'; // Invalid action type
                }

                // get one time earnings data filtered by month
                const oneTimeEarningsData = await getOneTimeEarningsByMonth(
                    organizationDbConnection,
                    month,
                    year,
                    action.toLowerCase(),
                    employeeId,
                    status
                );

                // return response
                return {
                    errorCode: '',
                    message: `One time earnings for ${action} month ${month}/${year} listed successfully.`,
                    success: true,
                    oneTimeEarnings: JSON.stringify(oneTimeEarningsData)
                };
            }
            catch (mainCatchError) {
                console.log('Error in listBenefitAndDeductionByMonth function main catch block', mainCatchError);

                if (mainCatchError === 'IVE0774') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new ApolloError(errResult.message1, mainCatchError);
                }

                const errResult = commonLib.func.getError(mainCatchError, 'PST0046');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

// function to get one time earnings filtered by payout or clawback month or for payroll prerequisite
async function getOneTimeEarningsByMonth(organizationDbConnection, month, year, action, employeeId, status) {
    try {
        return await organizationDbConnection.transaction(async (trx) => {
            // Build the M,YYYY format for filtering
            const targetMonth = `${month},${year}`;

            // Build query for one time earnings with JOIN to get type config
            let oneTimeEarningsQuery = organizationDbConnection('employee_one_time_earnings as EOTE')
                .select(
                    // Employee table fields (stored)
                    'EOTE.One_Time_Earning_Id',
                    'EOTE.Employee_Id',
                    'EOTE.One_Time_Earning_Type_Id',
                    'EOTE.Payout_Month',
                    'EOTE.Payout_Period',
                    'EOTE.Clawback_Period',
                    'EOTE.Amount',
                    'EOTE.Custom_Formula',
                    // Use overridden value if exists, else use type default
                    organizationDbConnection.raw('COALESCE(EOTE.Commitment_Period_Months, AAT.Commitment_Period_Months) as Commitment_Period_Months'),
                    'EOTE.Commitment_Start_Month',
                    'EOTE.Commitment_End_Month',
                    'EOTE.Clawback_Amount',
                    'EOTE.Clawback_Formula',
                    'EOTE.Approval_Status',
                    'EOTE.Process_Instance_Id',
                    'EOTE.Added_On',
                    'EOTE.Updated_On',
                    'EOTE.Added_By',
                    'EOTE.Updated_By',
                    'EOTE.Approved_On',
                    'EOTE.Approved_By',
                    // Type config fields (retrieved from onetime_earning_types)
                    'AAT.Calculation_Type',
                    'AAT.Default_Payout_Duration_Months as Payout_Duration_Months',
                    'AAT.Commitment_Period',
                    'AAT.Commitment_Period_Months as Type_Commitment_Period_Months',
                    'AAT.Clawback_Type',
                    'AAT.Auto_Approval',
                    // Employee name
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Employee_Name"),
                    // Adhoc allowance type title
                    'AAT.Title',
                    // Added by name
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI_Added.Emp_First_Name, EPI_Added.Emp_Middle_Name, EPI_Added.Emp_Last_Name) as Added_By_Name"),
                    // Updated by name
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI_Updated.Emp_First_Name, EPI_Updated.Emp_Middle_Name, EPI_Updated.Emp_Last_Name) as Updated_By_Name"),
                    // Approved by name
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI_Approved.Emp_First_Name, EPI_Approved.Emp_Middle_Name, EPI_Approved.Emp_Last_Name) as Approved_By_Name")
                )
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'EOTE.Employee_Id')
                .join(ehrTables.onetimeEarningTypes + ' as AAT', 'AAT.One_Time_Earning_Type_Id', 'EOTE.One_Time_Earning_Type_Id')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI_Added', 'EPI_Added.Employee_Id', 'EOTE.Added_By')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI_Updated', 'EPI_Updated.Employee_Id', 'EOTE.Updated_By')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI_Approved', 'EPI_Approved.Employee_Id', 'EOTE.Approved_By')
                .transacting(trx);

            // Filter by payout month or clawback month based on action (M,YYYY format)
            if (action === 'payout') {
                oneTimeEarningsQuery = oneTimeEarningsQuery
                    .where('EOTE.Payout_Month', targetMonth);
            } else if (action === 'clawback') {
                oneTimeEarningsQuery = oneTimeEarningsQuery
                    .where('EOTE.Commitment_End_Month', targetMonth)
                    .whereNotNull('EOTE.Commitment_End_Month'); // Only records with clawback month
            } else if (action === 'payrollprereq') {
                oneTimeEarningsQuery = oneTimeEarningsQuery
                    .where('EOTE.Payout_Month', targetMonth);
            }

            // Filter by approval status if provided
            if (status) {
                oneTimeEarningsQuery = oneTimeEarningsQuery
                    .where('EOTE.Approval_Status', status);
            } else {
                // Default: Only approved records for payout and clawback actions
                if (action === 'payout' || action === 'clawback') {
                    oneTimeEarningsQuery = oneTimeEarningsQuery
                        .where('EOTE.Approval_Status', 'Approved');
                }
            }

            // Filter by employee if provided
            if (employeeId) {
                oneTimeEarningsQuery = oneTimeEarningsQuery.where('EOTE.Employee_Id', employeeId);
            }

            // Order by month (payout or clawback based on action)
            if (action === 'payout' || action === 'payrollprereq') {
                oneTimeEarningsQuery = oneTimeEarningsQuery.orderBy('EOTE.Payout_Month', 'asc');
            } else if (action === 'clawback') {
                oneTimeEarningsQuery = oneTimeEarningsQuery.orderBy('EOTE.Commitment_End_Month', 'asc');
            }

            const oneTimeEarnings = await oneTimeEarningsQuery;

            return oneTimeEarnings;
        });
    } catch (error) {
        console.log('Error in getOneTimeEarningsByMonth function', error);
        throw error;
    }
}

module.exports = {
    resolvers
};

