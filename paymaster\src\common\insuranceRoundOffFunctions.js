const { ehrTables } = require('./tablealias');

/**
 * Fetches all insurance round-off settings from the database
 * @param {Object} organizationDbConnection - Database connection object
 * @returns {Promise<Map>} - Map with InsuranceType_Id as key and Round_Off_Insurance as value
 */
async function getInsuranceRoundOffSettings(organizationDbConnection) {
    try {
        const insuranceConfigs = await organizationDbConnection(ehrTables.insuranceConfiguration)
            .select('InsuranceType_Id', 'Round_Off_Insurance')
            .whereNotNull('Round_Off_Insurance');
        
        // Create a Map for O(1) lookup by InsuranceType_Id
        return new Map(insuranceConfigs.map(config => [config.InsuranceType_Id, config.Round_Off_Insurance]));
    } catch (error) {
        console.error('Error fetching insurance round-off settings:', error);
        return new Map(); // Return empty map if error
    }
}

/**
 * Gets round-off value for a specific insurance type based on insurance configuration
 * @param {number} insuranceTypeId - Insurance Type ID
 * @param {number} value - Amount to round off
 * @param {Map} insuranceRoundOffMap - Map of insurance round-off settings
 * @returns {number} - Rounded amount
 */
function getInsuranceSpecificRoundOffValue(insuranceTypeId, value, insuranceRoundOffMap) {
    try {
        // Get the round-off setting for this insurance type
        const roundOffSetting = insuranceRoundOffMap.get(insuranceTypeId);
        
        if (!roundOffSetting || roundOffSetting === 'Do not round off the value') {
            return value; // No rounding
        }
        
        // Apply specific rounding based on enum value
        switch (roundOffSetting) {
            case 'Round to nearest digit':
                return Math.round(value);
            case 'Round up to next digit':
                return Math.ceil(value);
            case 'Round down to previous digit':
                return Math.floor(value);
            default:
                return value;
        }
    } catch (error) {
        console.error('Error in getInsuranceSpecificRoundOffValue:', error);
        return value; // Return original value if error
    }
}

module.exports = {
    getInsuranceRoundOffSettings,
    getInsuranceSpecificRoundOffValue
};
