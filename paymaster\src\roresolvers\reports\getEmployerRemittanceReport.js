//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const { ehrTables } = require('../../common/tablealias');

//Rf1 philhealth - employeers remittance report
module.exports.getEmployerRemittanceReport = async (parent, args, context, info) => {

    let organizationDbConnection;
    try{
        console.log("Inside getEmployerRemittanceReport() function ")
        organizationDbConnection= knex(context.connection.OrganizationDb);

        const [employeeDetails, companyDetails, taxDetails, companyLocation] = await Promise.all([ 
        
                organizationDbConnection( ehrTables.empPersonalInfo + ' as epi')
                .select('insu.Policy_No as identificationNumber', 'epi.Emp_First_Name as firstName', 'epi.Emp_Middle_Name as middleName', 'epi.Emp_Last_Name as lastName', 'epi.DOB as dateOfBirth', 'epi.Gender as gender',
                    'insPay.Org_ShareAmount as employerShare', 'insPay.Emp_ShareAmount as personalShare',
                    organizationDbConnection.raw('CASE WHEN insPay.Org_ShareAmount IS NOT NULL THEN SUM(insPay.Org_ShareAmount) ELSE 0 END as employerShareTotal'),
                    organizationDbConnection.raw('CASE WHEN insPay.Emp_ShareAmount IS NOT NULL THEN SUM(insPay.Emp_ShareAmount) ELSE 0 END as personalShareTotal')
                )
                .innerJoin(ehrTables.empInsurance + ' as insu', 'insu.Employee_Id', 'epi.Employee_Id')
                .innerJoin(ehrTables.empInsurancePayment + ' as insPay', function() {
                    this.on('insPay.Employee_Id', '=', 'epi.Employee_Id')
                      .andOn('insPay.Salary_Month', '=',  organizationDbConnection.raw('?', [args.payRollMonth]) );
                }),

                organizationDbConnection(ehrTables.orgDetails).select('Org_Name').limit(1),

                organizationDbConnection(ehrTables.taxConfiguration).select('Service_Provider_Id', 'Org_Type', 'TAN').limit(1),

                organizationDbConnection(ehrTables.location+" as L")
                .select('L.Phone','L.Street1', 'L.Street2', 'city.City_Name', 'state.State_Name', 'country.Country_Name', 'L.Pincode')
                .leftJoin(ehrTables.country, 'L.Country_Code','country.Country_Code')
                .leftJoin(ehrTables.state, 'L.State_Id','state.State_Id')
                .leftJoin(ehrTables.city, 'L.City_Id', 'city.City_Id')
                .where('L.Location_Type','MainBranch')
            ]);


            let employeeDetail = {
                maillingAddress: {
                    street1:  companyLocation[0]?.Street1,
                    street2: companyLocation[0]?.Street2,
                    cityName: companyLocation[0]?.City_Name,
                    stateName: companyLocation[0]?.State_Name,
                    countryName: companyLocation[0]?.Country_Name,
                    pincode: companyLocation[0]?.Pincode
                },
                mobileNo: companyLocation[0]?.Phone,
                employerTinNo: taxDetails[0]?.TAN,
                employerName: companyDetails[0]?.Org_Name,
                employerType: 'PRIVATE',
                reportType: 'REGULAR RF-1',
                employeeDetails : employeeDetails,
                employerShareTotal: employeeDetails[0]?.employerShareTotal,
                personalShareTotal: employeeDetails[0]?.personalShareTotal,
                dateReceived: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                errorCode: "", 
                message: "Employer Remittance Report has been fetched successfully.",
            }

            organizationDbConnection ? organizationDbConnection.destroy() : null;

            return employeeDetail;

    } catch(err){
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.error('Error in getEmployerRemittanceReport function main catch block.', err);
        let errResult = commonLib.func.getError(err, 'PFF0012');
        throw new ApolloError(errResult.message, errResult.code)
    }
}