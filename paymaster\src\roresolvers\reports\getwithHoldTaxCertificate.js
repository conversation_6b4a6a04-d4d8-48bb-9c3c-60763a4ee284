const { ehrTables } = require("../../common/tablealias");
const knex = require("knex");
const { ApolloError } = require("apollo-server-lambda");
const commonLib = require("@cksiva09/hrapp-corelib").CommonLib;
const { getMainBranchLocation, getCompanyDetails, getTaxDetails, getMonthSequence } = require("../../common/reportsCommonFunctions");

module.exports.getWithHoldTaxCertificate = async (parent, args, context, info) => {
  const organizationDbConnection = knex(context.connection.OrganizationDb);
  const appManagerDbConnection = knex(context.connection.AppManagerDb);
  const assessmentYear = getMonthSequence(args.assessmentYear);
  let errorMessage;
  try {
    let [
      employeeDetails,
      companyDetails,
      taxDetails,
      companyLocation
    ] = await Promise.all([
      getEmployeeDetails(organizationDbConnection, args,assessmentYear),
      getCompanyDetails(organizationDbConnection),
      getTaxDetails(organizationDbConnection, args),
      getMainBranchLocation(
        organizationDbConnection,
        context.Org_Code,
        appManagerDbConnection,
        args
      )
    ]);
    if(!employeeDetails || !employeeDetails.length || !companyDetails || !companyDetails.length || !taxDetails || !taxDetails.length || !companyLocation){
      if (!employeeDetails.length) {
        errorMessage = "Payslip details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!taxDetails.length) {
        errorMessage = "Tax details are unavailable. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!companyDetails.length) {
        errorMessage = "Company details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!taxDetails.length) {
        errorMessage = "Tax details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!companyLocation) {
        errorMessage = "Company location details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
      
    }
    let name = "";
    if (args.serviceProviderId && companyLocation.organizationName) {
      name = companyLocation.organizationName || "";
    } else if (companyDetails.length > 0) {
      name = companyDetails[0].Org_Name || "";
    }
    companyLocation = companyLocation.location;
    const withHoldTaxDetail = {
      mailingCompanyAddress: {
        tan: (taxDetails && taxDetails[0] && taxDetails[0].TAN) || "",
        taxAgent: name,
        street1: (companyLocation && companyLocation[0] && companyLocation[0].Street1) || "",
        street2: (companyLocation && companyLocation[0] && companyLocation[0].Street2) || "",
        cityName: (companyLocation && companyLocation[0] && companyLocation[0].City_Name) || "",
        stateName: (companyLocation && companyLocation[0] && companyLocation[0].State_Name) || "",
        countryName: (companyLocation && companyLocation[0] && companyLocation[0].Country_Name) || "",
        pincode: (companyLocation && companyLocation[0] && companyLocation[0].Pincode) || "",
        mobileNo: (companyLocation && companyLocation[0] && companyLocation[0].Phone) || "",
      },
      employeeDetails: {
        tan: employeeDetails[0].panNo || "",
        personalIdentificationNo: employeeDetails[0].personalIdentificationNo || "",
        name: employeeDetails[0].Name || "",
        address: {
          apartmentName: (employeeDetails && employeeDetails[0] && employeeDetails[0].pApartment_Name) || "",
          streetName: (employeeDetails && employeeDetails[0] && employeeDetails[0].pStreet_Name) || "",
          city: (employeeDetails && employeeDetails[0] && employeeDetails[0].pCity) || "",
          state: (employeeDetails && employeeDetails[0] && employeeDetails[0].pState) || "",
          country: (employeeDetails && employeeDetails[0] && employeeDetails[0].pCountry) || "",
          pincode: (employeeDetails && employeeDetails[0] && employeeDetails[0].pPincode) || "",
          mobileNo: (employeeDetails && employeeDetails[0] && employeeDetails[0].Mobile_No) || "",
          mobileNoCode: (employeeDetails && employeeDetails[0] && employeeDetails[0].Mobile_No_Country_Code) || ""
        },
      },
      reportType: "With Holding Tax Certificate",
      taxWithHoldingDetails: {
        salaryWagePensionSection40_1: {
          amountPaid: employeeDetails[0].taxableSalaryCount || 0,
          taxWithHeld: employeeDetails[0].deductionSalaryCount || 0,
        },
        commissionsSection40_2: {
          amountPaid: 0,
          taxWithHeld: 0,
        },
        royaltiesSection40_3: {
          amountPaid: 0,
          taxWithHeld: 0,
        },
        interestSection40_4_a: {
          amountPaid: 0,
          taxWithHeld: 0,
        },
        incomeSubjectToWithholdingSection3Tredecim: {
          amountPaid: 0,
          taxWithHeld: 0,
        },
        others: {
          amountPaid: 0,
          taxWithHeld: 0,
        }
      },
      errorCode: "",
      message: "Tax with held  has been fetched successfully.",
    };

    organizationDbConnection.destroy();
    appManagerDbConnection.destroy();

    return withHoldTaxDetail;
  } catch (err) {
    organizationDbConnection.destroy();
    appManagerDbConnection.destroy();
    console.error("Error in getwithHoldTaxCertificate function main catch block.", err);
    if(err == "PFF0018"){
      throw new ApolloError(errorMessage, err);
    }
    throw new ApolloError(commonLib.func.getError(err, "PFF0017").message, commonLib.func.getError(err, "PFF0017").code);
  }
};

async function getEmployeeDetails(organizationDbConnection, args,assessmentYear) {
  return organizationDbConnection(ehrTables.empJob + " as EJ")
    .select(
      organizationDbConnection.raw(
        "COALESCE(SUM(MFS.Current_Month_Taxable_Salary), 0) as taxableSalaryCount"
      ),
      organizationDbConnection.raw(
        "COALESCE(SUM(SD.Deduction_Amount), 0) as deductionSalaryCount"
      ),
      "EPI.Aadhaar_Card_Number as taxpayerIdentificationNumber",
      organizationDbConnection.raw(
        "CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Name"
      ),
      "CD.pApartment_Name",
      "CD.pStreet_Name",
      "CD.pCity",
      "CD.pState",
      "CD.pCountry",
      "CD.pPincode",
      "CD.Mobile_No",
      "CD.Mobile_No_Country_Code"
    )
    .modify((queryBuilder) => {
      if (args.serviceProviderId) {
        queryBuilder.where("EJ.Service_Provider_Id", args.serviceProviderId);
      }
    })
    .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "EJ.Employee_Id")
    .leftJoin(ehrTables.contactDetails + " as CD", "CD.Employee_Id", "EPI.Employee_Id")
    .innerJoin(ehrTables.salaryPayslip + " as SP", "SP.Employee_Id", "EJ.Employee_Id")
    .whereIn("SP.Salary_Month", assessmentYear)
    .where("EJ.Employee_Id", args.employeeId)
    .innerJoin(ehrTables.salaryDeduction + " as SD", "SP.Payslip_Id", "SD.Payslip_Id")
    .innerJoin(
      ehrTables.monthlyForm16Snapshot + " as MFS",
      "MFS.Payslip_Id",
      "SP.Payslip_Id"
    );
}
