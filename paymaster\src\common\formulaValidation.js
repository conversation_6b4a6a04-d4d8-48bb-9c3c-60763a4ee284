// Formula Validation Service - Function-based for frontend sharing

// Cache for validation data (can be shared with frontend)
let validationCache = {
    components: new Map(),
    functions: new Map(),
    operators: new Set(),
    initialized: false
};

/**
 * Built-in functions with their parameter constraints
 * Single source of truth for all built-in function definitions
 */
const BUILT_IN_FUNCTIONS = {
    'if': { minParams: 3, maxParams: 3 },           // IF(condition, value_if_true, value_if_false)
    'date_diff': { minParams: 2, maxParams: 2 },    // DATE_DIFF(date1, date2)
    'date_add': { minParams: 2, maxParams: 2 },     // DATE_ADD(date, days)
    'date_year': { minParams: 1, maxParams: 1 },    // DATE_YEAR(date)
    'date_month': { minParams: 1, maxParams: 1 },   // DATE_MONTH(date)
    'date_day': { minParams: 1, maxParams: 1 },     // DATE_DAY(date)
    'date_today': { minParams: 0, maxParams: 0 },   // DATE_TODAY()
    'days_in_months': { minParams: 1, maxParams: 1 }, // DAYS_IN_MONTHS(months)
    'days_in_years': { minParams: 1, maxParams: 1 }   // DAYS_IN_YEARS(years)
};

/**
 * Initialize validation cache with database data
 * @param {Object} dbConnection - Database connection
 */
async function initializeValidationCache(dbConnection) {
    try {
        // Load valid components (ALL types including LEAVE components)
        const components = await dbConnection('salary_components')
            .select('Component_Code', 'Component_Name', 'Component_Type');

        components.forEach(comp => {
            validationCache.components.set(comp.Component_Code.toLowerCase(), {
                name: comp.Component_Name,
                type: comp.Component_Type
            });
        });

        // Load valid functions with Min_Params and Max_Params
        const functions = await dbConnection('functions_master')
            .select('Name', 'Min_Params', 'Max_Params', 'Category');

        functions.forEach(func => {
            validationCache.functions.set(func.Name.toLowerCase(), {
                minParams: func.Min_Params,
                maxParams: func.Max_Params,
                category: func.Category
            });
        });

        // Load valid operators
        const operators = await dbConnection('operators_master')
            .select('Symbol');

        operators.forEach(op => {
            validationCache.operators.add(op.Symbol);
        });

        validationCache.initialized = true;
    } catch (error) {
        console.error('Error initializing validation cache:', error);
        throw error;
    }
}

/**
 * Main formula validation function
 * @param {Object} dbConnection - Database connection (optional for frontend)
 * @param {string} formula - Formula to validate
 * @returns {Object} - Validation result with status and errors
 */
async function validateFormula(dbConnection, formula) {
    // Initialize cache if not done and dbConnection is available
    if (!validationCache.initialized && dbConnection) {
        await initializeValidationCache(dbConnection);
    }

    const result = { isValid: true, errors: [] };

    try {
        // Step 1: Basic validation
        if (!formula || formula.trim().length === 0) {
            result.isValid = false;
            result.errors.push('IVE0721');
            return result;
        }

        // Step 2: Syntax validation (parentheses, basic patterns)
        //         Parentheses are balanced.
        //         Formula doesn’t start/end with bad operators.
        //         No invalid repeated operators (++, --, etc.).
        //         No empty parentheses.
        const syntaxResult = validateSyntax(formula);
        if (!syntaxResult.isValid) {
            result.isValid = false;
            result.errors.push(...syntaxResult.errors);
        }

        // Step 3: Semantic validation
        const semanticResult = validateSemantics(formula);
        if (!semanticResult.isValid) {
            result.isValid = false;
            result.errors.push(...semanticResult.errors);
        }

        // Step 4: Function parameter validation (always run for built-in functions)
        const functionResult = validateFunctionParameters(formula);
        if (!functionResult.isValid) {
            result.isValid = false;
            result.errors.push(...functionResult.errors);
        }

        // Step 5: Business logic validation
        const businessResult = validateBusinessLogic(formula);
        if (!businessResult.isValid) {
            result.isValid = false;
            result.errors.push(...businessResult.errors);
        }

        return result;
    } catch (error) {
        console.error('Error in formula validation:', error);
        result.isValid = false;
        result.errors.push('IVE0730');
        return result;
    }
}

/**
 * Step 2: Validate formula syntax (parentheses, basic patterns)
 * @param {string} formula - Formula to validate
 * @returns {Object} - Validation result
 */
function validateSyntax(formula) {
    const result = { isValid: true, errors: [] };

    // Check for balanced parentheses
    let parenthesesCount = 0;
    for (let char of formula) {
        if (char === '(') parenthesesCount++;
        if (char === ')') parenthesesCount--;
        if (parenthesesCount < 0) {
            result.isValid = false;
            result.errors.push('IVE0727');
            break;
        }
    }

    if (parenthesesCount !== 0) {
        result.isValid = false;
        result.errors.push('IVE0727');
    }

    // Check for basic syntax patterns
    const invalidPatterns = [
        /\+(\s*\+)+/,  // Multiple plus signs (2 or more)
        /-(\s*-)+/,    // Multiple minus signs (2 or more)
        /\*(\s*\*)+/,  // Multiple multiply signs (2 or more)
        /\/(\s*\/)+/,  // Multiple divide signs (2 or more)
        /(?<![a-zA-Z0-9_])\(\s*\)/,     // Empty parentheses (not preceded by word characters)
        /\)\s*\(/,     // Invalid parentheses sequence like ))(
        /[+\-*/]\s*$/,  // Ending with operator
        /^[+*/]/,      // Starting with operator (except minus)
    ];

    for (let pattern of invalidPatterns) {
        if (pattern.test(formula)) {
            result.isValid = false;
            result.errors.push('IVE0721'); // IVE0721: Invalid formula syntax
            break;
        }
    }

    return result;
}

/**
 * Step 3: Validate formula semantics (component references, functions, operators)
 * @param {string} formula - Formula to validate
 * @returns {Object} - Validation result
 */
function validateSemantics(formula) {
    const result = { isValid: true, errors: [] };

    // Get built-in function names from the single source of truth
    const builtInFunctionNames = Object.keys(BUILT_IN_FUNCTIONS);

    // Extract component references (words that are not functions or numbers)
    // First, remove quoted strings to avoid matching string literals as components
    const formulaWithoutQuotes = formula.replace(/'[^']*'/g, '').replace(/"[^"]*"/g, '');

    const componentPattern = /\b[a-zA-Z][a-zA-Z0-9_]*\b/g;
    const matches = formulaWithoutQuotes.match(componentPattern) || [];

    for (let match of matches) {
        const lowerMatch = match.toLowerCase();

        // Skip if it's a pure number (starts with digit)
        if (/^\d/.test(match)) {
            continue;
        }

        // Skip common keywords that aren't components
        const keywords = ['true', 'false', 'null', 'undefined'];
        if (keywords.includes(lowerMatch)) {
            continue;
        }

        // Check if it's a built-in function FIRST (before checking database)
        if (builtInFunctionNames.includes(lowerMatch)) {
            continue; // Built-in functions are always valid
        }

        // Skip if it's a database function
        if (validationCache.functions.has(lowerMatch)) {
            continue;
        }

        // Check if it's a valid component in the database (includes all types: EARNING, LEAVE, etc.)
        if (validationCache.initialized) {
            // Cache is initialized, check against database components
            if (!validationCache.components.has(lowerMatch)) {
                result.isValid = false;
                result.errors.push('IVE0722'); // IVE0722: Unknown component reference in formula
                break;
            }
        }
        // If cache is not initialized, we can't validate database components
        // but we allow the validation to pass for now (will be caught at runtime)
    }

    // Extract and validate function calls
    const functionPattern = /([a-zA-Z][a-zA-Z0-9_]*)\s*\(/g;
    let functionMatch;
    while ((functionMatch = functionPattern.exec(formula)) !== null) {
        const funcName = functionMatch[1].toLowerCase();

        // Check if it's a built-in function
        if (Object.keys(BUILT_IN_FUNCTIONS).includes(funcName)) {
            continue; // Built-in functions are always valid
        }

        // Check if it's in the database functions (only if cache is initialized)
        if (validationCache.initialized && !validationCache.functions.has(funcName)) {
            result.isValid = false;
            result.errors.push('IVE0723'); // IVE0723: Invalid function usage in formula
            break;
        }

        // If cache is not initialized, we can't validate database functions
        // but we allow the validation to pass for now (will be caught at runtime)
    }

    return result;
}

/**
 * Step 4: Validate function parameters using Min_Params and Max_Params
 * @param {string} formula - Formula to validate
 * @returns {Object} - Validation result
 */
function validateFunctionParameters(formula) {
    const result = { isValid: true, errors: [] };

    // Use the single source of truth for built-in function parameters

    // Extract function calls with their parameters using proper nested parentheses handling
    const functionCalls = extractFunctionCalls(formula);

    for (let funcCall of functionCalls) {
        const funcName = funcCall.name.toLowerCase();
        const paramString = funcCall.params;

        // Count parameters (split by comma, but handle nested parentheses)
        const paramCount = countFunctionParameters(paramString);

        // Check if it's a built-in function
        if (BUILT_IN_FUNCTIONS[funcName]) {
            const funcInfo = BUILT_IN_FUNCTIONS[funcName];

            // Validate parameter count for built-in functions
            if (paramCount < funcInfo.minParams) {
                result.isValid = false;
                result.errors.push('IVE0728'); // Invalid parameter count for function
                break;
            }

            if (paramCount > funcInfo.maxParams) {
                result.isValid = false;
                result.errors.push('IVE0728'); // Invalid parameter count for function
                break;
            }
            continue;
        }

        // Get function info from database cache
        const funcInfo = validationCache.functions.get(funcName);
        if (!funcInfo) {
            continue; // Already validated in semantics
        }

        // Validate parameter count against Min_Params and Max_Params from database
        if (paramCount < funcInfo.minParams) {
            result.isValid = false;
            result.errors.push('IVE0728'); // Invalid parameter count for function
            break;
        }

        if (paramCount > funcInfo.maxParams) {
            result.isValid = false;
            result.errors.push('IVE0728'); // Invalid parameter count for function
            break;
        }
    }

    return result;
}

/**
 * Extract function calls with proper nested parentheses handling
 * @param {string} formula - Formula to parse
 * @returns {Array} - Array of {name, params} objects
 */
function extractFunctionCalls(formula) {
    const functions = [];
    const functionNamePattern = /[a-zA-Z][a-zA-Z0-9_]*\s*\(/g;
    let match;

    while ((match = functionNamePattern.exec(formula)) !== null) {
        const startIndex = match.index;
        const funcName = match[0].replace(/\s*\($/, '');
        const openParenIndex = formula.indexOf('(', startIndex);

        // Find matching closing parenthesis
        let level = 0;
        let endIndex = -1;

        for (let i = openParenIndex; i < formula.length; i++) {
            if (formula[i] === '(') {
                level++;
            } else if (formula[i] === ')') {
                level--;
                if (level === 0) {
                    endIndex = i;
                    break;
                }
            }
        }

        if (endIndex !== -1) {
            const paramString = formula.substring(openParenIndex + 1, endIndex);
            functions.push({
                name: funcName,
                params: paramString,
                startIndex: startIndex,
                endIndex: endIndex
            });
        }
    }

    return functions;
}

/**
 * Count function parameters handling nested parentheses
 * @param {string} paramString - Parameter string
 * @returns {number} - Parameter count
 */
function countFunctionParameters(paramString) {
    if (!paramString || paramString.trim().length === 0) {
        return 0;
    }

    let paramCount = 0;
    let parenthesesLevel = 0;
    let currentParam = '';

    for (let char of paramString) {
        if (char === '(') {
            parenthesesLevel++;
            currentParam += char;
        } else if (char === ')') {
            parenthesesLevel--;
            currentParam += char;
        } else if (char === ',' && parenthesesLevel === 0) {
            if (currentParam.trim().length > 0) {
                paramCount++;
            }
            currentParam = '';
        } else {
            currentParam += char;
        }
    }

    // Count the last parameter
    if (currentParam.trim().length > 0) {
        paramCount++;
    }

    return paramCount;
}

/**
 * Step 5: Validate business logic constraints
 * @param {string} formula - Formula to validate
 * @returns {Object} - Validation result
 */
function validateBusinessLogic(formula) {
    const result = { isValid: true, errors: [] };

    // Check formula complexity (length limit)
    if (formula.length > 1000) {
        result.isValid = false;
        result.errors.push('IVE0730');
    }

    // Check nesting depth
    let maxDepth = 0;
    let currentDepth = 0;
    for (let char of formula) {
        if (char === '(') {
            currentDepth++;
            maxDepth = Math.max(maxDepth, currentDepth);
        }
        if (char === ')') {
            currentDepth--;
        }
    }

    if (maxDepth > 10) {
        result.isValid = false;
        result.errors.push('IVE0730');
    }

    return result;
}

module.exports = {
    validateFormula,
    initializeValidationCache,
    validateSyntax,
    validateSemantics,
    validateFunctionParameters,
    validateBusinessLogic,
    extractFunctionCalls,
    countFunctionParameters
};


