const { ehrTables } = require("../../common/tablealias");
const knex = require("knex");
const { ApolloError } = require("apollo-server-lambda");
const commonLib = require("@cksiva09/hrapp-corelib").CommonLib;
const { getMainBranchLocation, getCompanyDetails, getTaxDetails, getMonthSequence, formatFieldBasedPadding } = require("../../common/reportsCommonFunctions");

module.exports.getPND3Report = async (parent, args, context, info) => {
  const organizationDbConnection = knex(context.connection.OrganizationDb);
  const appManagerDbConnection = knex(context.connection.AppManagerDb);
  let errorMessage;
  try {
    let [
      employeeDetails,
      companyDetails,
      taxDetails,
      companyLocation,
      tdsPayment
    ] = await Promise.all([
      getEmployeeDetails(organizationDbConnection,  args),
      getCompanyDetails(organizationDbConnection),
      getTaxDetails(organizationDbConnection, args),
      getMainBranchLocation(
        organizationDbConnection,
        context.Org_Code,
        appManagerDbConnection,
        args
      ),
      organizationDbConnection(ehrTables.tdsPayment + " as TDSP")
      .select(
        "TDSPT.Payment_Date as paymentDate",
      )
      .innerJoin(
        ehrTables.tdsPaymentTracker + " as TDSPT",
        "TDSPT.Payment_Id",
        "TDSP.Payment_Id"
      )
      .where("TDSP.Salary_Month", args.payRollMonth),
    ]);
    if((!employeeDetails.length || !companyDetails.length || !taxDetails.length || !companyLocation || !tdsPayment.length)){
      if (!employeeDetails.length) {
        errorMessage = "Payslip details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!tdsPayment.length) {
        errorMessage = "TDS payment details are unavailable. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!companyDetails.length) {
        errorMessage = "Company details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!taxDetails.length) {
        errorMessage = "Tax details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    if (!companyLocation) {
        errorMessage = "Company location details for this month are missing. Please generate them before proceeding.";
        throw 'PFF0018';
    }
    }
    let name = " ".repeat(fieldLengths.name);
    if (args.serviceProviderId && companyLocation.organizationName) {
      name = formatFieldBasedPadding(
        companyLocation.organizationName || "",
        fieldLengths.name
      );
    } else if (companyDetails.length > 0) {
      name = formatFieldBasedPadding(
        companyDetails[0].Org_Name || "",
        fieldLengths.name
      );
    }
    companyLocation = companyLocation.location;
    const pnd3Details={
        Sequence_Number:formatFieldBasedPadding(1,fieldLengths.sequenceNumber),
        Amount_Paid: formatFieldBasedPadding(employeeDetails[0].taxableSalaryCount || 0,fieldLengths.taxableSalaryCount),
        Amount_of_Tax_Deducted:formatFieldBasedPadding(employeeDetails[0].deductionSalaryCount ||0,fieldLengths.deductionSalaryCount),
        Name:  formatFieldBasedPadding(String(name || ""), fieldLengths.name),
        Taxpayer_Identification_Number: taxDetails.length > 0 ? formatFieldBasedPadding(String(taxDetails[0].TAN || "") , fieldLengths.taxpayerIdentificationNumber): ' '.repeat(fieldLengths.taxpayerIdentificationNumber),
        Street: companyLocation.length > 0 ? formatFieldBasedPadding(String(companyLocation[0].Street1 || ""), fieldLengths.street1) : ' '.repeat(fieldLengths.street1),
        Building_Name: companyLocation.length > 0 ? formatFieldBasedPadding(String(companyLocation[0].Street2 || ""), fieldLengths.street2) : ' '.repeat(fieldLengths.street2),
        District: companyLocation.length > 0 ? formatFieldBasedPadding(String(companyLocation[0].City_Name || ""), fieldLengths.cityName) : ' '.repeat(fieldLengths.cityName),
        Province: companyLocation.length > 0 ? formatFieldBasedPadding(String(companyLocation[0].State_Name || ""), fieldLengths.stateName) : ' '.repeat(fieldLengths.stateName),
        Postal_Code: companyLocation.length > 0 ? formatFieldBasedPadding(String(companyLocation[0].Pincode || ""), fieldLengths.pincode) : ' '.repeat(fieldLengths.pincode),
        Payment_Date: tdsPayment.length > 0 ? tdsPayment[0].paymentDate || "" : ' '.repeat(fieldLengths.paymentDate),
        Tax_Rate: '5%',
        Tax_Deduction_Conditions: ' Tax is deducted at the time of payment',
        Blank: ' '.repeat(fieldLengths.Blank)
    }
    const pnd3Reports = {
      reportType: "PND 3 Report",
      pnd3Details:JSON.stringify(pnd3Details),
      errorCode: "",
      message: "PND3 Report has been fetched successfully.",
    };

    organizationDbConnection.destroy();
    appManagerDbConnection.destroy();

    return pnd3Reports;
  } catch (err) {
    organizationDbConnection.destroy();
    appManagerDbConnection.destroy();
    console.error("Error in getPND3Report function main catch block.", err);
    if(err == "PFF0018"){
      throw new ApolloError(errorMessage, err);
    }
    throw new ApolloError(commonLib.func.getError(err, "PFF0017").message, commonLib.func.getError(err, "PFF0017").code);
  }
};

async function getEmployeeDetails(organizationDbConnection, args) {
    return organizationDbConnection(ehrTables.empJob + " as EJ")
      .select(
        organizationDbConnection.raw(
          "COALESCE(SUM(MFS.Current_Month_Taxable_Salary), 0) as taxableSalaryCount"
        ),
        organizationDbConnection.raw(
          "COALESCE(SUM(SD.Deduction_Amount), 0) as deductionSalaryCount"
        )
      )
      .modify((queryBuilder) => {
        if (args.serviceProviderId) {
          queryBuilder.where("EJ.Service_Provider_Id", args.serviceProviderId);
        }
      })
      .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "EJ.Employee_Id")
      .leftJoin(ehrTables.contactDetails + " as CD", "CD.Employee_Id", "EPI.Employee_Id")
      .innerJoin(ehrTables.salaryPayslip + " as SP", "SP.Employee_Id", "EJ.Employee_Id")
      .where("SP.Salary_Month",  args.payRollMonth)
      .innerJoin(ehrTables.salaryDeduction + " as SD", "SP.Payslip_Id", "SD.Payslip_Id")
      .innerJoin(
        ehrTables.monthlyForm16Snapshot + " as MFS",
        "MFS.Payslip_Id",
        "SP.Payslip_Id"
      );
  }
const fieldLengths = {
  sequenceNumber:5,
  taxpayerIdentificationNumber:13,
  name:320,
  street1:60,
  street2:60,
  cityName:100,
  stateName:100,
  countryName:100,
  pincode:5,
  taxableSalaryCount:15,
  deductionSalaryCount:15,
  Blank:27
};