// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
// Organization database connection
const knex = require('knex');
// require common constant files
const constants = require('../common/appconstants');
// require table alias
const {ehrTables} = require('../common/tablealias');
// require validation file
const {salaryConfigurationValidation}=require('../formvalidations/salaryTemplateInputValidation')

// variable declarations
let errResult = {};
let organizationDbConnection = '';
let validationError={};
let checkRights=false;
let action='add';

// resolver definition
const resolvers = {
    Mutation: {
        // function to update salary configuration
        updateSalaryConfiguration: async (parent, args, context, info) => {
            try{
                console.log('Inside updateSalaryConfiguration function');
                // variable declarations
                let loggedInEmpId=context.logInEmpId;
                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                let isEmpSettlementInitiated = await commonLib.payroll.empSettlementInitiated(organizationDbConnection,args.employeeId);
                //If the full and final settlement is initiated for the employee
                if(isEmpSettlementInitiated===1){
                    throw 'PFF0011';
                }else{
                    // formation of inputs
                    /** As of now we are enabling Eligible for pt and eligible for pension flags only */

                    let overtimeId = null;
                    //If the eligible for overtime is enabled then get the overtime id
                    if(args && args.eligibleForOvertime){
                        overtimeId = await organizationDbConnection(ehrTables.overtimeDetails)
                                        .select('Overtime_Id')
                                        .then(result => { 
                                            if(result && result.length > 0){
                                                return result[0].Overtime_Id;
                                            }else{
                                                return 0;
                                            }
                                        })
                                        .catch(function(error){throw error;});
                    }
                    let params={
                        Employee_Id           :args.employeeId,
                        Eligible_For_Overtime :args.eligibleForOvertime ? args.eligibleForOvertime : 0,
                        Overtime_Slab         : overtimeId,
                        Eligible_For_Pf       :args.eligibleForPf ? args.eligibleForPf : 0,
                        Eligible_For_Pension  :args.eligibleForPension,
                        Exempt_EDLI           :args.exemptEDLI ? args.exemptEDLI : 0,
                        UAN                   :(args.UAN)?args.UAN:null,
                        Pf_PolicyNo           :(args.PfPolicyNo)?args.PfPolicyNo:null,
                        Eligible_For_ESI      :args.eligibleForESI ? args.eligibleForESI : 0,
                        ESI_Number            :(args.ESINumber)?args.ESINumber:null,
                        Eligible_For_Insurance:args.eligibleForInsurance ? args.eligibleForInsurance : 0,
                        Eligible_For_Nps      :args.eligibleForNps ? args.eligibleForNps : 0,
                        Nps_Number            :(args.NpsNumber)?args.NpsNumber:null,
                        Eligible_For_Gratuity :args.eligibleForGratuity ? args.eligibleForGratuity : 0,
                        Eligible_For_PT       :args.eligibleForPT,      
                        Eligible_For_Vpf      :args.eligibleForVpf?args.eligibleForVpf:0,
                        Vpf_Type              :args.vpfType?args.vpfType:null,
                        Vpf_Employee_Share    :args.vpfEmployeeShare?args.vpfEmployeeShare:null,
                        Vpf_Employee_Share_Amount: args.vpfEmployeeShareAmount?args.vpfEmployeeShareAmount:null,
                        Bond_Recovery_Applicable: args.bondRecoveryApplicable ? args.bondRecoveryApplicable: 'No',
                        Minimum_Months_To_Be_Served: args.minimumMonthsToBeServed ? args.minimumMonthsToBeServed: null,
                        Bond_Value: args.bondValue ? args.bondValue: null
                    }

                    return(
                        organizationDbConnection
                        .transaction(function(trx){
                            return(
                                // check whether the salary configuration exist for that employee
                                organizationDbConnection(ehrTables.employeeSalaryConfiguration)
                                .where('Employee_Id',args.employeeId)
                                .transacting(trx)
                                .then(async(getDetails) => {
                                    // check whether data exist or not
                                    if(getDetails.length>0){
                                        action='edit';
                                        // Only admin can update the salary configuration details
                                        checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loggedInEmpId, constants.formName.employeeDetails, '','UI');
                                        if (Object.keys(checkRights).length <= 0) {
                                            // throw error if update rights is not exists
                                            throw ('_DB0102');
                                        }else{
                                            if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1 && checkRights.Employee_Role.toLowerCase() === 'admin') {
                                                // validate the inputs
                                                validationError=await salaryConfigurationValidation(args);
                                                if (Object.keys(validationError).length === 0) {
                                                    // calculate timezone based on loggedInEmpId
                                                    let employeeTimeZone= await commonLib.func.getEmployeeTimeZone(loggedInEmpId, organizationDbConnection, 1);
                                                    // form parameters
                                                    params.Updated_On= employeeTimeZone,
                                                    params.Updated_By=loggedInEmpId
                                                    
                                                    return(
                                                        organizationDbConnection(ehrTables.employeeSalaryConfiguration)
                                                        .update(params)
                                                        .where('Employee_Id',args.employeeId)
                                                        .transacting(trx)
                                                        .then(async(updateTemplateData) =>{
                                                            // function to insert system log
                                                            await includeSystemLog(action,context.userIp,organizationDbConnection,loggedInEmpId,args.employeeId);
                                                            return { errorCode : '',message : 'Salary configuration updated successfully' };
                                                        })
                                                    )
                                                }else{
                                                    // throw validation error
                                                    throw 'IVE0000';
                                                }
                                            }else {
                                                // throw error
                                                throw ('_DB0102');
                                            }
                                        }
                                    }
                                    else{
                                        // Only admin can add the salary configuration details
                                        checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loggedInEmpId, constants.formName.employeeDetails, '','UI');
                                        if (Object.keys(checkRights).length <= 0) {
                                            // throw error if add rights is not exists
                                            throw ('_DB0101');
                                        }else{
                                            if (Object.keys(checkRights).length > 0 && checkRights.Role_Add === 1 && checkRights.Employee_Role.toLowerCase() === 'admin') {
                                                // validate the inputs
                                                validationError=await salaryConfigurationValidation(args);
                                                if (Object.keys(validationError).length === 0) {
                                                    // calculate timezone based on loggedInEmpId
                                                    let employeeTimeZone= await commonLib.func.getEmployeeTimeZone(loggedInEmpId, organizationDbConnection, 1);
                                                    // form parameters
                                                    params.Added_On= employeeTimeZone,
                                                    params.Added_By=loggedInEmpId
                                                    return(
                                                        // insert record in employee salary configuration table
                                                        organizationDbConnection(ehrTables.employeeSalaryConfiguration)
                                                        .insert(params)
                                                        .transacting(trx)
                                                        .then(async(insertRecord) =>{
                                                            // function to insert system log
                                                            await includeSystemLog(action,context.userIp,organizationDbConnection,loggedInEmpId,args.employeeId);
                                                            return { errorCode : '',message : 'Salary configuration added successfully' };
                                                        })
                                                    );
                                                }
                                                else{
                                                    // throw validation error
                                                    throw 'IVE0000';
                                                }
                                            }else {
                                                // throw error
                                                throw ('_DB0101');
                                            }
                                        }
                                    }    
                                })
                                .then(function (result) {
                                    return result;
                                })
                                //catch db-connectivity errors
                                .catch(function (catchError) {
                                    console.log('Error in updateSalaryConfiguration function .catch() block', catchError);
                                    // return error code based on action
                                    let errorCode=(action==='edit')?'PST0104':'PST0105';
                                    errResult = commonLib.func.getError(catchError, errorCode);
                                    throw new ApolloError(errResult.message,errResult.code);
                                })
                                /**close database connection */
                                .finally(() => {
                                    organizationDbConnection.destroy();
                                })
                            );
                        })
                    );
                }
            } catch (updateSalaryConfigurationMainCatch){
                console.log('Error in updateSalaryConfiguration function main catch block',updateSalaryConfigurationMainCatch);
                // destroy database connection
                (organizationDbConnection)?organizationDbConnection.destroy():'';
                if (updateSalaryConfigurationMainCatch === 'IVE0000') {
                    errResult = commonLib.func.getError('', updateSalaryConfigurationMainCatch);
                    // return response
                    throw new UserInputError(errResult.message,{validationError: validationError});
                }
                else{
                    // return error code based on action
                    let errorCode=(action==='edit')?'PST0011':'PST0012';
                    errResult = commonLib.func.getError(updateSalaryConfigurationMainCatch, errorCode);
                    throw new ApolloError(errResult.message,errResult.code);
                }
            }
        }
    }
};

// function to insert system log
async function includeSystemLog(action,userIp,organizationDbConnection,loggedInEmpId,employeeId){
    try{
        let systemLogParams={};
        systemLogParams = {
            action : (action==='edit')?constants.systemLogs.roleUpdate:constants.systemLogs.roleAdd,
            userIp : userIp,
            employeeId: loggedInEmpId,
            formName : constants.formName.employeeDetails,
            trackingColumn: 'configuration',
            organizationDbConnection: organizationDbConnection,
            uniqueId : employeeId
        }
        // call function to update system log activities
        await commonLib.func.createSystemLogActivities(systemLogParams);
        return 'success';
    }
    catch(error){
        console.log('Error in includeSystemLog main catch block',error);
        return '';
    }
}
exports.resolvers = resolvers;
