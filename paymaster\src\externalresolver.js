const calculateSalary = require('./roresolvers/salary/calculateSalary');
const calculateSalaryArrears = require('./roresolvers/salary/calculateSalaryArrears');
const listSalaryTemplateDetails = require('./resolvers/listSalaryTemplateDetails');
const addUpdateCandidateSalaryDetails = require('./resolvers/candidateSalary/addUpdateCandidateSalaryDetails');

// Define resolver
const resolvers = {
    Query: Object.assign({},
        listSalaryTemplateDetails.resolvers.Query,
        calculateSalary,
        calculateSalaryArrears
    ),
    Mutation: Object.assign({},
        addUpdateCandidateSalaryDetails.resolvers.Mutation
    )
}
exports.resolvers = resolvers;

