const { ApolloServer, gql } = require('apollo-server-lambda');
const { resolvers } = require('./externalresolver');
const path = require("path");
const fs = require('fs');
const typeDefs = gql(fs.readFileSync(path.resolve()+'/src/'+'externalschema.graphql', 'utf8'));
// require common hrapp-corelib functions
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

module.exports.graphql = (event, context, callback) => {
    context.callbackWaitsForEmptyEventLoop = false; //to send the response immediately when callback executes

    // get customAuthorizerData from firebase authorize and  return it to resolver if exists.
    let idToken = event.requestContext.authorizer ? (event.requestContext.authorizer.idToken ? event.requestContext.authorizer.idToken : '') : '';
    let refreshToken = event.requestContext.authorizer ? (event.requestContext.authorizer.refreshToken ? event.requestContext.authorizer.refreshToken : '') : '';
    let partnerid = (event.headers.partnerid && event.headers.partnerid !== 'null' && event.headers.partnerid !== 'undefined') ? event.headers.partnerid : '-';

    // Create object for ApolloServer
    const server = new ApolloServer({
        typeDefs,
        resolvers,
        context: async ({ event }) => {
            let contextData = await commonLib.func.getContextDataWithoutEmployeeId(event, 1, 'ro');
            contextData.Employee_Id = event.headers.Employee_Id ? event.headers.Employee_Id : null; 
            contextData.partnerid = event.headers.partnerid ? event.headers.partnerid : event.headers.Partnerid ?  event.headers.Partnerid : '-'
            //return header to resolver function
            return {...contextData};
        }
    });

    const handler = server.createHandler({
        cors: {
            method: 'POST',
            allowHeaders: '*'
        },
        // Add body parser configuration
        expressGetMiddlewareOptions: {
            bodyParserConfig: {
                limit: '6mb',
                type: 'application/json'
            }
        }
    });
    
    function callbackFilter(error, output) {
        // We are appending the idToken and refreshToken in the response. While running this in local this is not returning the response
        // so here checked the stagename as local or not. If it is local then we will no append the token response. 
        // Otherwise token response will be append and response will be returned
        // If any doubts check this task #3794
        if(process.env.stageName !== 'local'){
            // parse the response data
            let responseData = JSON.parse(output.body);
            // push idToken and refresh token into an json object
            let identityToken = {
                idToken: idToken,
                refreshToken: refreshToken
            }
            // return the idToken and refreshTOken to UI
            responseData.identityToken = identityToken;
            output.body = JSON.stringify(responseData);
        }
        
        output.headers['Access-Control-Allow-Origin'] = '*';
        output.headers['Access-Control-Allow-Credentials'] = true;
        callback(error, output);
    }
    return handler(event, context, callbackFilter);
};

