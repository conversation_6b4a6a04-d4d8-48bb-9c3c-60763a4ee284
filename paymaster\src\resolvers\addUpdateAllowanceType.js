// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const { formId } = require('../common/appconstants');
const { validateCommonRuleInput } = require('../common/commonfunctions');
const { validateFormula } = require('../common/formulaValidation');

// resolver definition
const resolvers = {
    Mutation: {
        // function to delete allowance type
        deleteAllowanceType: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Validate required inputs
                if (!args.allowanceTypeId) {
                    throw 'IVE0681'; // Allowance Type ID is required
                }

                if (!args.formId || ![381, 382, 383].includes(args.formId)) {
                    throw 'IVE0658'; // Invalid formId
                }

                const accessFormId = args.formId;
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    accessFormId
                );
                if (Object.keys(checkRights).length === 0 || checkRights.Role_Delete !== 1) {
                    throw '_DB0103'; // Delete access denied
                }

                // Perform deletion with all business logic checks
                const result = await deleteAllowanceTypeWithChecks(
                    organizationDbConnection,
                    args.allowanceTypeId,
                    loggedInEmpId
                );

                return result;
            }
            catch (mainCatchError) {

                if (mainCatchError === 'IVE0681' || mainCatchError === 'IVE0658' ||
                    mainCatchError === 'IVE0682' || mainCatchError === 'IVE0683' ||
                    mainCatchError === 'IVE0684' || mainCatchError === 'IVE0685' ||
                    mainCatchError === 'IVE0686' || mainCatchError === 'IVE0687' ||
                    mainCatchError === 'IVE0688') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new UserInputError(errResult.message1, { errorCode: mainCatchError });
                } else if (mainCatchError === '_DB0100' || mainCatchError === '_DB0103') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new ApolloError(errResult.message, errResult.code);
                } else {
                    const errResult = commonLib.func.getError(mainCatchError, 'PST0027');
                    throw new ApolloError(errResult.message, errResult.code);
                }
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        },

        // function to add/update allowance type
        addUpdateAllowanceType: async (parent, args, context, info) => {
            let organizationDbConnection;
            let validationError = {};
            try {
                const loggedInEmpId = context.logInEmpId;
                const isEditMode = !!args.allowanceTypeId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // Validate formId
                if (!args.formId || ![381, 382, 383].includes(args.formId)) {
                    throw 'IVE0658';
                }

                // Determine formId for access rights (109 for allowance types)
                const accessFormId = args.formId;

                // check access right based on employeeid
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    accessFormId
                );
                if (Object.keys(checkRights).length === 0) {
                    throw '_DB0104';
                }

                if (isEditMode && checkRights.Role_Update !== 1) {
                    throw '_DB0102';
                } else if (!isEditMode && checkRights.Role_Add !== 1) {
                    throw '_DB0101';
                }

                // Input validation using validateCommonRuleInput like in addUpdateEmployeeTravel.js
                const fieldValidations = {};

                // Map fields to validation rules (similar to employee travel)
                if (args.allowanceName) fieldValidations.allowanceName = 'IVE0659';
                if (args.nameInPayslip) fieldValidations.nameInPayslip = 'IVE0659'; // Same validation as allowanceName
                if (args.description) fieldValidations.description = 'IVE0662';
                if (args.allowanceType === 'Amount' && args.amount ) fieldValidations.amount = 'IVE0027';
                if ( args.allowanceType === 'Percentage' && args.percentage) fieldValidations.percentage = 'IVE0691';
                // Skip customFormula from common validation as we handle it separately below
                validationError = await validateCommonRuleInput(args, fieldValidations);
                // Formula validation for Custom Formula type
                const calcType = args.calculationType || args.allowanceType;
                if (calcType === 'Custom Formula') {
                    if (!args.customFormula || args.customFormula.trim().length === 0) {
                        validationError['IVE0721'] = commonLib.func.getError('', 'IVE0721').message;
                    } else {
                        // Validate formula syntax and semantics
                        const formulaValidationResult = await validateFormula(organizationDbConnection, args.customFormula);
                        if (!formulaValidationResult.isValid) {
                            const firstError = formulaValidationResult.errors[0];
                            validationError[firstError] = commonLib.func.getError('', firstError).message;
                        }
                    }
                }

                // Create a copy of args without customFormula for common validation
                const argsForCommonValidation = { ...args };
                delete argsForCommonValidation.customFormula;

                const commonValidationErrors = await validateCommonRuleInput(argsForCommonValidation, fieldValidations);
                // Merge common validation errors with existing validation errors (including formula errors)
                Object.assign(validationError, commonValidationErrors);

                if (Object.keys(validationError).length > 0) {
                    const firstErrorCode = Object.keys(validationError)[0];
                    throw firstErrorCode;
                }

                // Additional specific validations
                await validateAllowanceTypeInput(args, validationError, organizationDbConnection, isEditMode);
                if (Object.keys(validationError).length > 0) {
                    const firstErrorCode = Object.keys(validationError)[0];
                    throw firstErrorCode;
                }

                // Business logic validations
                await performBusinessLogicValidations(organizationDbConnection, args, isEditMode, validationError);
                if (Object.keys(validationError).length > 0) {
                    const firstErrorCode = Object.keys(validationError)[0];
                    throw firstErrorCode;
                }

                // Process add/update operation
                const result = await processAllowanceType(organizationDbConnection, args, loggedInEmpId, isEditMode);

                // return response
                return {
                    errorCode: '',
                    message: isEditMode ? 'Allowance type updated successfully.' : 'Allowance type added successfully.',
                    success: true,
                    allowanceTypeId: result.allowanceTypeId
                };
            }
            catch (mainCatchError) {

                // Check if it's a validation error (starts with IVE)
                if (typeof mainCatchError === 'string' && mainCatchError.startsWith('IVE')) {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new UserInputError(errResult.message1 || errResult.message, { errorCode: mainCatchError });
                } else {
                    const errResult = commonLib.func.getError(mainCatchError, 'PST0027');
                    throw new ApolloError(errResult.message, errResult.code);
                }
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

// Validate allowance type specific inputs (following addUpdateSalaryDetails.js pattern)
async function validateAllowanceTypeInput(args, validationError,dbConnection,isEditMode) {
    // Validate enum fields using the same format as addUpdateSalaryDetails.js
    const validTaxInclusion = ['Taxable', 'Non Taxable'];
    if (args.taxInclusion && !validTaxInclusion.includes(args.taxInclusion)) {
        validationError['IVE0664'] = commonLib.func.getError('', 'IVE0664').message1;
    }

    const validFormulaBased = ['Yes', 'No'];
    if (args.formulaBased && !validFormulaBased.includes(args.formulaBased)) {
        validationError['IVE0665'] = commonLib.func.getError('', 'IVE0665').message1;
    }

    const validCalculationType = ['Amount', 'Percentage', 'Custom Formula'];
    if (args.allowanceType && !validCalculationType.includes(args.allowanceType)) {
        validationError['IVE0713'] = commonLib.func.getError('', 'IVE0713').message1;
    }

    const validAllowanceMode = ['Bonus', 'Non Bonus'];
    if (args.allowanceMode && !validAllowanceMode.includes(args.allowanceMode)) {
        validationError['IVE0666'] = commonLib.func.getError('', 'IVE0666').message1;
    }

    const validPeriod = ['Monthly', 'Quarterly', 'HalfYearly', 'Annually'];
    if (args.period && !validPeriod.includes(args.period)) {
        validationError['IVE0667'] = commonLib.func.getError('', 'IVE0667').message1;
    }

    const validYesNo = ['Yes', 'No'];
    if (args.isFlexiBenefitPlan && !validYesNo.includes(args.isFlexiBenefitPlan)) {
        validationError['IVE0668'] = commonLib.func.getError('', 'IVE0668').message1;
    }

    if (args.isClaimFromReimbursement && !validYesNo.includes(args.isClaimFromReimbursement)) {
        validationError['IVE0669'] = commonLib.func.getError('', 'IVE0669').message1;
    }

    const validStatus = ['Active', 'Inactive'];
    if (args.allowanceTypeStatus && !validStatus.includes(args.allowanceTypeStatus)) {
        validationError['IVE0670'] = commonLib.func.getError('', 'IVE0670').message1;
    }

    // Validate reimbursement type enum values
    if (args.reimbursementType) {
        const validReimbursementTypes = [
            'Fuel Reimbursement',
            'Driver Reimbursement',
            'Telephone Reimbursement',
            'Leave Travel Allowance',
            'Vehicle Maintenance Reimbursement'
        ];
        
        if (!validReimbursementTypes.includes(args.reimbursementType)) {
            validationError['IVE0671'] = commonLib.func.getError('', 'IVE0671').message1;
        }
    }

    // Validate unclaimed LTA enum values
    if (args.unclaimedLTA) {
        const validUnclaimedLTA = ['Carry Forward Unclaimed LTA', 'Encash Unclaimed LTA'];
        if (!validUnclaimedLTA.includes(args.unclaimedLTA)) {
            validationError['IVE0672'] = commonLib.func.getError('', 'IVE0672').message1;
        }
    }

    // Validate EPF contribution enum values
    if (args.epfContribution) {
        const validEPFContribution = ['Always', 'Only when PF Wage is less than ₹15,000'];
        if (!validEPFContribution.includes(args.epfContribution)) {
            validationError['IVE0673'] = commonLib.func.getError('', 'IVE0673').message1;
        }
    }

    // Validate benefit association
    if (args.benefitAssociation && Array.isArray(args.benefitAssociation)) {
        for (const formId of args.benefitAssociation) {
            if (!Number.isInteger(formId) || formId <= 0) {
                validationError['IVE0674'] = commonLib.func.getError('', 'IVE0674').message1;
                break;
            }
        }
    }

    // These validations are now handled in validateAllowanceTypeInput function
    // Removed duplicate validations to avoid conflicts

    // Business logic validations
    await performBusinessLogicValidations(dbConnection, args, isEditMode, validationError);

    // Additional validation: Check if trying to inactivate allowance type that's used in active salary templates
    if (isEditMode && args.allowanceTypeStatus.toLowerCase() === 'inactive' && args.allowanceTypeId) {
        const activeTemplateUsage = await dbConnection(ehrTables.salaryTemplate + ' as ST')
            .select('ST.Template_Id', 'ST.Template_Name')
            .innerJoin(ehrTables.templateAllowanceComponents + ' as TAC', 'TAC.Template_Id', 'ST.Template_Id')
            .where('ST.Template_Status', 'Active')
            .where('TAC.Allowance_Type_Id', args.allowanceTypeId)
            .first();

        // Check if allowance type is used in active salary templates
        if (activeTemplateUsage) {
            validationError['IVE0684'] = commonLib.func.getError('', 'IVE0684').message1;
        }

           // 2. Check if this is the last basic pay or fixed allowance type
            const allowanceTypeWithComponent = await dbConnection(ehrTables.allowanceType + ' as AT')
                .select('SC.Component_Code')
                .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
                .where('AT.Allowance_Type_Id', args.allowanceTypeId)
                .first();

            if (allowanceTypeWithComponent && allowanceTypeWithComponent.Component_Code) {
                const componentCode = allowanceTypeWithComponent.Component_Code?.toLowerCase();

                // Check if this is basic pay or fixed allowance
                if (componentCode === 'basic_salary_amount' || componentCode === 'fixed_allowance_amount') {
                    // Count how many active allowance types have this component
                    const countWithSameComponent = await dbConnection(ehrTables.allowanceType + ' as AT')
                        .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
                        .whereRaw('LOWER(SC.Component_Code) = ?', [componentCode])
                        .where('AT.AllowanceType_Status', 'Active')
                        .count('* as count')
                        .first();

                    // If this is the only one, prevent deletion
                    if (countWithSameComponent.count <= 1) {
                        if (componentCode === 'basic_salary_amount') {
                            validationError['IVE0682'] = commonLib.func.getError('', 'IVE0682').message1; // Cannot delete last basic pay
                        } else if (componentCode === 'fixed_allowance_amount') {
                            validationError['IVE0683'] = commonLib.func.getError('', 'IVE0683').message1; // Cannot delete last fixed allowance
                        }
                    }
                }
            }
    }

    return validationError;
}

// Business logic validations
async function performBusinessLogicValidations(dbConnection, args, isEditMode, validationError) {
    try {
        // Check for duplicate allowance name
        if (args.allowanceName) {
            const duplicateNameQuery = dbConnection(ehrTables.allowanceType)
                .where('Allowance_Name', args.allowanceName.trim())
                .where('AllowanceType_Status', 'Active');

            if (isEditMode) {
                duplicateNameQuery.whereNot('Allowance_Type_Id', args.allowanceTypeId);
            }

            const duplicateName = await duplicateNameQuery.first();
            if (duplicateName) {
                validationError['IVE0677'] = commonLib.func.getError('', 'IVE0677').message1;
            }
        }

        // Check for duplicate reimbursement type
        if (args.isClaimFromReimbursement === 'Yes' && args.reimbursementType) {
            const duplicateReimbursementQuery = dbConnection(ehrTables.allowanceType)
                .where('Reimbursement_Type', args.reimbursementType)
                .where('AllowanceType_Status', 'Active');


            if (isEditMode && args.allowanceTypeId) {
                duplicateReimbursementQuery.whereNot('Allowance_Type_Id', args.allowanceTypeId);
            }

            const duplicateReimbursement = await duplicateReimbursementQuery.first();
            if (duplicateReimbursement) {
                validationError['IVE0678'] = commonLib.func.getError('', 'IVE0678').message1;
            }
        }

        // Validate EPF contribution when PF is in benefit association
        if (args.benefitAssociation && Array.isArray(args.benefitAssociation)) {
            const pfFormId = formId.pfId; // Assuming 29 is PF form ID
            if (args.benefitAssociation.includes(pfFormId) && !args.epfContribution) {
                validationError['IVE0676'] = commonLib.func.getError('', 'IVE0676').message1;
            }
        }

        // Custom component validations
        if (args.isCustomComponent === true) {
            // Check for duplicate component name in salary_components table
            if (args.allowanceName) {
                const duplicateComponent = await dbConnection(ehrTables.salaryComponents)
                    .where('Component_Name', args.allowanceName.trim())
                    .first();

                if (duplicateComponent) {
                    validationError['IVE0737'] = commonLib.func.getError('', 'IVE0737').message1;
                }
            }
        } else if (!args.isCustomComponent || args.isCustomComponent === false) {
            // When not creating custom component, salaryComponentId is required
            if (!args.salaryComponentId) {
                validationError['IVE0738'] = commonLib.func.getError('', 'IVE0738').message1;
            }
        }

    } catch (error) {
        validationError['PST0027'] = commonLib.func.getError('', 'PST0027').message1;
    }
}

// Process allowance type add/update
async function processAllowanceType(dbConnection, args, loggedInEmpId, isEditMode) {
    const trx = await dbConnection.transaction();

    try {
        // Handle custom component creation first if needed
        let finalSalaryComponentId = args.salaryComponentId;
        if (args.isCustomComponent === true && args.allowanceName) {
            finalSalaryComponentId = await createCustomComponent(trx, args);
        }

        // Set default values based on formId
        const allowanceData = setDefaultValues(args);

        // Override salaryComponentId with the custom component ID if created
        if (finalSalaryComponentId) {
            allowanceData.Salary_Component_Id = finalSalaryComponentId;
        }

        // Set audit fields
        const currentTimestamp = new Date();
        if (isEditMode) {
            allowanceData.Updated_By = loggedInEmpId;
            allowanceData.Updated_On = currentTimestamp;
        } else {
            allowanceData.Added_By = loggedInEmpId;
            allowanceData.Added_On = currentTimestamp;
        }

        let allowanceTypeId;

        if (isEditMode) {
            // Update existing allowance type
            await trx(ehrTables.allowanceType)
                .where('Allowance_Type_Id', args.allowanceTypeId)
                .update(allowanceData);

            allowanceTypeId = args.allowanceTypeId;
        } else {
            // Insert new allowance type
            const [insertId] = await trx(ehrTables.allowanceType).insert(allowanceData);
            allowanceTypeId = insertId;
        }

        // Handle benefit association
        await handleBenefitAssociation(trx, allowanceTypeId, args.benefitAssociation, isEditMode);
        

        // Workflow assignment is already handled in allowanceData preparation above

        // Update DataSetup Dashboard
        await updateDataSetupDashboard(trx);

        await trx.commit();

        return { allowanceTypeId };

    } catch (error) {
        await trx.rollback();
        throw error;
    }
}

// Set default values based on formId and business rules
function setDefaultValues(args) {
    const allowanceData = {};

    // Basic fields
    if (args.allowanceName) allowanceData.Allowance_Name = args.allowanceName.trim();
    if (args.nameInPayslip) {
        allowanceData.Name_In_Payslip = args.nameInPayslip.trim();
    } else if (args.allowanceName) {
        // Default to allowanceName if nameInPayslip not provided
        allowanceData.Name_In_Payslip = args.allowanceName.trim();
    }
    if (args.description){
        allowanceData.Description = args.description.trim();
    }
    else{
        allowanceData.Description = null;
    }

    // Salary Component ID field (for formula functionality)
    if (args.salaryComponentId) {
        allowanceData.Salary_Component_Id = args.salaryComponentId;
    }

    // Set defaults based on formId (381=Earnings, 382=Reimbursements, 383=Bonus)
    if (args.formId === 381) { // Earnings
        allowanceData.Allowance_Mode = 'Non Bonus';
        allowanceData.Period = 'Monthly';
        allowanceData.Is_Claim_From_Reimbursement = 'No';
        allowanceData.Reimbursement_Type = null;
        allowanceData.Unclaimed_LTA = null;
        allowanceData.LTA_Start_Year = null;
        allowanceData.End_Month = null;
        allowanceData.Number_Of_Claims = null;
    } else if (args.formId === 382) { // Reimbursements
        allowanceData.Allowance_Mode = 'Non Bonus';
        allowanceData.Period = 'Monthly';
        allowanceData.Formula_Based = 'No';
        allowanceData.Is_Basic_Pay = 'No';
        allowanceData.Is_Claim_From_Reimbursement = 'Yes';

        // Set reimbursement-specific values
        if (args.reimbursementType) {
            allowanceData.Reimbursement_Type = args.reimbursementType;

            if (args.reimbursementType === 'Leave Travel Allowance') {
                allowanceData.LTA_Start_Year = 2022;
                allowanceData.End_Month = 12;
                allowanceData.Number_Of_Claims = 2;
                allowanceData.Unclaimed_LTA = args.unclaimedLTA || null;
            } else {
                allowanceData.LTA_Start_Year = null;
                allowanceData.End_Month = 3;
                allowanceData.Number_Of_Claims = null;
                allowanceData.Unclaimed_LTA = null;
            }
        }
    } else if (args.formId === 383) { // Bonus
        allowanceData.Allowance_Mode = 'Bonus';
        allowanceData.Period = 'Monthly';
        allowanceData.Formula_Based = 'No';
        allowanceData.Is_Basic_Pay = 'No';
        allowanceData.Is_Claim_From_Reimbursement = 'No';
        allowanceData.Reimbursement_Type = null;
        allowanceData.Unclaimed_LTA = null;
        allowanceData.LTA_Start_Year = null;
        allowanceData.End_Month = null;
        allowanceData.Number_Of_Claims = null;
    }

    // Override with user inputs where applicable (but respect business rules)
    if (args.taxInclusion) allowanceData.Tax_Inclusion = args.taxInclusion;
    if (args.allowanceMode) allowanceData.Allowance_Mode = args.allowanceMode;
    if(args.formulaBased) allowanceData.Formula_Based = args.formulaBased;
    if(args.isBasicPay)  allowanceData.Is_Basic_Pay = args.isBasicPay;
    if (args.period) allowanceData.Period = args.period;
    if (args.isFlexiBenefitPlan) allowanceData.Is_Flexi_Benefit_Plan = args.isFlexiBenefitPlan;
    if (args.allowanceTypeStatus) allowanceData.AllowanceType_Status = args.allowanceTypeStatus;
    // Allowance_As_Is_Payment field
    if (args.asIsPayment) allowanceData.Allowance_As_Is_Payment = args.asIsPayment;
    if (args.perquisitesId)
    {
        allowanceData.Perquisites_Id = args.perquisitesId;
    }
    else{
        allowanceData.Perquisites_Id = null;
    } 

    // Handle Is_Basic_Pay - only allow 'No' for new records, don't change if already 'Yes' in updates
    if (args.isBasicPay) {
        allowanceData.Is_Basic_Pay = args.isBasicPay;
    }

    // Never allow Formula_Based to be changed from default 'No' for new records
    // For updates, this field should not be modified (handled in validation)

    // Flexi Benefit Plan specific fields
    if (args.isFlexiBenefitPlan === 'Yes') {
        allowanceData.Calculation_Type = 'Amount';
        allowanceData.Allowance_Amount =  0;
        allowanceData.Allowance_Percentage = null;
        allowanceData.FBP_Max_Declaration_Amount = args.fbpMaxDeclaration || 0;
        allowanceData.Restrict_Employee_FBP_Override = args.restrictEmployeeFbpOverride || 'No';
    }
    else{
        // Use calculationType if provided, otherwise fallback to allowanceType for backward compatibility
        const calcType = args.calculationType || args.allowanceType;
        allowanceData.Calculation_Type = calcType;

         if (calcType === 'Amount') {
            allowanceData.Allowance_Amount = args.amount || 0;
            allowanceData.Allowance_Percentage = null;
            allowanceData.Custom_Formula = null;
        } else if (calcType === 'Percentage') {
            allowanceData.Allowance_Percentage = args.percentage || 0;
            allowanceData.Allowance_Amount = null;
            allowanceData.Custom_Formula = null;
        } else if (calcType === 'Custom Formula') {
            allowanceData.Custom_Formula = args.customFormula;
            allowanceData.Allowance_Amount = null;
            allowanceData.Allowance_Percentage = null;
        }
        allowanceData.FBP_Max_Declaration_Amount = null;
        allowanceData.Restrict_Employee_FBP_Override = 'No';
    }

    // Workflow assignment for reimbursement types (workflowId comes from frontend)
    if (args.isClaimFromReimbursement === 'Yes' && args.workflowId) {
        allowanceData.Workflow_Id = args.workflowId;
    }

    // EPF contribution
    if (args.epfContribution) allowanceData.Consider_For_EPF_Contribution = args.epfContribution;

    // Set default status for new records
    if (!args.allowanceTypeId) {
        allowanceData.AllowanceType_Status = 'Active';
    }

    return allowanceData;
}

// Handle benefit association
async function handleBenefitAssociation(trx, allowanceTypeId, benefitAssociation, isEditMode) {
    try {
        // Delete existing associations if updating
        if (isEditMode) {
            await trx(ehrTables.allowanceBenefitAssociation)
                .where('Allowance_Type_Id', allowanceTypeId)
                .del();
        }

        if (!benefitAssociation || !Array.isArray(benefitAssociation)) {
            return;
        }
        // Insert new associations
        const associations = benefitAssociation.map(formId => ({
            Allowance_Type_Id: allowanceTypeId,
            Form_Id: formId
        }));

        if (associations.length > 0) {
            await trx(ehrTables.allowanceBenefitAssociation).insert(associations);
        }
    } catch (error) {
        throw error;
    }
}

// Workflow assignment is now handled directly in main processing using workflowId from frontend

// Update DataSetup Dashboard
async function updateDataSetupDashboard(trx) {
    try {
        // Update the status to indicate data has been modified
        await trx(ehrTables.dataSetupDashboard)
            .where('Form_Id', 109)
            .update({
                Status: 'Completed'
            });
    } catch (error) {
        // Don't throw error as this is not critical
    }
}

// Create custom component in salary_components table
async function createCustomComponent(trx, args) {
    try {
        const componentName = args.allowanceName.trim(); // Use allowanceName as componentName

        // Determine component type based on formId
        let componentType = 'EARNING'; // Default
        if (args.formId === 381) { // Earnings
            componentType = 'EARNING';
        } else if (args.formId === 382) { // Reimbursements
            componentType = 'REIMBURSEMENT';
        } else if (args.formId === 383) { // Bonus
            componentType = 'BONUS';
        }

        // Generate unique component code
        const componentCode = generateComponentCode(componentName, componentType);

        // Create component data
        const componentData = {
            Component_Name: componentName,
            Component_Code: componentCode,
            Component_Type: componentType,
            Is_Custom_Components: true,
            Description: args.description || null
        };

        // Insert into salary_components table
        const [componentId] = await trx(ehrTables.salaryComponents).insert(componentData);

        console.log(`Created custom component: ${componentName} with ID: ${componentId}`);
        return componentId;

    } catch (error) {
        console.error('Error in createCustomComponent:', error);
        throw error;
    }
}

// Generate component code based on component name and type (following existing pattern)
function generateComponentCode(componentName, componentType) {
    // Convert to lowercase and replace spaces/special chars with underscores
    let baseCode = componentName
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '_')
        .replace(/_+/g, '_')
        .replace(/^_|_$/g, '');

    // Add type-specific suffix following existing patterns
    let suffix;
    switch (componentType) {
        case 'EARNING': suffix = '_amount'; break;
        case 'REIMBURSEMENT': suffix = '_reimbursement'; break;
        case 'BONUS': suffix = '_bonus'; break;
        case 'GROSS': suffix = '_gross'; break;
        case 'RETIRAL': suffix = '_retiral'; break;
        case 'CTC': suffix = '_ctc'; break;
        default: suffix = '_amount';
    }

    return `${baseCode}${suffix}`;
}

// Delete allowance type with comprehensive business logic checks
async function deleteAllowanceTypeWithChecks(dbConnection, allowanceTypeId, loggedInEmpId) {
    try {
        return await dbConnection.transaction(async (trx) => {
            // 1. Check if allowance type exists and get its details
            const allowanceType = await trx(ehrTables.allowanceType)
                .select('*')
                .where('Allowance_Type_Id', allowanceTypeId)
                .first();

            if (!allowanceType) {
                throw 'IVE0681'; // Allowance Type not found
            }

            // Collect validation errors instead of throwing immediately
            const validationError = {};

            // 2. Check if this is the last basic pay or fixed allowance type
            const allowanceTypeWithComponent = await trx(ehrTables.allowanceType + ' as AT')
                .select('SC.Component_Code')
                .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
                .where('AT.Allowance_Type_Id', allowanceTypeId)
                .first();

            if (allowanceTypeWithComponent && allowanceTypeWithComponent.Component_Code) {
                const componentCode = allowanceTypeWithComponent.Component_Code?.toLowerCase();

                // Check if this is basic pay or fixed allowance
                if (componentCode === 'basic_salary_amount' || componentCode === 'fixed_allowance_amount') {
                    // Count how many active allowance types have this component
                    const countWithSameComponent = await trx(ehrTables.allowanceType + ' as AT')
                        .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
                        .whereRaw('LOWER(SC.Component_Code) = ?', [componentCode])
                        .where('AT.AllowanceType_Status', 'Active')
                        .count('* as count')
                        .first();

                    // If this is the only one, prevent deletion
                    if (countWithSameComponent.count <= 1) {
                        if (componentCode === 'basic_salary_amount') {
                            validationError['IVE0682'] = commonLib.func.getError('', 'IVE0682').message1; // Cannot delete last basic pay
                        } else if (componentCode === 'fixed_allowance_amount') {
                            validationError['IVE0683'] = commonLib.func.getError('', 'IVE0683').message1; // Cannot delete last fixed allowance
                        }
                    }
                }
            }

            // 4. Check if allowance type is used in active salary templates
            const activeTemplateUsage = await trx(ehrTables.salaryTemplate + ' as ST')
                .select('ST.Template_Id', 'ST.Template_Name')
                .innerJoin(ehrTables.templateAllowanceComponents + ' as TAC', 'TAC.Template_Id', 'ST.Template_Id')
                .where('ST.Template_Status', 'Active')
                .where('TAC.Allowance_Type_Id', allowanceTypeId)
                .first();

            // 4. Check if allowance type is used in active salary templates
            if (activeTemplateUsage) {
                validationError['IVE0684'] = commonLib.func.getError('', 'IVE0684').message1;
            }

            // 5. Check if allowance type is used in salary payslips (matching original PHP logic)
            const payslipUsage = await trx(ehrTables.allowanceType + ' as A')
                .innerJoin(ehrTables.salaryIncentive + ' as I', 'A.Allowance_Type_Id', 'I.Description')
                .where('I.Incentive_Name', 'like', 'Allowance')
                .where('A.Allowance_Type_Id', allowanceTypeId)
                .count('A.Allowance_Type_Id as count')
                .first();
            if (payslipUsage.count > 0) {
                validationError['IVE0685'] = commonLib.func.getError('', 'IVE0685').message1;
            }
            // 7. Check if allowance type is associated with tax section allowance mapping
            const taxSectionMapping = await trx('tax_section_allowance_mapping')
                .where('Allowance_Type_Id', allowanceTypeId)
                .count('* as count')
                .first();

            if (taxSectionMapping.count > 0) {
                validationError['IVE0687'] = commonLib.func.getError('', 'IVE0687').message1;
            }

            // 8. Check if allowance type is used in employee_salary_allowance
            const employeeSalaryUsage = await trx(ehrTables.employeeSalaryAllowance)
                .where('Allowance_Type_Id', allowanceTypeId)
                .count('* as count')
                .first();

            if (employeeSalaryUsage.count > 0) {
                validationError['IVE0688'] = commonLib.func.getError('', 'IVE0688').message1;
            }

            // Check if there are any validation errors before proceeding
            if (Object.keys(validationError).length > 0) {
                // Return the specific validation errors instead of throwing generic IVE0000
                const firstErrorCode = Object.keys(validationError)[0];
                throw firstErrorCode;
            }

            // 8. All checks passed - proceed with deletion
            // Delete from allowance_type_benefit_association first (foreign key constraint)
            await trx(ehrTables.allowanceBenefitAssociation)
                .where('Allowance_Type_Id', allowanceTypeId)
                .del();

            // Delete the main allowance type record
            const deletedCount = await trx(ehrTables.allowanceType)
                .where('Allowance_Type_Id', allowanceTypeId)
                .del();

            if (deletedCount === 0) {
                throw new Error('Failed to delete allowance type record');
            }

            // Update DataSetup Dashboard (following the pattern from add/update)
            await updateDataSetupDashboard(trx);

            return {
                errorCode: '',
                message: 'Allowance type deleted successfully.',
                success: true,
                allowanceTypeId: allowanceTypeId
            };
        });
    } catch (error) {
        throw error;
    }
}

module.exports.resolvers = resolvers;
