// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// Organization database connection
const knex = require('knex');
// require table alias
const {ehrTables} = require('../common/tablealias');
// require common constant files
const constants = require('../common/appconstants');

// variable declarations
let errResult = {};
let organizationDbConnection = '';
let checkRights=false;

// resolver definition
const resolvers = {
    Query: {
        // function to get salary configuration details
        retrieveSalaryConfiguration: async (parent, args, context, info) => {
            try{
                // get the organization data base connection
                organizationDbConnection = knex(context.connection.OrganizationDb);
                let loggedInEmpId=context.logInEmpId;
                // Check whether employee has view access rights for employee details form
                checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loggedInEmpId,constants.formName.employeeDetails,constants.roles.roleView);
                                    
                if (checkRights === true) {
                    let pfExist = await checkDeductionExist(organizationDbConnection, args.employeeId, constants.deductionName.providentFund);
                    let ptExist = await checkDeductionExist(organizationDbConnection, args.employeeId, constants.deductionName.professionalTax);
                    if(pfExist !== '' && ptExist !== ''){
                        let retiralsDetails = {
                            "providentFundExist" : pfExist,
                            "professionalTaxExist" : ptExist
                        };
                        return(
                            organizationDbConnection
                            .transaction(function(trx){
                                return (
                                    // Get salary configuration details from employee_salary_configuration table
                                    organizationDbConnection
                                    .select('ESC.Eligible_For_Overtime','ESC.Eligible_For_Pf','ESC.Eligible_For_Pension','ESC.Exempt_EDLI','ESC.UAN','ESC.Pf_PolicyNo','ESC.Eligible_For_ESI','ESC.ESI_Number','ESC.Eligible_For_Vpf','ESC.Vpf_Type','ESC.Vpf_Employee_Share','ESC.Vpf_Employee_Share_Amount',
                                    'ESC.Eligible_For_Insurance','ESC.Eligible_For_Nps','ESC.Nps_Number','ESC.Eligible_For_Gratuity','ESC.Eligible_For_PT','ESC.Bond_Recovery_Applicable','ESC.Minimum_Months_To_Be_Served', 'ESC.Bond_Value', 'ESC.Added_On','ESC.Updated_On',
                                    organizationDbConnection.raw("CONCAT_WS(' ',P1.Emp_First_Name,P1.Emp_Middle_Name, P1.Emp_Last_Name) as Added_By"),
                                    organizationDbConnection.raw("CONCAT_WS(' ',P2.Emp_First_Name,P2.Emp_Middle_Name, P2.Emp_Last_Name) as Updated_By"))
                                    .from(ehrTables.employeeSalaryConfiguration+' as ESC')
                                    .leftJoin(ehrTables.empPersonalInfo+' as P1', 'P1.Employee_Id', 'ESC.Added_By')
                                    .leftJoin(ehrTables.empPersonalInfo+' as P2', 'P2.Employee_Id', 'ESC.Updated_By')                    
                                    .where('ESC.Employee_Id',args.employeeId)
                                    .transacting(trx)
                                    .then(async (salaryDetails) => {
                                        return { errorCode: '', message: 'Salary configuration retrieved successfully.' , salaryConfigurationDetails:salaryDetails, retiralsDetails:retiralsDetails};
                                    })
                                );
                            })
                            .then(function (result) {
                                return result;
                            })
                            //catch db-connectivity errors
                            .catch(function (catchError) {
                                console.log('Error in retrieveSalaryConfiguration function .catch() block', catchError);
                                errResult = commonLib.func.getError(catchError, 'PST0103');
                                throw new ApolloError(errResult.message,errResult.code)
                            })
                            /**close db connection */
                            .finally(() => {
                                organizationDbConnection.destroy();
                            })
                        ); 
                    } else {
                        errResult = commonLib.func.getError('', 'PST0018');
                        throw new ApolloError(errResult.message,errResult.code)
                    }
                }
                else if (checkRights === false) {
                    throw '_DB0100';
                } else {
                    // throw error
                    throw (checkRights);
                }        
            }
            catch (mainCatchError){
                console.log('Error in retrieveSalaryConfiguration function main catch block',mainCatchError);
                // destroy database connection
                (organizationDbConnection)?organizationDbConnection.destroy():'';
                errResult = commonLib.func.getError(mainCatchError, 'PST0010');
                throw new ApolloError(errResult.message,errResult.code)
            }
        }
    }
};

/** Function to get the salary deduction details */
async function checkDeductionExist(organizationDbConnection, employeeId, deductionName){
    try{
        /** get employee salary type */
        let salaryType = await commonLib.payroll.getEmployeeSalaryType(organizationDbConnection,employeeId);
        let subQuery;
        if(salaryType && salaryType.toLowerCase() === 'monthly'){
            subQuery=organizationDbConnection(ehrTables.salaryPayslip +" as SP")
                .select(organizationDbConnection.raw("SP.Payslip_Id"))
                .leftJoin(ehrTables.salaryDeduction+' as SD', 'SP.Payslip_Id', 'SD.Payslip_Id')
                .where('SD.Deduction_Name ', deductionName)
                .where('SP.Employee_Id', employeeId)
        }
        else if(salaryType && salaryType.toLowerCase() === 'hourly'){
            subQuery=organizationDbConnection(ehrTables.hourlywagesPayslip +" as HP")
                .select(organizationDbConnection.raw("HP.Payslip_Id, HP.Employee_Id"))
                .leftJoin(ehrTables.hourlywageDeduction+' as HD', 'HP.Payslip_Id', 'HD.Payslip_Id')
                .where('HD.Deduction_Name ', deductionName)
                .where('HP.Employee_Id', employeeId)
        } else {
            /** if the salary type is not configured for the employee we will get '' value. 
             * In this case, deduction does not exist not the employee */
            return 0;
        }
        return(
          subQuery.then(getPayslip=>{
            if(getPayslip.length>0){
              return 1;
            }
            else{
              return 0;
            }
          }).catch(getError=>{
            console.log('Error in checkDeductionExist function .catch block.',getError);
            return '';
          })
        );  
      }
      catch(catchError){
          console.log('Error in checkDeductionExist function main catch block',catchError);
          return '';
      }
}

exports.resolvers = resolvers;
