const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const moment = require('moment-timezone');
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../common/tablealias');
const { getRoundOffValue, getRoundOffSettings } = require('../../common/commonfunctions');
const { formId } = require('../../common/appconstants');
const {
    getInsuranceRoundOffSettings,
    getInsuranceSpecificRoundOffValue
} = require('../../common/insuranceRoundOffFunctions');

let roundOffSettings;
let insuranceRoundOffMap;
module.exports.calculateSalaryArrears = async (parent, args, context, info) => {
    let organizationDbConnection;

    try {
        organizationDbConnection = context.orgdb ? context.orgdb : knex(context.connection.OrganizationDb);

        // Validate required arguments
        if (!args.employeeId || !args.revisionId) {
            throw new Error('Employee ID and Revision ID are required');
        }

        // Step 1: Get salary details and arrears details in parallel
        const [salaryDetails, salaryArrears, roundOffSetting, insuranceRoundOff] = await Promise.all([
            getSalaryDetails(organizationDbConnection, args.employeeId),
            getRevisionDetails(organizationDbConnection, args.revisionId),
            getRoundOffSettings(organizationDbConnection),
            getInsuranceRoundOffSettings(organizationDbConnection)
        ]);
        roundOffSettings = roundOffSetting;
        insuranceRoundOffMap = insuranceRoundOff;

        // Validate retrieved data
        validateSalaryData(salaryArrears, salaryDetails);

        if (salaryArrears['Salary_Effective_Month']) {
            salaryArrears['Effective_Month'] = moment(salaryArrears['Salary_Effective_Month'], 'M,YYYY').format('YYYY-MM');
        } else if (salaryArrears['Effective_From']) {
            salaryArrears['Effective_Month'] = moment(salaryArrears['Effective_From'], 'YYYY-MM-DD').format('YYYY-MM');
        }
        // Use Salary_Effective_To for arrears calculation
        const effectiveToMonth = moment(salaryArrears.Salary_Effective_To, 'M,YYYY').format('YYYY-MM');

        // Step 2: Calculate month range and get payslips
        const monthList = getMonthsBetweenDates(
            salaryArrears.Effective_Month,
            effectiveToMonth
        );

        const { salaryPayslips, resignationDate } = await getSalaryPayslipWithResignation(
            organizationDbConnection,
            args.employeeId,
            monthList
        );

        if(!salaryPayslips?.length){
            throw new Error('No salary payslips found for the given employee ID and month list');
        }

        // Step 3: Calculate allowance and retiral differences in parallel
        const [allowanceDifferences, retiralDifferences] = await Promise.all([
            calculateAllowanceDifferences(
                organizationDbConnection,
                salaryDetails,
                salaryArrears
            ),
            calculateRetiralDifferences(
                organizationDbConnection,
                salaryDetails,
                salaryArrears
            )
        ]);
        let allowanceArrearDetails = [];
        let retiralArrearDetails = [];

        retiralDifferences.forEach((item) => {
            let currentEmployeeShareAmount = 0;
            let previousEmployeeShareAmount = 0;
            let currentEmployerShareAmount = 0;
            let previousEmployerShareAmount = 0;

            for (let i = 0; i < salaryPayslips.length; i++) {
                if (salaryPayslips[i].Unpaid_Leave_Days === 0) {
                    currentEmployeeShareAmount += item.currentEmployeeShareAmount;
                    previousEmployeeShareAmount += item.previousEmployeeShareAmount;
                    currentEmployerShareAmount += item.currentEmployerShareAmount;
                    previousEmployerShareAmount += item.previousEmployerShareAmount;

                } else {
                    const currentEmployeeDeduction = (item.currentEmployeeShareAmount * salaryPayslips[i].Unpaid_Leave_Days) / salaryPayslips[i].Salary_Calc_Tot_Working_Days;
                    const previousEmployeeDeduction = (item.previousEmployeeShareAmount * salaryPayslips[i].Unpaid_Leave_Days) / salaryPayslips[i].Salary_Calc_Tot_Working_Days;
                    const currentEmployerDeduction = (item.currentEmployerShareAmount * salaryPayslips[i].Unpaid_Leave_Days) / salaryPayslips[i].Salary_Calc_Tot_Working_Days;
                    const previousEmployerDeduction = (item.previousEmployerShareAmount * salaryPayslips[i].Unpaid_Leave_Days) / salaryPayslips[i].Salary_Calc_Tot_Working_Days;
                    
                    currentEmployeeShareAmount += item.currentEmployeeShareAmount-currentEmployeeDeduction;
                    previousEmployeeShareAmount += item.previousEmployeeShareAmount-previousEmployeeDeduction;
                    currentEmployerShareAmount += item.currentEmployerShareAmount-currentEmployerDeduction;
                    previousEmployerShareAmount += item.previousEmployerShareAmount-previousEmployerDeduction;
                }
            }
            // Use insurance-specific round-off for insurance records (Retirals_Id > 0)
            let roundedCurrentEmployeeShareAmount, roundedPreviousEmployeeShareAmount;
            let roundedCurrentEmployerShareAmount, roundedPreviousEmployerShareAmount;

            if (item.Retirals_Id > 0) {
                // Insurance records - use insurance-specific round-off
                roundedCurrentEmployeeShareAmount = getInsuranceSpecificRoundOffValue(item.Retirals_Id, currentEmployeeShareAmount, insuranceRoundOffMap);
                roundedPreviousEmployeeShareAmount = getInsuranceSpecificRoundOffValue(item.Retirals_Id, previousEmployeeShareAmount, insuranceRoundOffMap);
                roundedCurrentEmployerShareAmount = getInsuranceSpecificRoundOffValue(item.Retirals_Id, currentEmployerShareAmount, insuranceRoundOffMap);
                roundedPreviousEmployerShareAmount = getInsuranceSpecificRoundOffValue(item.Retirals_Id, previousEmployerShareAmount, insuranceRoundOffMap);
            } else {
                // Non-insurance records - use general round-off
                roundedCurrentEmployeeShareAmount = getRoundOffValue(item.Form_Id, currentEmployeeShareAmount, roundOffSettings);
                roundedPreviousEmployeeShareAmount = getRoundOffValue(item.Form_Id, previousEmployeeShareAmount, roundOffSettings);
                roundedCurrentEmployerShareAmount = getRoundOffValue(item.Form_Id, currentEmployerShareAmount, roundOffSettings);
                roundedPreviousEmployerShareAmount = getRoundOffValue(item.Form_Id, previousEmployerShareAmount, roundOffSettings);
            }
            const roundedEmployeeAmount = roundedCurrentEmployeeShareAmount-roundedPreviousEmployeeShareAmount;
            const roundedEmployerAmount = roundedCurrentEmployerShareAmount-roundedPreviousEmployerShareAmount;
           
            if(roundedEmployeeAmount>0 || roundedEmployerAmount>0){
                retiralArrearDetails.push({
                Revision_Id: args.revisionId,
                Form_Id: item.Form_Id,
                Retirals_Id: item.Retirals_Id,
                Current_Employee_Share_Amount: roundedCurrentEmployeeShareAmount,
                Previous_Employee_Share_Amount: roundedPreviousEmployeeShareAmount,
                Employee_Share_Amount: roundedEmployeeAmount || 0,
                Current_Employer_Share_Amount: roundedCurrentEmployerShareAmount,
                Previous_Employer_Share_Amount: roundedPreviousEmployerShareAmount,
                Employer_Share_Amount: roundedEmployerAmount || 0,
            });
            }
        });

        allowanceDifferences.forEach((item) => {
            let totalCurrentAllowanceAmount = 0;
            let totalPreviousAllowanceAmount = 0;

            for (let i = 0; i < salaryPayslips.length; i++) {
                if (salaryPayslips[i].Unpaid_Leave_Days === 0) {
                    totalCurrentAllowanceAmount += item.currentAllowanceAmount;
                    totalPreviousAllowanceAmount += item.previousAllowanceAmount;
                } else {
                    const currentAllowanceDeduction = (item.currentAllowanceAmount * salaryPayslips[i].Unpaid_Leave_Days) / salaryPayslips[i].Salary_Calc_Tot_Working_Days;
                    const previousAllowanceDeduction = (item.previousAllowanceAmount * salaryPayslips[i].Unpaid_Leave_Days) / salaryPayslips[i].Salary_Calc_Tot_Working_Days;
                    totalCurrentAllowanceAmount += item.currentAllowanceAmount-currentAllowanceDeduction;
                    totalPreviousAllowanceAmount += item.previousAllowanceAmount-previousAllowanceDeduction;
                }
            }
            const roundedtotalCurrentAllowanceAmount = getRoundOffValue(formId.salary, totalCurrentAllowanceAmount, roundOffSettings);
            const roundedtotalPreviousAllowanceAmount = getRoundOffValue(formId.salary, totalPreviousAllowanceAmount, roundOffSettings);
            const roundedAllowanceAmount = roundedtotalCurrentAllowanceAmount-roundedtotalPreviousAllowanceAmount;

            allowanceArrearDetails.push({
                Revision_Id: args.revisionId,
                Component_Name: 'Allowances',
                Component_Id: item.Allowance_Type_Id,
                Current_Component_Amount: roundedtotalCurrentAllowanceAmount,
                Previous_Component_Amount: roundedtotalPreviousAllowanceAmount,
                Component_Amount: roundedAllowanceAmount,
            });
        })

        //Insert retiral arrear details and
        await organizationDbConnection.transaction(async trx => {
            await Promise.all([
                organizationDbConnection(ehrTables.payslipRetiralsRevisionDetails)
                    .insert(retiralArrearDetails)
                    .transacting(trx),
                organizationDbConnection(ehrTables.revisionPayslipDetails)
                    .insert(allowanceArrearDetails)
                    .transacting(trx)
            ]);
        });

        return {
            errorCode: '',
            message: 'Salary arrears calculated successfully',
        };

    } catch (err) {
        const errResult = commonLib.func.getError(err, 'SLR0002');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        if (organizationDbConnection &&  !context?.orgdb) {
            await organizationDbConnection.destroy();
        }
    }
};

/**
 * Retrieves the salary details for the given employee ID.
 * @param {Object} db - The database connection object.
 * @param {number} employeeId - The employee ID to retrieve salary details for.
 * @returns {Promise<Object>} - The salary details.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function getSalaryDetails(db, employeeId) {
    try {
        return db(ehrTables.employeeSalaryDetails + ' as ES')
            .select('ES.*', 'ESL.Allowance_Type_Id', 'ESL.Amount as Basic_Pay')
            .innerJoin(ehrTables.employeeSalaryAllowance + ' as ESL', 'ES.Employee_Id', 'ESL.Employee_Id')
            .innerJoin(ehrTables.allowanceType + ' as AT', 'ESL.Allowance_Type_Id', 'AT.Allowance_Type_Id')
            .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
            .where('SC.Component_Code', 'basic_salary_amount')
            .where('ES.Employee_Id', employeeId)
            .first();

    } catch (err) {
        throw err;
    }
}

/**
 * Retrieves the revision details for the given revision ID.
 * @param {Object} db - The database connection object.
 * @param {number} revisionId - The revision ID to retrieve details for.
 * @returns {Promise<Object>} - The revision details.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function getRevisionDetails(db, revisionId) {
    try {
        return db(ehrTables.salaryRevisionDetails + ' as SRD')
            .select('SRD.*', 'SL.Allowance_Type_Id', 'SL.Amount as Basic_Pay')
            .innerJoin(ehrTables.salaryRevisionAllowance + ' as SL', 'SRD.Revision_Id', 'SL.Revision_Id')
            .innerJoin(ehrTables.allowanceType + ' as AT', 'SL.Allowance_Type_Id', 'AT.Allowance_Type_Id')
            .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
            .where('SC.Component_Code', 'basic_salary_amount')
            .where('SRD.Revision_Id', revisionId)
            .first();
    } catch (err) {
        throw err;
    }
}

/**
 * Retrieves the salary payslips for the given employee ID and month list.
 * @param {Object} db - The database connection object.
 * @param {number} employeeId - The employee ID to retrieve salary payslips for.
 * @param {Array<string>} monthList - The list of month-year strings to retrieve salary payslips for.
 * @returns {Promise<Array>} - An array of salary payslips.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function getSalaryPayslipWithResignation(db, employeeId, monthList) {
    try {
        if (monthList.length === 0) return [];

        const [salaryPayslips, resignationData] = await Promise.all([
            db(ehrTables.salaryPayslip + ' as SP')
                .select('SP.*', 'EJ.Date_Of_Join')
                .innerJoin(ehrTables.empJob + ' as EJ', 'SP.Employee_Id', 'EJ.Employee_Id')
                .where('SP.Employee_Id', employeeId)
                .whereIn('SP.Salary_Month', monthList),

            db(ehrTables.empResignation)
                .select('Resignation_Date')
                .where('Approval_Status', 'Approved')
                .where('Employee_Id', employeeId)
                .first()
        ]);

        if (resignationData) {
            //Check if the resignation comes between the monthList
            const resignationMonth = moment(resignationData.Resignation_Date).format('M,YYYY');
            const resignationIndex = monthList.indexOf(resignationMonth);
            if (resignationIndex !== -1) {
                monthList.splice(resignationIndex, 1);
            }
        }

        return {
            salaryPayslips,
            resignationDate: resignationData?.Resignation_Date
        };
    } catch (err) {
        throw err;
    }
}

// Validation function
function validateSalaryData(salaryArrears, salaryDetails) {
    if (!salaryArrears?.Effective_From || !salaryArrears?.Salary_Effective_To) {
        throw new Error('Salary details or arrears not found or incomplete');
    }
    if (!salaryDetails?.Basic_Pay) {
        throw new Error('Salary details or arrears not found or incomplete');
    }
}

/**
 * Calculates the differences between the current and arrear allowances.
 * @param {Object} db - The database connection object.
 * @param {Object} salaryDetails - The salary details object.
 * @param {Object} salaryArrears - The salary arrears object.
 * @returns {Promise<Array>} - An array of allowance differences.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function calculateAllowanceDifferences(db, salaryDetails, salaryArrears) {
    try {
        const [currentAllowances, arrearAllowances] = await Promise.all([
            getAllowances(db, 'current', salaryDetails.Employee_Id),
            getAllowances(db, 'arrear', salaryArrears.Revision_Id)
        ]);

        return generateAllowanceDifferences(currentAllowances, arrearAllowances);
    } catch (err) {
        throw err;
    }
}

/**
 * Calculates the differences between the current and arrear retiral details.
 * @param {Object} db - The database connection object.
 * @param {Object} salaryDetails - The salary details object.
 * @param {Object} salaryArrears - The salary arrears object.
 * @returns {Promise<Array>} - An array of retiral differences.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function calculateRetiralDifferences(db, salaryDetails, salaryArrears) {
    try {
        const [currentRetirals, arrearRetirals] = await Promise.all([
            getCurrentRetirals(db, salaryDetails.Employee_Id),
            getArrearRetirals(db, salaryArrears.Revision_Id)
        ]);
        return generateRetiralDifference(arrearRetirals, currentRetirals);
    } catch (err) {
        throw err;
    }
}

// Get allowances with optimized query
async function getAllowances(db, type, id) {
    try {
        const baseQuery = db
            .select('ESL.Allowance_Type_Id', 'ESL.Amount', 'ESL.Percentage', 'ESL.Allowance_Type')
            .leftJoin(ehrTables.allowanceType + ' as AT', 'ESL.Allowance_Type_Id', 'AT.Allowance_Type_Id')
            .where('AT.Allowance_Mode', 'Non Bonus');

        if (type === 'current') {
            return baseQuery
                .from(ehrTables.employeeSalaryAllowance + ' as ESL')
                .where('ESL.Employee_Id', id);
        } else {
            return baseQuery
                .from(ehrTables.salaryRevisionAllowance + ' as ESL')
                .where('ESL.Revision_Id', id);
        }
    } catch (err) {
        throw err;
    }
}

/**
 * Retrieves the current retirals from the database for the given employee salary ID.
 * @param {Object} db - The database connection object.
 * @param {number} employeeSalaryId - The employee salary ID to retrieve current retirals for.
 * @returns {Promise<Array>} - An array of current retirals.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function getCurrentRetirals(db, employeeSalaryId) {
    try {
        // Get all retirals for the employee
        const allRetirals = await db(ehrTables.employeeSalaryRetirals)
            .select('*')
            .where('Employee_Id', employeeSalaryId);

        // Get configuration flags using Promise.all for better performance
        let pfIncludeFlag = true;
        let insuranceIncludeFlags = {}; // Map for insurance configurations by InsuranceType_Id
        let npsIncludeFlag = true;

        const configPromises = [
            db('provident_fund').select('Include_In_Arrears_Calculation').first().catch(() => null),
            db('insurance_configuration').select('InsuranceType_Id', 'Include_In_Arrears_Calculation').catch(() => []),
            db('nps_configuration').select('Include_In_Arrears_Calculation').first().catch(() => null)
        ];

        try {
            const [pfConfig, insuranceConfigs, npsConfig] = await Promise.all(configPromises);

            pfIncludeFlag = pfConfig?.Include_In_Arrears_Calculation === 'Yes';
            npsIncludeFlag = npsConfig?.Include_In_Arrears_Calculation === 'Yes';

            // Create a map for insurance configurations by InsuranceType_Id
            if (Array.isArray(insuranceConfigs)) {
                insuranceConfigs.forEach(config => {
                    insuranceIncludeFlags[config.InsuranceType_Id] = config.Include_In_Arrears_Calculation === 'Yes';
                });
            }
        } catch (configErr) {
            // Error checking configuration flags, using defaults
        }

        // Filter retirals based on Include_In_Arrears_Calculation flag
        const filteredRetirals = [];

        for (const retiral of allRetirals) {
            let includeRetiral = false;

            // Check based on Form_Id using pre-fetched flags
            switch (parseInt(retiral.Form_Id)) {
                case 52: // Provident Fund
                    includeRetiral = pfIncludeFlag;
                    break;

                case 58: // Insurance
                    // For insurance, check specific InsuranceType_Id (Retirals_Id)
                    includeRetiral = insuranceIncludeFlags[retiral.Retirals_Id] ?? true; // Default to true if not found
                    break;

                case 126: // NPS
                    includeRetiral = npsIncludeFlag;
                    break;
                case 110: // Gratuity
                    includeRetiral = false;
                    break;

                default:
                    // For other Form_Ids, include by default
                    includeRetiral = true;
                    break;
            }

            if (includeRetiral) {
                filteredRetirals.push(retiral);
            }
        }
        return filteredRetirals;
    } catch (err) {
        throw err;
    }
}

/**
 * Retrieves the arrear retirals from the database for the given revision ID.
 * Filters retirals based on Include_In_Arrears_Calculation flag in respective configuration tables.
 * @param {Object} db - The database connection object.
 * @param {number} revisionId - The revision ID to retrieve arrear retirals for.
 * @returns {Promise<Array>} - An array of arrear retirals.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function getArrearRetirals(db, revisionId) {
    try {
        const allRetirals = await db(ehrTables.salaryRevisionRetirals + ' as SRR')
            .select('SRR.*')
            .where('SRR.Revision_Id', revisionId);

        let pfIncludeFlag = true;
        let insuranceIncludeFlags = {}; // Map for insurance configurations by InsuranceType_Id
        let npsIncludeFlag = true;

        // Get configuration flags using Promise.all for better performance
        const configPromises = [
            db('provident_fund').select('Include_In_Arrears_Calculation').first().catch(() => null),
            db('insurance_configuration').select('InsuranceType_Id', 'Include_In_Arrears_Calculation').catch(() => []),
            db('nps_configuration').select('Include_In_Arrears_Calculation').first().catch(() => null)
        ];

        try {
            const [pfConfig, insuranceConfigs, npsConfig] = await Promise.all(configPromises);

            pfIncludeFlag = pfConfig?.Include_In_Arrears_Calculation === 'Yes';
            npsIncludeFlag = npsConfig?.Include_In_Arrears_Calculation === 'Yes';

            // Create a map for insurance configurations by InsuranceType_Id
            if (Array.isArray(insuranceConfigs)) {
                insuranceConfigs.forEach(config => {
                    insuranceIncludeFlags[config.InsuranceType_Id] = config.Include_In_Arrears_Calculation === 'Yes';
                });

            }

        } catch (configErr) {
            // Error checking configuration flags, using defaults
        }
        // Filter retirals based on Include_In_Arrears_Calculation flag
        const filteredRetirals = [];

        for (const retiral of allRetirals) {
            let includeRetiral = false;

            // Check based on Form_Id using pre-fetched flags
            switch (parseInt(retiral.Form_Id)) {
                case 52: // Provident Fund
                    includeRetiral = pfIncludeFlag;
                    break;

                case 58: // Insurance
                    // For insurance, check specific InsuranceType_Id (Retirals_Id)
                    includeRetiral = insuranceIncludeFlags[retiral.Retirals_Id] ?? true; // Default to true if not found
                    break;

                case 126: // NPS
                    includeRetiral = npsIncludeFlag;
                    break;
                case 110: // Gratuity
                    includeRetiral = false;
                    break;

                default:
                    // For other Form_Ids, include by default
                    includeRetiral = true;
                    break;
            }

            if (includeRetiral) {
                filteredRetirals.push(retiral);
            }
        }

        return filteredRetirals;
    } catch (err) {
        throw err;
    }
}

/**
 * Generates the difference between the arrear and existing allowance details.
 * @param {Array} currentDetails - Array of current allowance details.
 * @param {Array} arrearDetails - Array of arrear allowance details.
 * @returns {Array} - Array of allowance difference details.
 * @throws Will log an error and throw if an error occurs during processing.
 */
function generateAllowanceDifferences(currentDetails, arrearDetails) {
    try {
        const currentMap = new Map(currentDetails.map(a => [a.Allowance_Type_Id, a]));
        const arrearMap = new Map(arrearDetails.map(a => [a.Allowance_Type_Id, a]));
        const allIds = new Set([...currentMap.keys(), ...arrearMap.keys()]);

        return Array.from(allIds).map(id => {
            const current = currentMap.get(id);
            const arrear = arrearMap.get(id);

            let difference = 0;
            if (current && arrear) {
                difference = arrear.Amount - current.Amount;
            } else if (arrear) {
                difference = arrear.Amount;
            } else if (current) {
                difference = -current.Amount;
            }

            return {
                Allowance_Type_Id: id,
                Difference: getRoundOffValue(formId.allowances, difference, roundOffSettings),
                currentAllowanceAmount: arrear?.Amount || 0,
                previousAllowanceAmount: current?.Amount || 0
            };
        }).filter(item => Math.abs(item.Difference) > 0.01); // Filter out negligible differences
    } catch (err) {
        throw err;
    }
}

/**
 * Generates the difference between the arrear and existing retiral details.
 * @param {Array} arrear - Array of arrear retiral details.
 * @param {Array} existing - Array of existing retiral details.
 * @returns {Array} - Array of retiral difference details.
 * @throws Will log an error and throw if an error occurs during processing.
 */
function generateRetiralDifference(arrear = [], existing = []) {
    try {
        const existingMap = new Map(
            existing.map(item => [`${item.Form_Id}_${item.Retirals_Id}`, item])
        );

        const result = [];

        // Process arrear items
        for (const item of arrear) {
            const key = `${item.Form_Id}_${item.Retirals_Id}`;
            const existingItem = existingMap.get(key);

            result.push({
                    Form_Id: item.Form_Id,
                    Revision_Id: item.Revision_Id,
                    Retirals_Id: item.Retirals_Id,
                    currentEmployeeShareAmount: item.Employee_Share_Amount || 0,
                    previousEmployeeShareAmount: existingItem?.Employee_Share_Amount || 0,
                    currentEmployerShareAmount: item.Employer_Share_Amount || 0,
                    previousEmployerShareAmount: existingItem?.Employer_Share_Amount || 0
            });
            delete existingMap[key];
        }

        return result;
    } catch (err) {
        throw err;
    }
}

/**
 * Calculates retiral amounts based on the given retiral details and basic pay.
 * If the retiral type is 'percentage', calculates the amount as a percentage of basic pay.
 * @param {Array} retirals - Array of retiral objects.
 * @param {number} basicPay - The basic pay amount for calculation.
 * @returns {Array} - Array of retiral details with calculated amounts if applicable.
 * @throws Will log an error and throw if an error occurs during processing.
 */
function calculateRetiralDetails(retirals = [], basicPay = 0) {
    try {
        if (!Array.isArray(retirals) || basicPay <= 0) {
            return retirals;
        }

        return retirals.map(retiral => {
            if (retiral.Retirals_Type?.toLowerCase() === 'percentage') {
                const employerPercentage = parseFloat(retiral.Employer_Share_Percentage) || 0;
                const employeePercentage = parseFloat(retiral.Employee_Share_Percentage) || 0;

                return {
                    ...retiral,
                    Employer_Share_Amount: retiral.Employer_Share_Amount,
                    Employee_Share_Amount: retiral.Employee_Share_Amount
                };
            }
            return retiral;
        });
    } catch (err) {
        throw err;
    }
}

/**
 * Returns an array of month-year strings between the given start and end month-year.
 * The start and end month-year are inclusive.
 * The function throws an error if the input format is invalid or if the start date is after the end date.
 * @param {string} startMonthYear - The start month-year in the format 'month-year', e.g. '01-2022'.
 * @param {string} endMonthYear - The end month-year in the format 'month-year', e.g. '12-2022'.
 * @returns {Array<string>} - An array of month-year strings between the given start and end month-year.
 * @throws {Error} - If the input format is invalid or if the start date is after the end date.
 */
function getMonthsBetweenDates(startMonthYear, endMonthYear) {
    try {
        if (!startMonthYear || !endMonthYear) {
            throw new Error('Start and end month-year are required');
        }

        const parseMonthYear = (monthYear) => {
            const [year, month] = monthYear.split('-').map(num => parseInt(num.trim()));
            if (isNaN(month) || isNaN(year) || month < 1 || month > 12) {
                throw new Error(`Invalid month-year format: ${monthYear}`);
            }
            return { month, year };
        };

        const start = parseMonthYear(startMonthYear);
        const end = parseMonthYear(endMonthYear);

        const months = [];
        let current = { ...start };

        while (
            current.year < end.year ||
            (current.year === end.year && current.month <= end.month)
        ) {
            months.push(`${current.month},${current.year}`);

            current.month++;
            if (current.month > 12) {
                current.month = 1;
                current.year++;
            }
        }

        return months;
    } catch (err) {
        throw err;
    }
}